import { env } from '$env/dynamic/private';
import {
	updateEmailSchema as baseUpdateEmailSchema,
	updatePasswordSchema as baseUpdatePasswordSchema,
	deleteAccountSchema,
	updateProfileSchema
} from '$lib/schemas/settings';
import { addPasskeySchema, deletePasskeySchema } from '$lib/schemas/webauthn';
import { invalidateUserSessions, setSessionTokenCookie } from '$lib/server/auth';
import { checkEmailAvailability } from '$lib/server/auth/email';
import {
	createEmailVerificationAttempt,
	sendVerificationEmail
} from '$lib/server/auth/email-verification';
import { verifyPasswordHash, verifyPasswordStrength } from '$lib/server/auth/password';
import { getUserPasswordHash, updateUserEmail, updateUserPassword } from '$lib/server/auth/user';
import { startRegistration } from '$lib/server/auth/webauthn';
import { db } from '$lib/server/db';
import type { User } from '$lib/server/db/schema';
import { user_oauth_accounts, users, webauthn_credentials } from '$lib/server/db/schema';
import {
	verifyAuthenticationResponse,
	type VerifiedAuthenticationResponse
} from '@simplewebauthn/server';
import { error, fail, redirect } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	if (!locals.user) {
		redirect(302, '/login');
	}
	const isDeveloperEnvironment =
		env.VERCEL_ENV === 'development' ||
		env.VERCEL_ENV === 'preview' ||
		env.NODE_ENV === 'development';

	const [credentials, oauthAccounts] = await Promise.all([
		db.query.webauthn_credentials.findMany({
			where: eq(webauthn_credentials.user_id, locals.user.id),
			orderBy: (credentials, { desc }) => [desc(credentials.created_at)]
		}),
		db.query.user_oauth_accounts.findMany({
			where: eq(user_oauth_accounts.user_id, locals.user.id)
		})
	]);

	return {
		isDeveloperEnvironment,
		credentials,
		oauthAccounts,
		hasPassword: !!locals.user.hashed_password,
		hasPasskeys: credentials.length > 0
	};
};

const updateEmailSchema = baseUpdateEmailSchema.refine(
	async (data) => await checkEmailAvailability(data.email),
	{
		message: 'This email address is already in use.',
		path: ['email']
	}
);

const updatePasswordSchema = baseUpdatePasswordSchema.refine(
	async (data) => await verifyPasswordStrength(data.new_password),
	{
		message: 'New password is not strong enough. Please choose a more complex one.',
		path: ['new_password']
	}
);

const addPasswordSchema = baseUpdatePasswordSchema.omit({ current_password: true });

async function verifyReauthentication(
	request: Request,
	user: User
): Promise<{ success: boolean; error?: string }> {
	const formData = await request.formData();
	const authMethod = formData.get('authMethod');

	if (authMethod === 'password') {
		const password = formData.get('password');
		if (typeof password !== 'string' || !user.hashed_password) {
			return { success: false, error: 'Invalid authentication method.' };
		}
		const validPassword = await verifyPasswordHash(user.hashed_password, password);
		if (!validPassword) {
			return { success: false, error: 'Incorrect password.' };
		}
		return { success: true };
	}

	if (authMethod === 'passkey') {
		const authentication = formData.get('authentication');
		if (typeof authentication !== 'string' || !user.current_challenge) {
			return { success: false, error: 'Invalid authentication method.' };
		}

		try {
			const credential = await db.query.webauthn_credentials.findFirst({
				where: eq(webauthn_credentials.credential_id, JSON.parse(authentication).id)
			});

			if (!credential) {
				return { success: false, error: 'Credential not found.' };
			}

			const verification: VerifiedAuthenticationResponse = await verifyAuthenticationResponse({
				response: JSON.parse(authentication),
				expectedChallenge: user.current_challenge,
				expectedOrigin: new URL(request.url).origin,
				expectedRPID: env.RP_ID || 'localhost',
				credential: {
					id: credential.credential_id,
					publicKey: Buffer.from(credential.public_key, 'base64url'),
					counter: credential.counter,
					transports: credential.transports?.split(',') as AuthenticatorTransport[] | undefined
				}
			});

			if (!verification.verified) {
				return { success: false, error: 'Passkey verification failed.' };
			}

			// Update counter
			await db
				.update(webauthn_credentials)
				.set({ counter: verification.authenticationInfo.newCounter })
				.where(eq(webauthn_credentials.credential_id, credential.credential_id));

			return { success: true };
		} catch (e) {
			console.error('Passkey verification error:', e);
			return { success: false, error: 'Passkey verification failed.' };
		} finally {
			// Clear challenge
			await db.update(users).set({ current_challenge: null }).where(eq(users.id, user.id));
		}
	}

	return { success: false, error: 'No authentication method provided.' };
}

export const actions: Actions = {
	updateProfile: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) return fail(401, { profile: { errors: { root: ['Unauthorized'] } } });

		const formData = Object.fromEntries(await request.formData());
		const validation = updateProfileSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				profile: { errors: validation.error.flatten().fieldErrors }
			});
		}

		await db
			.update(users)
			.set({ first_name: validation.data.first_name, updated_at: new Date() })
			.where(eq(users.id, user.id));

		await invalidateUserSessions(user.id);

		return { profile: { success: true, message: 'Profile updated successfully.' } };
	},

	updateEmail: async ({ request, locals, url }) => {
		const user = locals.user;
		if (!user) return fail(401, { email: { errors: { root: ['Unauthorized'] } } });

		const formData = Object.fromEntries(await request.formData());
		const validation = await updateEmailSchema.safeParseAsync(formData);

		if (!validation.success) {
			return fail(400, { email: { errors: validation.error.flatten().fieldErrors } });
		}

		const { email, password } = validation.data;

		const currentPasswordHash = await getUserPasswordHash(user.id);
		if (!currentPasswordHash || !(await verifyPasswordHash(currentPasswordHash, password))) {
			return fail(400, {
				email: { errors: { password: ['Incorrect password.'] } }
			});
		}

		await updateUserEmail(user.id, email);
		await db.update(users).set({ email_verified: false }).where(eq(users.id, user.id));

		const attempt = await createEmailVerificationAttempt(user.id, email);
		if (attempt) {
			await sendVerificationEmail(
				email,
				attempt.plaintextUrlToken,
				attempt.plaintextOtp,
				url.origin
			);
		}

		await invalidateUserSessions(user.id);
		return redirect(302, '/login');
	},

	updatePassword: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { password: { errors: { root: ['Unauthorized'] } } });
		}
		const formData = Object.fromEntries(await request.formData());
		const validation = await baseUpdatePasswordSchema.safeParseAsync(formData);

		if (!validation.success) {
			return fail(400, { password: { errors: validation.error.flatten().fieldErrors } });
		}

		const { current_password, new_password } = validation.data;

		const currentPasswordHash = await getUserPasswordHash(user.id);
		if (
			!currentPasswordHash ||
			!(await verifyPasswordHash(currentPasswordHash, current_password))
		) {
			return fail(400, {
				password: { errors: { current_password: ['Incorrect current password.'] } }
			});
		}

		await verifyPasswordStrength(new_password);
		await updateUserPassword(user.id, new_password);
		await invalidateUserSessions(user.id);

		return { password: { success: true, message: 'Password updated. Please log in again.' } };
	},

	addPassword: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { password: { errors: { root: ['Unauthorized'] } } });
		}

		// Extra check to make sure user doesn't already have a password
		if (user.hashed_password) {
			return fail(400, { password: { errors: { root: ['Account already has a password.'] } } });
		}

		const formData = Object.fromEntries(await request.formData());
		const validation = await addPasswordSchema.safeParseAsync(formData);

		if (!validation.success) {
			return fail(400, { password: { errors: validation.error.flatten().fieldErrors } });
		}

		const { new_password } = validation.data;
		await verifyPasswordStrength(new_password);
		await updateUserPassword(user.id, new_password);

		// Invalidate other sessions for security
		await invalidateUserSessions(user.id);

		return { password: { success: true } };
	},

	deleteAccount: async (event) => {
		const { request, locals } = event;
		const user = locals.user;
		if (!user) return fail(401, { delete: { errors: { root: ['Unauthorized'] } } });

		const formData = Object.fromEntries(await request.formData());
		const validation = deleteAccountSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				delete: { errors: validation.error.flatten().fieldErrors }
			});
		}

		const { password } = validation.data;
		const currentPasswordHash = await getUserPasswordHash(user.id);
		if (!currentPasswordHash || !(await verifyPasswordHash(currentPasswordHash, password))) {
			return fail(400, {
				delete: { errors: { password: ['Incorrect password.'] } }
			});
		}

		// Proceed with account deletion
		await db.delete(users).where(eq(users.id, user.id));
		await invalidateUserSessions(user.id);
		setSessionTokenCookie(event, '', new Date(0));

		redirect(302, '/');
	},

	startRegistration: async ({ locals }) => {
		const user = locals.user;
		if (!user) return fail(401);

		try {
			const options = await startRegistration(user.id, user.email, user.first_name || '');
			return { registrationOptions: options };
		} catch (e: unknown) {
			const message = e instanceof Error ? e.message : 'Could not start registration';
			error(500, message);
		}
	},

	deleteCredential: async ({ locals, request }) => {
		const user = locals.user;
		if (!user) return fail(401, { deleteCredential: { errors: { root: ['Unauthorized'] } } });

		const formData = await request.formData();
		const validation = deletePasskeySchema.safeParse(Object.fromEntries(formData));

		if (!validation.success) {
			return fail(400, {
				deleteCredential: {
					data: Object.fromEntries(formData),
					errors: validation.error.flatten().fieldErrors
				}
			});
		}

		const { credentialId, password } = validation.data;

		// Verify password
		const dbUser = await db.query.users.findFirst({
			where: eq(users.id, user.id),
			columns: { hashed_password: true }
		});

		if (!dbUser?.hashed_password) {
			return fail(500, {
				deleteCredential: { errors: { root: ['Could not verify your identity.'] } }
			});
		}

		const validPassword = await verifyPasswordHash(dbUser.hashed_password, password);
		if (!validPassword) {
			return fail(401, {
				deleteCredential: {
					data: Object.fromEntries(formData),
					errors: { password: ['Incorrect password.'] }
				}
			});
		}

		// Check if credential belongs to user before deleting
		const credential = await db.query.webauthn_credentials.findFirst({
			where: and(
				eq(webauthn_credentials.credential_id, credentialId),
				eq(webauthn_credentials.user_id, user.id)
			)
		});

		if (!credential) {
			return fail(404, {
				deleteCredential: { errors: { root: ['Passkey not found.'] } }
			});
		}

		await db
			.delete(webauthn_credentials)
			.where(eq(webauthn_credentials.credential_id, credentialId));

		return { deleteCredential: { success: true } };
	},

	'add-passkey': async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { 'add-passkey': { errors: { root: ['Unauthorized'] } } });
		}
		const formData = Object.fromEntries(await request.formData());
		const validation = addPasskeySchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				'add-passkey': {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}
		return { 'add-passkey': { success: true, name: validation.data.name } };
	},

	disconnectOAuth: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { disconnectOAuth: { error: 'Unauthorized' } });
		}

		const formData = await request.formData();
		const provider = formData.get('provider');

		if (!provider || typeof provider !== 'string') {
			return fail(400, { disconnectOAuth: { error: 'Invalid provider specified.' } });
		}

		const hasPassword = !!user.hashed_password;
		const passkeys = await db.query.webauthn_credentials.findMany({
			where: eq(webauthn_credentials.user_id, user.id)
		});
		const otherOauths = await db.query.user_oauth_accounts.findMany({
			where: and(
				eq(user_oauth_accounts.user_id, user.id),
				eq(user_oauth_accounts.provider, provider)
			)
		});

		if (!hasPassword && passkeys.length === 0 && otherOauths.length <= 1) {
			return fail(400, {
				disconnectOAuth: {
					error:
						'You cannot disconnect your only sign-in method. Please add a password or passkey first.'
				}
			});
		}

		await db
			.delete(user_oauth_accounts)
			.where(
				and(eq(user_oauth_accounts.user_id, user.id), eq(user_oauth_accounts.provider, provider))
			);

		return { disconnectOAuth: { success: true } };
	},

	logoutAll: async (event) => {
		const { locals } = event;
		if (locals.user) {
			await invalidateUserSessions(locals.user.id);
		}
		setSessionTokenCookie(event, '', new Date(0));
		return { logoutAll: { success: true, message: 'Successfully logged out of all devices.' } };
	},

	reauthenticateAndDeleteCredential: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { deleteCredential: { errors: { root: ['Unauthorized'] } } });
		}

		const authCheck = await verifyReauthentication(request.clone(), user);
		if (!authCheck.success) {
			return fail(403, { deleteCredential: { errors: { root: [authCheck.error] } } });
		}

		const formData = await request.formData();
		const credentialId = formData.get('credentialId');

		if (!credentialId || typeof credentialId !== 'string') {
			return fail(400, { deleteCredential: { errors: { root: ['Invalid credential ID'] } } });
		}

		await db
			.delete(webauthn_credentials)
			.where(
				and(
					eq(webauthn_credentials.user_id, user.id),
					eq(webauthn_credentials.credential_id, credentialId)
				)
			);

		return { deleteCredential: { success: true } };
	},

	reauthenticateAndDeleteAccount: async ({ request, locals, cookies }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { delete: { errors: { root: ['Unauthorized'] } } });
		}

		const authCheck = await verifyReauthentication(request.clone(), user);
		if (!authCheck.success) {
			return fail(403, { delete: { errors: { root: [authCheck.error] } } });
		}

		await invalidateUserSessions(user.id);
		await db.delete(users).where(eq(users.id, user.id)); // Deletes user and cascades
		cookies.delete('session_token', { path: '/' });
		redirect(302, '/login');
	}
};
