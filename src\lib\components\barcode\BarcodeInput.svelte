<!--
  ALPHA FEATURE: Barcode Input Component
  A form input component with integrated barcode scanning capability

  Used by ALPHA FEATURE: Inventory Management for barcode entry in forms.
  Combines manual barcode entry with camera scanning functionality.

  Props:
    - value: the current barcode value
    - onScan: callback when a barcode is scanned
    - placeholder: input placeholder text
    - disabled: whether the input is disabled
    - name: input name attribute
    - id: input id attribute

  This component is part of the inventory management system that is currently in alpha status.
-->
<script lang="ts">
	import { toast } from '$lib/ui/toast';
	import BarcodeScanner from './BarcodeScanner.svelte';
	
	interface Props {
		value?: string;
		onScan?: (barcode: string) => void;
		placeholder?: string;
		disabled?: boolean;
		name?: string;
		id?: string;
		onInput?: (event: Event) => void;
		onBlur?: (event: Event) => void;
	}

	let {
		value = $bindable(''),
		onScan = () => {},
		placeholder = 'Enter or scan barcode',
		disabled = false,
		name,
		id,
		onInput = () => {},
		onBlur = () => {}
	}: Props = $props();

	import IconBarCode from '~icons/icon-park-outline/bar-code';
	import IconScan from '~icons/icon-park-outline/scan';

	let showScanner = $state(false);
	let inputElement: HTMLInputElement;

	function handleScanClick() {
		if (disabled) return;
		showScanner = true;
	}

	function handleBarcodeScanned(scannedCode: string) {
		console.log('BarcodeInput: Barcode scanned:', scannedCode);
		value = scannedCode;
		showScanner = false;
		onScan(scannedCode);

		// Note: We don't dispatch input/blur events here because the onScan callback
		// handles the barcode processing directly, and dispatching events would cause
		// duplicate processing in the form handler
	}

	function handleScannerClose() {
		showScanner = false;
	}

	function handleScannerError(error: string) {
		console.error('Barcode scanner error:', error);
		toast.error('Barcode scanning failed. Please try again or enter the barcode manually.');
		showScanner = false;
	}
</script>

<div class="relative group">
	<!-- Enhanced Input with scan button and modern styling -->
	<label class="input input-bordered input-lg flex items-center gap-3 transition-all duration-300 focus-within:input-info group-hover:border-info/50 group-hover:shadow-lg">
		<IconBarCode class="h-5 w-5 text-info/70 transition-colors duration-300 group-hover:text-info" />
		<input
			bind:this={inputElement}
			bind:value
			type="text"
			class="grow text-lg placeholder:text-base-content/50"
			{placeholder}
			{disabled}
			{name}
			{id}
			oninput={onInput}
			onblur={onBlur}
		/>
		<button
			type="button"
			class="btn btn-info btn-sm transition-all duration-300 hover:btn-info hover:scale-110 active:scale-95 shadow-md hover:shadow-lg"
			onclick={handleScanClick}
			{disabled}
			aria-label="Scan barcode with camera"
		>
			<IconScan class="h-4 w-4" />
			<span class="hidden sm:inline text-xs font-medium">Scan</span>
		</button>
	</label>

	<!-- Subtle hover effect overlay -->
	<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-info/5 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

	<!-- Scanner Modal -->
	{#if showScanner}
		<BarcodeScanner
			onScan={handleBarcodeScanned}
			onClose={handleScannerClose}
			onError={handleScannerError}
			isActive={true}
		/>
	{/if}
</div>
