<!--
This Svelte page serves as the main application dashboard.
It provides a simple welcome screen for authenticated users with basic information
and quick access to account settings.
-->
<script lang="ts">
	/**
	 * @component DashboardPage
	 * @description The main dashboard for the application. It provides an at-a-glance overview
	 * of key metrics and recent activity, serving as the user's central hub.
	 */
	import { formatDistanceToNow } from 'date-fns';
	import IconArrowRight from '~icons/icon-park-outline/arrow-right';
	import IconClient from '~icons/icon-park-outline/peoples';
	import IconPlus from '~icons/icon-park-outline/plus';
	import IconNote from '~icons/icon-park-outline/transaction';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	function formatTimeAgo(date: Date | string | null) {
		if (!date) return '';
		return `${formatDistanceToNow(new Date(date))} ago`;
	}
</script>

<div class="space-y-8">
	<!-- Header -->
	<div class="flex flex-col items-start justify-between gap-6 sm:flex-row">
		<div>
			<div class="mb-2 flex items-center gap-3">
				<div class="bg-primary/40 h-2 w-2 rounded-full"></div>
				<h1 class="text-base-content/90 text-2xl font-semibold">
					Welcome back, {data.user?.first_name || 'User'}
				</h1>
			</div>
			<p class="text-base-content/60 ml-5">Here's a snapshot of your workspace</p>
		</div>
		<div class="flex flex-col gap-3 sm:flex-row">
			<a
				href="/app/clients/new"
				class="btn btn-primary transition-all duration-200 hover:shadow-md"
			>
				<IconPlus class="h-4 w-4" />
				Add New Client
			</a>
		</div>
	</div>

	<!-- Stats Grid -->
	{#await data.stats}
		<div
			class="stats stats-vertical lg:stats-horizontal bg-base-100/80 border-base-300/30 rounded-xl border shadow-sm"
		>
			<div class="stat">
				<div class="stat-figure skeleton h-10 w-10 rounded-lg"></div>
				<div class="stat-title skeleton h-4 w-24"></div>
				<div class="stat-value skeleton h-8 w-16"></div>
				<div class="stat-desc skeleton h-3 w-20"></div>
			</div>
			<div class="stat">
				<div class="stat-figure skeleton h-10 w-10 rounded-lg"></div>
				<div class="stat-title skeleton h-4 w-24"></div>
				<div class="stat-value skeleton h-8 w-16"></div>
				<div class="stat-desc skeleton h-3 w-20"></div>
			</div>
		</div>
	{:then stats}
		<div
			class="stats stats-vertical lg:stats-horizontal bg-base-100/80 border-base-300/30 rounded-xl border shadow-sm"
		>
			<div class="stat py-6">
				<div class="stat-figure text-primary">
					<div class="bg-primary/5 border-primary/10 rounded-xl border p-3">
						<IconClient class="text-primary/70 h-6 w-6" />
					</div>
				</div>
				<div class="stat-title text-base-content/70">Total Clients</div>
				<div class="stat-value text-primary text-2xl font-semibold">{stats?.totalClients ?? 0}</div>
				{#if stats?.newClientsLast30Days}
					<div class="stat-desc text-base-content/60">+{stats.newClientsLast30Days} this month</div>
				{/if}
			</div>
			<div class="stat py-6">
				<div class="stat-figure text-secondary">
					<div class="bg-secondary/5 border-secondary/10 rounded-xl border p-3">
						<IconNote class="text-secondary/70 h-6 w-6" />
					</div>
				</div>
				<div class="stat-title text-base-content/70">Total Notes</div>
				<div class="stat-value text-secondary text-2xl font-semibold">{stats?.totalNotes ?? 0}</div>
				{#if stats?.newNotesLast30Days}
					<div class="stat-desc text-base-content/60">+{stats.newNotesLast30Days} this month</div>
				{/if}
			</div>
		</div>
	{/await}

	<!-- Recent Activity Section -->
	<div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
	<!-- Recently Active Clients -->
	<div class="card bg-base-100 border-base-300 border shadow-lg">
		<div class="card-body p-6">
			<div class="mb-4 flex items-center justify-between">
				<h2 class="card-title text-lg font-bold">Recently Active Clients</h2>
				<a href="/app/clients" class="btn btn-ghost btn-sm hover:btn-primary transition-colors">
					View All
					<IconArrowRight class="h-4 w-4" />
				</a>
			</div>
			<div>
				<ul class="space-y-3">
					{#await data.recentClients}
						{#each { length: 3 } as _}
							<li class="bg-base-50 border-base-300 rounded-lg border p-4">
								<div class="flex items-center space-x-4">
									<div class="skeleton h-12 w-12 shrink-0 rounded-full"></div>
									<div class="flex w-full flex-col gap-2">
										<div class="skeleton h-4 w-3/4"></div>
										<div class="skeleton h-3 w-1/2"></div>
									</div>
									<div class="skeleton h-3 w-1/4"></div>
								</div>
							</li>
						{/each}
					{:then recentClients}
						{#each recentClients || [] as client}
							<li class="hover:bg-base-100 border-base-300 rounded-lg border transition-colors">
								<a
									href="/app/clients/{client.id}"
									class="group flex w-full items-center space-x-4 p-4"
								>
									<div class="flex-shrink-0">
										{#if client.image_base64}
											<div class="avatar">
												<div class="h-12 w-12 rounded-full">
													<img
														src={client.image_base64}
														alt="{client.first_name} {client.last_name}"
														class="h-full w-full rounded-full object-cover"
													/>
												</div>
											</div>
										{:else}
											<div
												class="bg-primary/10 text-primary border-primary/20 flex h-12 w-12 items-center justify-center rounded-full border-2 text-lg font-bold"
											>
												{client.first_name?.[0]}{client.last_name?.[0]}
											</div>
										{/if}
									</div>
									<div class="min-w-0 flex-1">
										<p
											class="text-base-content group-hover:text-primary truncate text-base font-semibold transition-colors"
										>
											{client.first_name}
											{client.last_name}
										</p>
										<p class="text-base-content/60 truncate text-sm font-medium">{client.email}</p>
									</div>
									<div
										class="text-base-content/60 group-hover:text-primary inline-flex items-center text-xs font-semibold transition-colors"
									>
										Updated {formatTimeAgo(client.updated_at)}
									</div>
								</a>
							</li>
						{:else}
							<li class="p-8 bg-base-50 rounded-lg border border-base-300">
								<div
									class="flex w-full items-center justify-center text-center text-base-content/60 font-medium"
								>
									No clients found.
								</div>
							</li>
						{/each}
					{/await}
				</ul>
			</div>
		</div>
	</div>

	<!-- Recent Templates -->
	<div class="card bg-base-100 border-base-300 border shadow-lg">
		<div class="card-body p-6">
			<div class="mb-4 flex items-center justify-between">
				<h2 class="card-title text-lg font-bold">Recent Templates</h2>
				<a href="/app/templates" class="btn btn-ghost btn-sm hover:btn-primary transition-colors">
					View All
					<IconArrowRight class="h-4 w-4" />
				</a>
			</div>
			<div>
				<ul class="space-y-3">
					{#await data.recentTemplates}
						{#each { length: 3 } as _}
							<li class="bg-base-50 border-base-300 rounded-lg border p-4">
								<div class="flex w-full flex-col gap-2">
									<div class="skeleton h-4 w-3/4"></div>
									<div class="skeleton h-3 w-1/2"></div>
								</div>
								<div class="skeleton mt-2 h-3 w-1/4"></div>
							</li>
						{/each}
					{:then recentTemplates}
						{#each recentTemplates || [] as template}
							<li class="hover:bg-base-100 border-base-300 rounded-lg border transition-colors">
								<a
									href="/app/templates/{template.id}/preview"
									class="group flex w-full items-center space-x-4 p-4"
								>
									<div class="min-w-0 flex-1">
										<p
											class="group-hover:text-primary truncate text-base font-semibold transition-colors"
										>
											{template.name}
										</p>
										<p class="text-base-content/60 truncate text-sm font-medium">
											{template.user_id ? 'Custom' : 'System'} Template
										</p>
									</div>
									<div class="text-base-content/60 inline-flex items-center text-xs font-semibold">
										Created {formatTimeAgo(template.created_at)}
									</div>
								</a>
							</li>
						{:else}
							<li class="p-8 bg-base-50 rounded-lg border border-base-300">
								<div
									class="flex w-full items-center justify-center text-center text-base-content/60 font-medium"
								>
									No templates found.
								</div>
							</li>
						{/each}
					{/await}
				</ul>
			</div>
		</div>
	</div>
</div>
</div>

<!-- ALPHA FEATURE: Quick Restock Modal removed - feature moved to alpha status -->
