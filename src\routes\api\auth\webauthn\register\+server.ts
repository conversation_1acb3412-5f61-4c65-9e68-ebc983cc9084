import { startRegistration, verifyRegistration } from '$lib/server/auth/webauthn';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { json } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// Step 1: Begin registration process
export const GET: RequestHandler = async ({ locals }) => {
	// Check if user is already authenticated
	if (!locals.user) {
		return new Response('Unauthorized', { status: 401 });
	}

	const user = locals.user;

	// Get or create a display name
	const displayName = user.first_name || user.email.split('@')[0];

	try {
		// Start WebAuthn registration process
		const options = await startRegistration(user.id, user.email, displayName);

		return json(options);
	} catch (err) {
		console.error('Error starting WebAuthn registration:', err);
		const error = err as Error;
		return json({ error: error.message }, { status: 500 });
	}
};

// Step 2: Verify and complete registration
export const POST: RequestHandler = async ({ request, locals }) => {
	// Check if user is already authenticated
	if (!locals.user) {
		return new Response('Unauthorized', { status: 401 });
	}

	try {
		const { name, response } = await request.json();
		const userId = locals.user.id;

		// Verify the registration response
		const result = await verifyRegistration(userId, response, name);

		if (result) {
			// Update user to indicate they have WebAuthn credentials
			await db.update(users).set({ updated_at: new Date() }).where(eq(users.id, userId));

			return json({ success: true, message: 'WebAuthn credential registered successfully' });
		} else {
			return json(
				{ success: false, message: 'Failed to register WebAuthn credential' },
				{ status: 400 }
			);
		}
	} catch (err: unknown) {
		console.error('Error verifying WebAuthn registration:', err);
		// Type guard to check if it's a SvelteKit HttpError
		if (
			typeof err === 'object' &&
			err !== null &&
			'status' in err &&
			typeof err.status === 'number' &&
			'body' in err &&
			typeof err.body === 'object' &&
			err.body !== null &&
			'message' in err.body
		) {
			return json({ message: err.body.message }, { status: err.status });
		}
		// Handle generic errors
		const message = err instanceof Error ? err.message : 'An unknown server error occurred.';
		return json({ message }, { status: 500 });
	}
};
