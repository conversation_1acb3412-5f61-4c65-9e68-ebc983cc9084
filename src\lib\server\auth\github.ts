import { env } from '$env/dynamic/private';
import { GitHub } from 'arctic';

function createGitHubClient() {
	if (!env.GITHUB_CLIENT_ID || !env.GITHUB_CLIENT_SECRET || !env.GITHUB_REDIRECT_URI) {
		// If you are not using GitHub login, you can ignore this error.
		// Otherwise, please add the required environment variables.
		// You can create a GitHub OAuth app at: https://github.com/settings/developers
		console.warn(
			'GitHub OAuth environment variables are not set. GitHub login will not be available.'
		);
		return null;
	}

	return new GitHub(env.GITHUB_CLIENT_ID, env.GITHUB_CLIENT_SECRET, env.GITHUB_REDIRECT_URI);
}

export const github = createGitHubClient();
