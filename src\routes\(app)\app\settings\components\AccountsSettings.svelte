<!--
	@component
	Accounts Settings Module - Handles connected OAuth accounts management
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { confirm } from '$lib/ui/confirm';
	import { toast } from '$lib/ui/toast';
	import IconLink from '~icons/icon-park-outline/link-three';
	import IconBrandGithub from '~icons/icon-park-outline/github';
	import IconBrandGoogle from '~icons/icon-park-outline/google';
	import type { PageData, ActionData } from '../$types';

	interface Props {
		data: PageData;
		actionData?: ActionData;
	}

	let { data, actionData }: Props = $props();

	const brandIcons: Record<string, any> = {
		github: IconBrandGithub,
		google: IconBrandGoogle
	};

	// Handle action results
	$effect(() => {
		if (!actionData) return;

		// Disconnect OAuth
		if ('disconnectOAuth' in actionData && actionData.disconnectOAuth) {
			const result = actionData.disconnectOAuth;
			if (result && typeof result === 'object') {
				if ('success' in result && result.success) {
					toast.success('Account disconnected successfully.');
				} else if ('error' in result && result.error) {
					toast.error(result.error as string);
				}
			}
		}
	});
</script>

<div class="space-y-6">
	<div class="collapse collapse-arrow bg-base-200/50">
		<input type="checkbox" checked />
		<div class="collapse-title text-xl font-medium">
			<IconLink class="mr-2 h-5 w-5 inline" />
			Connected Accounts
		</div>
		<div class="collapse-content">
			<p class="text-base-content/70 mb-4">Manage third-party accounts linked for sign-in.</p>

			{#if (data as any).oauthAccounts && (data as any).oauthAccounts.length > 0}
				<ul class="divide-base-300 divide-y">
					{#each (data as any).oauthAccounts as account (account.id)}
						{@const Icon = brandIcons[account.provider]}
						<li class="flex items-center justify-between py-4">
							<div class="flex items-center gap-4">
								<Icon class="h-8 w-8" />
								<div>
									<p class="font-bold capitalize">{account.provider}</p>
									<p class="text-base-content/70 text-sm">
										Connected as {account.email}
									</p>
								</div>
							</div>
							<form
								action="?/disconnectOAuth"
								method="POST"
								use:enhance
								onsubmit={async (e) => {
									const confirmed = await confirm({
										title: `Disconnect ${account.provider}?`,
										message:
											'Are you sure you want to disconnect this account? You will no longer be able to sign in with it.',
										confirmText: 'Disconnect',
										confirmClass: 'btn-error'
									});
									if (!confirmed) {
										e.preventDefault();
									}
								}}
							>
								<input type="hidden" name="provider" value={account.provider} />
								<button type="submit" class="btn btn-error btn-sm">Disconnect</button>
							</form>
						</li>
					{/each}
				</ul>
			{:else}
				<p class="text-base-content/70 py-4 text-center">
					You have not connected any third-party accounts.
				</p>
			{/if}
		</div>
	</div>
</div>
