// ALPHA FEATURE: Shopping List Management - Page Server Load and Actions
// This server file provided comprehensive shopping list functionality including:
// - Loading shopping list items with inventory details and cost calculations
// - Adding items from inventory to shopping list
// - Updating quantities and toggling purchase status
// - Bulk completion workflows with inventory updates
// - Reorder suggestions based on inventory thresholds
// Part of the shopping list system that is currently in alpha status.

import { addShoppingListItemSchema, togglePurchasedSchema, updateShoppingListItemSchema } from '$lib/schemas/shoppingList';
import { db } from '$lib/server/db';
import { inventory_items, shopping_list_items } from '$lib/server/db/schema';
import { error, fail } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	const user = locals.user;
	if (!user) {
		error(401, 'Unauthorized');
	}

	// Get shopping list items with inventory data - optimized query
	const shoppingListItems = await db
		.select({
			id: shopping_list_items.id,
			quantity: shopping_list_items.quantity,
			purchased: shopping_list_items.purchased,
			created_at: shopping_list_items.created_at,
			inventory_item: {
				id: inventory_items.id,
				name: inventory_items.name,
				description: inventory_items.description,
				sku: inventory_items.sku,
				price_in_cents: inventory_items.price_in_cents,
				cost_in_cents: inventory_items.cost_in_cents,
				image_base64: inventory_items.image_base64
			}
		})
		.from(shopping_list_items)
		.innerJoin(inventory_items, eq(shopping_list_items.inventory_item_id, inventory_items.id))
		.where(
			and(
				eq(shopping_list_items.user_id, user.id),
				eq(inventory_items.user_id, user.id) // Ensure inventory item also belongs to user
			)
		)
		.orderBy(shopping_list_items.purchased, shopping_list_items.created_at);

	// Get available inventory items for adding to shopping list
	const availableInventoryItems = await db
		.select()
		.from(inventory_items)
		.where(eq(inventory_items.user_id, user.id))
		.orderBy(inventory_items.name);

	// Get items that need reordering (below threshold and not already in shopping list)
	const existingShoppingListItemIds = shoppingListItems.map(item => item.inventory_item.id);

	const reorderSuggestions = await db
		.select()
		.from(inventory_items)
		.where(
			and(
				eq(inventory_items.user_id, user.id),
				// Has reorder threshold set
				// quantity <= reorder_threshold (using SQL expression)
			)
		)
		.orderBy(inventory_items.name);

	// Filter out items already in shopping list and apply reorder logic
	const filteredReorderSuggestions = reorderSuggestions.filter(item =>
		item.reorder_threshold !== null &&
		item.quantity <= item.reorder_threshold &&
		!existingShoppingListItemIds.includes(item.id)
	);

	return {
		shoppingListItems,
		availableInventoryItems,
		reorderSuggestions: filteredReorderSuggestions
	};
};

export const actions: Actions = {
	add: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		const formData = await request.formData();
		const data = Object.fromEntries(formData);

		const result = addShoppingListItemSchema.safeParse(data);
		if (!result.success) {
			return fail(400, {
				message: 'Invalid data',
				errors: result.error.flatten().fieldErrors
			});
		}

		try {
			// Validate that the inventory item exists and belongs to the user
			const inventoryItem = await db
				.select({ id: inventory_items.id, name: inventory_items.name, quantity: inventory_items.quantity })
				.from(inventory_items)
				.where(
					and(
						eq(inventory_items.id, result.data.inventory_item_id),
						eq(inventory_items.user_id, user.id)
					)
				)
				.limit(1);

			if (inventoryItem.length === 0) {
				return fail(400, { message: 'Inventory item not found' });
			}

			// Check if item already exists in shopping list
			const existingItem = await db
				.select()
				.from(shopping_list_items)
				.where(
					and(
						eq(shopping_list_items.user_id, user.id),
						eq(shopping_list_items.inventory_item_id, result.data.inventory_item_id)
					)
				)
				.limit(1);

			if (existingItem.length > 0) {
				// Update quantity instead of creating duplicate
				await db
					.update(shopping_list_items)
					.set({
						quantity: existingItem[0].quantity + result.data.quantity,
						purchased: false // Reset purchased status when adding more
					})
					.where(eq(shopping_list_items.id, existingItem[0].id));
			} else {
				// Create new shopping list item
				await db.insert(shopping_list_items).values({
					user_id: user.id,
					inventory_item_id: result.data.inventory_item_id,
					quantity: result.data.quantity
				});
			}

			return { success: true };
		} catch (err) {
			console.error('Error adding shopping list item:', err);
			return fail(500, { message: 'Failed to add item to shopping list' });
		}
	},

	update: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		const formData = await request.formData();
		const data = Object.fromEntries(formData);

		const result = updateShoppingListItemSchema.safeParse(data);
		if (!result.success) {
			return fail(400, {
				message: 'Invalid data',
				errors: result.error.flatten().fieldErrors
			});
		}

		try {
			// Verify the shopping list item exists and belongs to the user
			const existingItem = await db
				.select({ id: shopping_list_items.id })
				.from(shopping_list_items)
				.where(
					and(
						eq(shopping_list_items.id, result.data.id),
						eq(shopping_list_items.user_id, user.id)
					)
				)
				.limit(1);

			if (existingItem.length === 0) {
				return fail(404, { message: 'Shopping list item not found' });
			}

			await db
				.update(shopping_list_items)
				.set({ quantity: result.data.quantity })
				.where(eq(shopping_list_items.id, result.data.id));

			return { success: true };
		} catch (err) {
			console.error('Error updating shopping list item:', err);
			return fail(500, { message: 'Failed to update shopping list item' });
		}
	},

	togglePurchased: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		const formData = await request.formData();
		const data = Object.fromEntries(formData);

		const result = togglePurchasedSchema.safeParse(data);
		if (!result.success) {
			return fail(400, {
				message: 'Invalid data',
				errors: result.error.flatten().fieldErrors
			});
		}

		try {
			await db
				.update(shopping_list_items)
				.set({ purchased: result.data.purchased })
				.where(
					and(
						eq(shopping_list_items.id, result.data.id),
						eq(shopping_list_items.user_id, user.id)
					)
				);

			return { success: true };
		} catch (err) {
			console.error('Error toggling purchased status:', err);
			return fail(500, { message: 'Failed to update item status' });
		}
	},

	remove: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		const formData = await request.formData();
		const id = formData.get('id') as string;

		if (!id) {
			return fail(400, { message: 'Missing item ID' });
		}

		try {
			await db
				.delete(shopping_list_items)
				.where(
					and(
						eq(shopping_list_items.id, id),
						eq(shopping_list_items.user_id, user.id)
					)
				);

			return { success: true };
		} catch (err) {
			console.error('Error removing shopping list item:', err);
			return fail(500, { message: 'Failed to remove item from shopping list' });
		}
	},

	completeAndUpdateInventory: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		const formData = await request.formData();
		const id = formData.get('id') as string;
		const updateInventory = formData.get('updateInventory') === 'true';

		if (!id) {
			return fail(400, { message: 'Missing item ID' });
		}

		try {
			// Get the shopping list item with inventory details
			const shoppingItem = await db
				.select({
					id: shopping_list_items.id,
					quantity: shopping_list_items.quantity,
					inventory_item_id: shopping_list_items.inventory_item_id,
					current_inventory_quantity: inventory_items.quantity
				})
				.from(shopping_list_items)
				.innerJoin(inventory_items, eq(shopping_list_items.inventory_item_id, inventory_items.id))
				.where(
					and(
						eq(shopping_list_items.id, id),
						eq(shopping_list_items.user_id, user.id)
					)
				)
				.limit(1);

			if (shoppingItem.length === 0) {
				return fail(404, { message: 'Shopping list item not found' });
			}

			const item = shoppingItem[0];

			// Mark as purchased
			await db
				.update(shopping_list_items)
				.set({ purchased: true })
				.where(eq(shopping_list_items.id, id));

			// Update inventory if requested
			if (updateInventory) {
				await db
					.update(inventory_items)
					.set({
						quantity: item.current_inventory_quantity + item.quantity,
						updated_at: new Date()
					})
					.where(eq(inventory_items.id, item.inventory_item_id));
			}

			return { success: true };
		} catch (err) {
			console.error('Error completing shopping list item:', err);
			return fail(500, { message: 'Failed to complete item' });
		}
	},

	bulkMarkPurchased: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		try {
			await db
				.update(shopping_list_items)
				.set({ purchased: true })
				.where(
					and(
						eq(shopping_list_items.user_id, user.id),
						eq(shopping_list_items.purchased, false)
					)
				);

			return { success: true };
		} catch (err) {
			console.error('Error marking all items as purchased:', err);
			return fail(500, { message: 'Failed to mark all items as purchased' });
		}
	},

	bulkClearPurchased: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		try {
			await db
				.delete(shopping_list_items)
				.where(
					and(
						eq(shopping_list_items.user_id, user.id),
						eq(shopping_list_items.purchased, true)
					)
				);

			return { success: true };
		} catch (err) {
			console.error('Error clearing purchased items:', err);
			return fail(500, { message: 'Failed to clear purchased items' });
		}
	},

	bulkCompleteAndUpdateInventory: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		try {
			// Get all unpurchased shopping list items with inventory details
			const unpurchasedItems = await db
				.select({
					id: shopping_list_items.id,
					quantity: shopping_list_items.quantity,
					inventory_item_id: shopping_list_items.inventory_item_id,
					current_inventory_quantity: inventory_items.quantity
				})
				.from(shopping_list_items)
				.innerJoin(inventory_items, eq(shopping_list_items.inventory_item_id, inventory_items.id))
				.where(
					and(
						eq(shopping_list_items.user_id, user.id),
						eq(shopping_list_items.purchased, false)
					)
				);

			if (unpurchasedItems.length === 0) {
				return { success: true, message: 'No items to complete' };
			}

			// Mark all as purchased
			await db
				.update(shopping_list_items)
				.set({ purchased: true })
				.where(
					and(
						eq(shopping_list_items.user_id, user.id),
						eq(shopping_list_items.purchased, false)
					)
				);

			// Update inventory quantities
			for (const item of unpurchasedItems) {
				await db
					.update(inventory_items)
					.set({
						quantity: item.current_inventory_quantity + item.quantity,
						updated_at: new Date()
					})
					.where(eq(inventory_items.id, item.inventory_item_id));
			}

			return { success: true, message: `Completed ${unpurchasedItems.length} items and updated inventory` };
		} catch (err) {
			console.error('Error bulk completing items:', err);
			return fail(500, { message: 'Failed to complete items and update inventory' });
		}
	},

	addReorderSuggestions: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { message: 'Unauthorized' });
		}

		try {
			// Get items that need reordering and aren't already in shopping list
			const existingShoppingListItems = await db
				.select({ inventory_item_id: shopping_list_items.inventory_item_id })
				.from(shopping_list_items)
				.where(eq(shopping_list_items.user_id, user.id));

			const existingItemIds = existingShoppingListItems.map(item => item.inventory_item_id);

			const reorderItems = await db
				.select()
				.from(inventory_items)
				.where(eq(inventory_items.user_id, user.id));

			const itemsToAdd = reorderItems.filter(item =>
				item.reorder_threshold !== null &&
				item.quantity <= item.reorder_threshold &&
				!existingItemIds.includes(item.id)
			);

			if (itemsToAdd.length === 0) {
				return { success: true, message: 'No items need reordering' };
			}

			// Add suggested items to shopping list
			const shoppingListInserts = itemsToAdd.map(item => ({
				user_id: user.id,
				inventory_item_id: item.id,
				quantity: Math.max(1, (item.reorder_threshold || 1) - item.quantity) // Suggest enough to reach threshold
			}));

			await db.insert(shopping_list_items).values(shoppingListInserts);

			return { success: true, message: `Added ${itemsToAdd.length} items to shopping list` };
		} catch (err) {
			console.error('Error adding reorder suggestions:', err);
			return fail(500, { message: 'Failed to add reorder suggestions' });
		}
	}
};