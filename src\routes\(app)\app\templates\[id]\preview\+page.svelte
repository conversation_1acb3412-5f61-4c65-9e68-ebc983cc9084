<script lang="ts">
	import { enhance } from '$app/forms';
	import DynamicFormRenderer from '$lib/components/templates/DynamicFormRenderer.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { copyTemplateSchema, updateTemplateStatusSchema } from '$lib/schemas/template';
	import { toast } from '$lib/ui/toast';
	import IconCopy from '~icons/icon-park-outline/copy';
	import IconArchive from '~icons/icon-park-outline/inbox-in';
	import type { ActionData, PageData } from './$types';

	type StatusAction = { success?: boolean; message?: string; errors?: unknown };
	type CopyAction = { message?: string; errors?: unknown };

	type PreviewActionData = {
		updateStatus?: StatusAction;
		copyTemplate?: CopyAction;
	};

	let { data, form: actionForm }: { data: PageData; form?: ActionData } = $props();
	let formData = $state({});

	const statusForm = createRuneForm(updateTemplateStatusSchema, {
		status: data.template.status === 'active' ? 'archived' : 'active'
	});

	const copyForm = createRuneForm(copyTemplateSchema, {
		templateId: data.template.id
	});

	$effect(() => {
		const form = actionForm as PreviewActionData | undefined;
		if (!form) return;

		if (form.updateStatus) {
			const { success, message } = form.updateStatus;
			if (success) {
				toast.success(message ?? 'Status updated!');
			} else if (message) {
				toast.error(message);
			}
		}

		if (form.copyTemplate) {
			const { message, errors } = form.copyTemplate;
			// On success, a redirect is thrown, so we only need to handle errors.
			if (errors || message) {
				toast.error(message ?? 'Failed to copy template.');
			}
		}
	});
</script>

<div class="bg-base-200 min-h-screen p-4 md:p-8">
	<div class="container mx-auto">
		<div class="mb-6">
			<div class="flex flex-wrap items-center justify-between gap-4">
				<div>
					<h1 class="text-3xl font-bold">Preview: {data.template.name}</h1>
					<p class="text-base-content/70">
						This is a preview of how your form will look. The inputs are interactive, but no data
						will be saved.
					</p>
				</div>
				{#if data.template.user_id && data.template.status !== 'draft'}
					<div class="flex items-center gap-2">
						<RuneForm form={statusForm}>
							{#snippet children(form)}
								<form
									method="POST"
									action="/app/templates/{data.template.id}?/updateStatus"
									use:enhance={() => {
										form.setSubmitting(true);
										return async ({ update }) => {
											await update({ reset: false });
											form.setSubmitting(false);
										};
									}}
									onsubmit={form.handleSubmit()}
								>
									{#if data.template.status === 'active'}
										<input type="hidden" name="status" value="archived" />
										<button class="btn btn-warning" type="submit" disabled={form.isSubmitting}>
											{#if form.isSubmitting}
												<span class="loading loading-spinner"></span>
											{:else}
												<IconArchive />
											{/if}
											Archive
										</button>
									{:else if data.template.status === 'archived'}
										<input type="hidden" name="status" value="active" />
										<button class="btn btn-secondary" type="submit" disabled={form.isSubmitting}>
											{#if form.isSubmitting}
												<span class="loading loading-spinner"></span>
											{:else}
												<IconArchive />
											{/if}
											Unarchive
										</button>
									{/if}
								</form>
							{/snippet}
						</RuneForm>

						<RuneForm form={copyForm}>
							{#snippet children(form)}
								<form
									method="POST"
									action="/app/templates?/copyTemplate"
									use:enhance={() => {
										form.setSubmitting(true);
										// on success, redirect will happen. on failure, we just stop submitting.
										return async ({ update }) => {
											await update({ reset: false });
											form.setSubmitting(false);
										};
									}}
									onsubmit={form.handleSubmit()}
								>
									<input type="hidden" name="templateId" value={data.template.id} />
									<button class="btn btn-outline" type="submit" disabled={form.isSubmitting}>
										{#if form.isSubmitting}
											<span class="loading loading-spinner"></span>
										{:else}
											<IconCopy />
										{/if}
										Copy to Draft
									</button>
								</form>
							{/snippet}
						</RuneForm>
					</div>
				{/if}
			</div>
		</div>

		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<form
					onsubmit={(e: SubmitEvent) => {
						e.preventDefault();
						alert('Form submission is disabled in preview mode.');
					}}
				>
					<DynamicFormRenderer template={data.template} bind:formData />
					<div class="card-actions mt-6 justify-end">
						<button type="submit" class="btn btn-primary">Submit (Preview)</button>
					</div>
				</form>
			</div>
		</div>
		{#if data.template.status === 'draft'}
			<div class="mt-4 text-center">
				<a href="/app/templates/{data.template.id}" class="link">Go back to editor</a>
			</div>
		{/if}
	</div>
</div>
