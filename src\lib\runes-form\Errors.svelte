<!--
    @component
    Errors
    @description
    A component to display validation errors for a specific form field.

    **Purpose and Reasoning:**

    1.  **Contextual Error Display:** It connects to the main form's context to access the reactive `errors` and `touched` state objects.
    2.  **Targeted Feedback:** By using the `name` prop, it subscribes to changes for a single field. This ensures that only relevant error messages are displayed.
    3.  **User-Friendly Interaction:** It's designed to only show errors if the field has been "touched" (i.e., the user has interacted with it). This prevents showing a screen full of errors before the user has had a chance to input anything, which is a better user experience.
-->
<script lang="ts">
	import { getContext } from 'svelte';
	import type { z } from 'zod/v4';
	import { RUNE_FORM_CONTEXT, type RuneFormContext } from './symbols';

	type T = $$Generic<z.ZodType>;

	let { name }: { name: string } = $props();

	const form = getContext<RuneFormContext<T>>(RUNE_FORM_CONTEXT);

	let errors: string[] | undefined = $derived(form.errors[name]);
	let touched: boolean | undefined = $derived(form.touched[name]);
</script>

{#if errors && (touched || name === 'root')}
	<ul>
		{#each errors as error}
			<li class="text-error">{error}</li>
		{/each}
	</ul>
{/if}
