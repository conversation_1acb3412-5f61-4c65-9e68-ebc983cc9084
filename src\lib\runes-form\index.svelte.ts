/*
 * This file exports the `createRuneForm` factory function, which is the core of the `runes-form` library.
 *
 * **Purpose and Reasoning:**
 *
 * 1.  **Centralized State Management:** `createRuneForm` encapsulates all the necessary state for a form—`values`, `errors`, `touched`, `isSubmitting`—using Svelte 5 Runes (`$state`). This makes the form state reactive and easy to manage.
 * 2.  **Type Safety with Zod:** It integrates tightly with Zod. By taking a Zod schema as its primary argument, it provides end-to-end type safety. The `z.infer<S>` type inference ensures that the `values` object and `onSubmit` callback parameters are fully typed according to the schema.
 * 3.  **Decoupled Logic:** It separates the form's state logic from its presentation. Components can consume the returned `RunesForm` object without needing to know the implementation details of validation or state updates.
 * 4.  **Client-Side Validation:** The `validate` method uses the provided Zod schema to perform client-side validation, providing immediate feedback to the user.
 * 5.  **Progressive Enhancement:** The `handleSubmit` method is designed to work seamlessly with SvelteKit's progressive enhancement. It first runs client-side validation and, if it fails, prevents the form submission. This provides a better UX than a full page reload for simple validation errors.
 * 6.  **Extensibility:** The returned object includes methods like `reset`, `setTouched`, `setSubmitting`, and now `setErrors`, providing a comprehensive API for controlling the form from parent components or Svelte actions.
 */
import { z, type ZodType } from 'zod/v4';

export type RunesForm<S extends ZodType> = {
	// State
	values: z.infer<S>;
	errors: Record<string, string[] | undefined>;
	touched: Record<string, boolean | undefined>;
	isSubmitting: boolean;
	isDirty: boolean;

	// Methods
	validate(): boolean;
	reset(): void;
	setTouched(name: string, value: boolean): void;
	setErrors(errors: Record<string, string[] | undefined>): void;
	setSubmitting(value: boolean): void;
	handleSubmit(onSubmit?: (values: z.infer<S>) => void | Promise<void>): (event: Event) => void;
};

export function createRuneForm<S extends ZodType>(
	schema: S,
	initialValues: z.infer<S>
): RunesForm<S> {
	let values = $state<z.infer<S>>(structuredClone(initialValues));
	let errors = $state<Record<string, string[] | undefined>>({});
	let touched = $state<Record<string, boolean | undefined>>({});
	let isSubmitting = $state(false);
	let isDirty = $state(false);

	function setTouched(name: string, value: boolean) {
		touched[name] = value;
	}

	function setErrors(newErrors: Record<string, string[] | undefined>) {
		errors = newErrors;
	}

	function setSubmitting(value: boolean) {
		isSubmitting = value;
	}

	function reset() {
		values = structuredClone(initialValues);
		errors = {};
		touched = {};
		isSubmitting = false;
		isDirty = false;
	}

	function validate() {
		const result = schema.safeParse(values);
		if (result.success) {
			errors = {};
			return true;
		}

		const newErrors: Record<string, string[] | undefined> = {};
		for (const issue of result.error.issues) {
			const path = issue.path.length === 0 ? 'root' : issue.path.join('.');
			if (!newErrors[path]) {
				newErrors[path] = [];
			}
			newErrors[path]?.push(issue.message);
		}
		errors = newErrors;
		return false;
	}

	$effect(() => {
		isDirty = JSON.stringify(values) !== JSON.stringify(initialValues);
	});

	function handleSubmit(onSubmit?: (values: z.infer<S>) => void | Promise<void>) {
		return (event: Event) => {
			if (!validate()) {
				event.preventDefault();
				return;
			}
			if (onSubmit) {
				onSubmit(values);
			}
		};
	}

	return {
		get values() {
			return values;
		},
		set values(newValues) {
			values = newValues;
		},
		get errors() {
			return errors;
		},
		get touched() {
			return touched;
		},
		get isSubmitting() {
			return isSubmitting;
		},
		get isDirty() {
			return isDirty;
		},

		validate,
		reset,
		setTouched,
		setErrors,
		setSubmitting,
		handleSubmit
	};
}
