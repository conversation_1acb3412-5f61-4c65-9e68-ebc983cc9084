import { createPool } from '@vercel/postgres';
import 'dotenv/config';
import { sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/vercel-postgres';
import * as schema from './schema';

const databaseUrl = process.env.POSTGRES_URL;

if (!databaseUrl) {
	throw new Error('POSTGRES_URL is not set. Please check your environment variables.');
}

const db = drizzle(createPool({ connectionString: databaseUrl }), { schema });

export async function resetDatabase() {
	console.log('Resetting database...');

	const vercelEnv = process.env.PUBLIC_VERCEL_ENV;
	const nodeEnv = process.env.NODE_ENV;

	if (vercelEnv === 'production' || nodeEnv === 'production') {
		console.error('ERROR: Attempted to run reset script in production. Aborting.');
		process.exit(1);
	}

	if (vercelEnv !== 'development' && vercelEnv !== 'preview' && nodeEnv !== 'development') {
		console.error(
			`ERROR: Reset script can only be run in 'development' or 'preview' environments. Current PUBLIC_VERCEL_ENV: ${vercelEnv}, NODE_ENV: ${nodeEnv}. Aborting.`
		);
		process.exit(1);
	}

	console.log(`Running in ${vercelEnv || nodeEnv} environment. Proceeding with database reset...`);

	const tableSchemas = Object.values(schema).filter(
		(value): value is typeof schema.users => 'getSQL' in value
	);

	if (tableSchemas.length === 0) {
		console.log('No tables found in schema. Nothing to reset.');
		process.exit(0);
	}

	// This is a bit of a hack, but it's the most reliable way to drop all tables
	// without having to manually manage the order due to foreign key constraints.
	// We just try to drop them all, and if it fails, we try again.
	// We do this a few times to make sure everything gets dropped.
	for (let i = 0; i < 5; i++) {
		for (const table of tableSchemas) {
			try {
				// Drizzle doesn't have a built-in drop all, so we use raw SQL.
				// We also don't have an easy way to get the table name from the schema object.
				// DrizzleKit uses the key name and converts to snake_case, so we do the same.
				console.log(`Dropping table: ${table._.name}`);
				await db.execute(sql.raw(`DROP TABLE IF EXISTS "${table._.name}" CASCADE;`));
			} catch (_error) {
				// Ignore errors, as some tables will fail to drop on the first pass
				// due to foreign key constraints.
			}
		}
	}

	console.log('Database reset complete!');
}

async function main() {
	try {
		await resetDatabase();
		process.exit(0);
	} catch (err) {
		console.error(err);
		process.exit(1);
	}
}

if (import.meta.url.startsWith('file:')) {
	const moduleUrl = new URL(import.meta.url);
	const executionUrl = new URL(process.argv[1], 'file:');
	if (moduleUrl.pathname === executionUrl.pathname) {
		main();
	}
}
