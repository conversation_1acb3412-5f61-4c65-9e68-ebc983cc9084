import { goto } from '$app/navigation';
import { toast } from '$lib/ui/toast';

/**
 * Composable for handling authentication flows
 * Separates business logic from UI components and integrates with runes-form
 */
export function useAuth() {
	let isLoading = $state(false);
	let error = $state<string | null>(null);

	/**
	 * Handle form submission for authentication actions
	 * Works with SvelteKit form actions and runes-form
	 */
	async function submitAuthForm(
		action: string,
		formData: Record<string, string | File>,
		onSuccess?: () => void
	): Promise<boolean> {
		isLoading = true;
		error = null;

		try {
			const form = new FormData();
			Object.entries(formData).forEach(([key, value]) => {
				if (typeof value === 'string') {
					form.append(key, value);
				} else {
					form.append(key, value);
				}
			});

			const response = await fetch(action, {
				method: 'POST',
				body: form
			});

			if (response.ok) {
				// Check if response is a redirect (successful auth)
				if (response.redirected || response.url.includes('/app')) {
					if (onSuccess) onSuccess();
					await goto('/app');
					return true;
				}
			}

			// Handle error response - try to extract error from response
			const result = await response.text();
			const errorMatch = result.match(/error[^>]*>([^<]+)</);
			error = errorMatch ? errorMatch[1] : 'Authentication failed';
			return false;

		} catch (err) {
			error = 'Network error. Please try again.';
			return false;
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Convenience method for login
	 */
	async function login(email: string, password: string): Promise<boolean> {
		return submitAuthForm('/login', { email, password });
	}

	/**
	 * Convenience method for signup
	 */
	async function signup(email: string, password: string, firstName: string): Promise<boolean> {
		return submitAuthForm('/login', {
			email,
			password,
			first_name: firstName,
			confirm_password: password
		});
	}

	/**
	 * Convenience method for magic link
	 */
	async function sendMagicLink(email: string): Promise<boolean> {
		const success = await submitAuthForm('/login', { email });
		if (success) {
			toast.success('Magic link sent! Check your email.');
		}
		return success;
	}

	/**
	 * Handle passkey authentication
	 */
	async function authenticateWithPasskey(email: string): Promise<boolean> {
		isLoading = true;
		error = null;

		try {
			// Get authentication options
			const optionsResponse = await fetch('/api/auth/webauthn/generate-authentication-options', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ email })
			});

			if (!optionsResponse.ok) {
				const err = await optionsResponse.json();
				error = err.error || 'Could not get passkey options.';
				return false;
			}

			const options = await optionsResponse.json();

			// Import WebAuthn client dynamically
			const { startAuthentication } = await import('@simplewebauthn/browser');
			const response = await startAuthentication({ optionsJSON: options });

			// Use the form submission method for consistency
			return await submitAuthForm('/login', {
				passkeyResponse: JSON.stringify(response)
			});

		} catch (err) {
			console.error('Passkey authentication error:', err);
			error = err instanceof Error ? err.message : 'Passkey authentication failed';
			return false;
		} finally {
			isLoading = false;
		}
	}

	/**
	 * Handle logout
	 */
	async function logout(): Promise<void> {
		try {
			await fetch('/logout', { method: 'POST' });
			await goto('/login');
		} catch (err) {
			console.error('Logout error:', err);
			// Force redirect even if logout request fails
			await goto('/login');
		}
	}

	/**
	 * Check authentication methods available for an email
	 */
	async function checkAuthMethods(email: string): Promise<{
		userExists: boolean;
		methods: string[];
	} | null> {
		try {
			const response = await fetch('/login', {
				method: 'POST',
				headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
				body: new URLSearchParams({ email })
			});

			if (response.ok) {
				const result = await response.json();
				return {
					userExists: result.userExists,
					methods: result.methods || []
				};
			}
		} catch (err) {
			console.error('Auth methods check error:', err);
		}
		return null;
	}

	/**
	 * Clear any existing errors
	 */
	function clearError(): void {
		error = null;
	}

	return {
		// State
		get isLoading() { return isLoading; },
		get error() { return error; },

		// Core method for form submission
		submitAuthForm,

		// Convenience methods
		login,
		signup,
		sendMagicLink,
		authenticateWithPasskey,
		logout,
		checkAuthMethods,
		clearError
	};
}
