import { defineConfig, devices } from '@playwright/test';

/**
 * Enhanced <PERSON>wright configuration for comprehensive authentication testing
 * Includes WebAuthn testing, accessibility checks, and performance monitoring
 */
export default defineConfig({
	testDir: 'e2e',

	// Run tests in files in parallel
	fullyParallel: true,

	// Fail the build on CI if you accidentally left test.only in the source code
	forbidOnly: !!process.env.CI,

	// Retry on CI only
	retries: process.env.CI ? 2 : 0,

	// Opt out of parallel tests on CI
	workers: process.env.CI ? 1 : undefined,

	// Reporter to use
	reporter: [
		['html'],
		['json', { outputFile: 'test-results/results.json' }],
		['junit', { outputFile: 'test-results/junit.xml' }]
	],

	// Shared settings for all the projects below
	use: {
		// Base URL to use in actions like `await page.goto('/')`
		baseURL: 'http://localhost:4173',

		// Collect trace when retrying the failed test
		trace: 'on-first-retry',

		// Capture screenshot on failure
		screenshot: 'only-on-failure',

		// Record video on failure
		video: 'on',

		// Global test timeout - increased for more robust testing
		actionTimeout: 15000,
		navigationTimeout: 30000,

		// Ignore HTTPS errors for local development
		ignoreHTTPSErrors: true,

		// Set user agent for consistent testing
		userAgent: 'Playwright-Test-Agent'
	},

	// Configure projects for major browsers
	projects: [
		{
			name: 'chromium',
			use: { ...devices['Desktop Chrome'] }
		}
		// Commented out other browsers until we have solid tests
		// {
		// 	name: 'firefox',
		// 	use: { ...devices['Desktop Firefox'] },
		// },
		// {
		// 	name: 'webkit',
		// 	use: { ...devices['Desktop Safari'] },
		// },

		// Test against mobile viewports
		// {
		// 	name: 'Mobile Chrome',
		// 	use: { ...devices['Pixel 5'] },
		// },
		// {
		// 	name: 'Mobile Safari',
		// 	use: { ...devices['iPhone 12'] },
		// },

		// Test against branded browsers
		// {
		// 	name: 'Microsoft Edge',
		// 	use: { ...devices['Desktop Edge'], channel: 'msedge' },
		// },
		// {
		// 	name: 'Google Chrome',
		// 	use: { ...devices['Desktop Chrome'], channel: 'chrome' },
		// },
	],

	// Run your local dev server before starting the tests
	webServer: {
		command: 'npm run build && npm run preview',
		port: 4173,
		reuseExistingServer: !process.env.CI,
		timeout: 120000
	},

	// Global setup and teardown
	// globalSetup: './e2e/global-setup.ts',
	// globalTeardown: './e2e/global-teardown.ts',

	// Test match patterns
	testMatch: ['**/*.spec.ts', '**/*.test.ts'],

	// Test ignore patterns
	testIgnore: ['**/node_modules/**', '**/dist/**', '**/.svelte-kit/**'],

	// Expect options
	expect: {
		// Maximum time expect() should wait for the condition to be met
		timeout: 5000,

		// Custom matchers
		toHaveScreenshot: { threshold: 0.2 },
		toMatchSnapshot: { threshold: 0.2 }
	},

	// Output directory for test artifacts
	outputDir: 'test-results/',

	// Maximum time one test can run for
	timeout: 30000,

	// Maximum time the whole test suite can run
	globalTimeout: 600000
});
