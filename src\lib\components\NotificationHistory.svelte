<!--
The NotificationHistory component is designed to provide users with a persistent, scrollable log of all toast notifications that have appeared in the application. It serves as a functional replacement for a traditional notification bell icon, ensuring that no important message is missed.

This modernized version uses the DaisyUI timeline component for a more intuitive and visually appealing chronological display.

**Core Functionality:**

1.  **History Subscription:** The component subscribes to the `toastStore.history` writable store. This store accumulates every notification, which are then displayed in a "newest first" order.

2.  **Infinite Scroll:** To efficiently manage potentially long lists of notifications, the component implements an "infinite scroll" pattern.
    - It initially displays a limited number of notifications (`visibleCount`).
    - An invisible `sentinel` element is placed at the bottom of the list.
    - The `IntersectionObserver` API monitors this sentinel. When it becomes visible, more notifications are loaded.

3.  **Dynamic & Animated Rendering:**
    - It uses the DaisyUI `timeline` component for a clean, modern layout.
    - <PERSON>velte's `fly` transition provides a subtle animation as new notifications are added to the list.
    - Notification types (`success`, `error`, `info`, `warning`, `loading`) are mapped to specific icons and colors for clear visual distinction.
    - A `formatTimeAgo` utility provides a user-friendly, relative timestamp for each notification.

**Lifecycle Management:**

-   The component uses Svelte 5 runes (`$effect`) for reactive lifecycle management, automatically handling subscriptions and IntersectionObserver cleanup to prevent memory leaks.
-->
<script lang="ts">
	import { TIMING } from '$lib/config';
	import { toastStore, type ToastMessage } from '$lib/stores/toastStore';
	import { fly } from 'svelte/transition';
	import IconAlertTriangle from '~icons/icon-park-outline/caution';
	import IconCheckCircle from '~icons/icon-park-outline/check-small';
	import IconCloseCircle from '~icons/icon-park-outline/close-one';
	import IconDelete from '~icons/icon-park-outline/delete';
	import IconInfoCircle from '~icons/icon-park-outline/info';
	import LoaderIcon from '~icons/icon-park-outline/loading-one';
	import IconBell from '~icons/icon-park-outline/remind';

	let allNotifications = $state<ToastMessage[]>([]);
	let visibleCount = $state(15);
	const increment = 10;
	let sentinel: HTMLDivElement | undefined = $state(undefined);

	// Subscribe to changes in the toast history store
	$effect(() => {
		const unsubscribe = toastStore.history.subscribe((history) => {
			allNotifications = history;
		});

		// Cleanup subscription when the component is destroyed
		return () => unsubscribe();
	});

	const visibleNotifications = $derived(allNotifications.slice(0, visibleCount));

	function loadMore() {
		if (visibleCount < allNotifications.length) {
			visibleCount += increment;
		}
	}

	// Infinite scroll implementation using Svelte 5 $effect
	$effect(() => {
		if (!sentinel) return;

		const observer = new IntersectionObserver(
			(entries) => {
				if (entries[0].isIntersecting) {
					loadMore();
				}
			},
			{ threshold: 1 }
		);

		observer.observe(sentinel);

		return () => {
			const currentSentinel = sentinel;
			if (currentSentinel) {
				observer.unobserve(currentSentinel);
			}
		};
	});

	const icons = {
		info: IconInfoCircle,
		success: IconCheckCircle,
		warning: IconAlertTriangle,
		error: IconCloseCircle,
		loading: LoaderIcon
	};

	const colors = {
		info: 'text-info',
		success: 'text-success',
		warning: 'text-warning',
		error: 'text-error',
		loading: 'text-info'
	};

	// A map of time units to their duration in seconds
	const timeUnits: { unit: Intl.RelativeTimeFormatUnit; seconds: number }[] = [
		{ unit: 'year', seconds: 31536000 },
		{ unit: 'month', seconds: 2592000 },
		{ unit: 'day', seconds: 86400 },
		{ unit: 'hour', seconds: 3600 },
		{ unit: 'minute', seconds: 60 }
	];

	// Create a single formatter instance for performance
	const rtf = new Intl.RelativeTimeFormat('en', { numeric: 'auto' });

	// Format the time ago
	function formatTimeAgo(date: Date): string {
		const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
		if (seconds < 5) return 'just now';

		for (const { unit, seconds: unitSeconds } of timeUnits) {
			const interval = seconds / unitSeconds;
			if (interval > 1) {
				return rtf.format(-Math.floor(interval), unit);
			}
		}
		return rtf.format(-seconds, 'second');
	}

	// Handle delete with animation
	function handleDelete(notificationId: number, event: MouseEvent) {
		const button = event.currentTarget as HTMLButtonElement;
		const icon = button.querySelector('.delete-icon') as HTMLElement;

		// Add click animation class
		if (icon) {
			icon.classList.add('delete-clicked');

			// Remove the class after animation completes
			setTimeout(() => {
				icon.classList.remove('delete-clicked');
			}, TIMING.ANIMATION_NORMAL);
		}

		// Slight delay to show the animation before removing
		setTimeout(() => {
			toastStore.removeFromHistory(notificationId);
		}, TIMING.SCANNER_PROCESSING_DELAY);
	}

	function handleClearAll() {
		toastStore.clearHistory();
	}
</script>

<div
	class="card bg-base-200 border-base-content/10 w-96 overflow-hidden border shadow-xl"
	role="region"
	aria-labelledby="notifications-heading"
>
	<h3
		id="notifications-heading"
		class="border-base-content/10 flex items-center justify-between border-b p-4 text-lg font-semibold"
	>
		<div class="flex items-center gap-2">
			<IconBell class="h-5 w-5 shrink-0" aria-hidden="true" />
			<span>Notifications</span>
		</div>
		<button
			class="btn btn-ghost btn-sm font-medium normal-case"
			onclick={handleClearAll}
			disabled={allNotifications.length === 0}
			aria-label="Clear all notifications"
		>
			Clear all
		</button>
	</h3>
	<div class="z-50 max-h-96 overflow-y-auto">
		{#if visibleNotifications.length === 0}
			<p class="text-base-content/70 p-4">No new notifications.</p>
		{:else}
			<div class="flex flex-col gap-1 p-2">
				{#each visibleNotifications as notification (notification.id)}
					<div
						transition:fly={{ y: 10, duration: 200 }}
						class="hover:bg-base-content/5 flex items-start gap-3 rounded-lg p-3 transition-colors"
						role="listitem"
					>
						<div class="mt-0.5 shrink-0">
							{#if notification.type === 'loading'}
								<span class="loading loading-spinner {colors[notification.type]}"></span>
							{:else}
								{@const Icon = icons[notification.type]}
								<Icon class="h-5 w-5 {colors[notification.type]}" aria-hidden="true" />
							{/if}
						</div>
						<div class="flex-1">
							<p class="text-sm">{notification.message}</p>
							<p class="text-base-content/60 text-xs">
								{formatTimeAgo(notification.timestamp)}
							</p>
						</div>
						<div
							class="shrink-0"
							onclick={(e) => e.stopPropagation()}
							onkeydown={(e) => e.stopPropagation()}
							role="presentation"
						>
							<button
								class="delete-button group hover:bg-error/10 focus:bg-error/10 focus:ring-error/20 relative rounded-full p-1 transition-all duration-200 ease-out focus:ring-2 focus:outline-none active:scale-95"
								onclick={(e) => handleDelete(notification.id, e)}
								aria-label="Delete notification"
								type="button"
							>
								<IconDelete
									class="delete-icon text-base-content/40 group-hover:text-error group-focus:text-error h-4 w-4 transition-all duration-200 ease-out group-hover:scale-110 group-focus:scale-110"
								/>
							</button>
						</div>
					</div>
				{/each}
			</div>
			{#if visibleNotifications.length < allNotifications.length}
				<div bind:this={sentinel} class="flex h-10 items-center justify-center">
					<span class="loading loading-spinner text-primary" role="status"
						>Loading more notifications...</span
					>
				</div>
			{/if}
		{/if}
	</div>
</div>

<style>
	/* Modern microinteractions for delete button */
	.delete-button {
		transform-origin: center;
	}

	/* Click animation with bounce effect */
	.delete-icon.delete-clicked {
		animation: deleteClick 0.2s ease-out;
	}

	@keyframes deleteClick {
		0% {
			transform: scale(1) rotate(0deg);
		}
		50% {
			transform: scale(0.85) rotate(-5deg);
		}
		100% {
			transform: scale(1.1) rotate(0deg);
		}
	}

	/* Subtle pulse animation on hover for additional feedback */
	.delete-button:hover {
		animation: pulseHover 2s ease-in-out infinite;
	}

	@keyframes pulseHover {
		0%,
		100% {
			background-color: rgba(239, 68, 68, 0.1);
		}
		50% {
			background-color: rgba(239, 68, 68, 0.15);
		}
	}

	/* Smooth transition for the icon stroke */
	.delete-icon {
		stroke-width: 1.5;
		transition: stroke-width 0.2s ease-out;
	}
</style>
