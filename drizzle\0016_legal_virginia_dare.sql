-- First, update any NULL cost_in_cents values to use price_in_cents as fallback
UPDATE "inventory_items" SET "cost_in_cents" = "price_in_cents" WHERE "cost_in_cents" IS NULL AND "price_in_cents" IS NOT NULL;--> statement-breakpoint
-- Set a default value of 0 for any remaining NULL cost_in_cents
UPDATE "inventory_items" SET "cost_in_cents" = 0 WHERE "cost_in_cents" IS NULL;--> statement-breakpoint
-- Now make the schema changes
ALTER TABLE "inventory_items" ALTER COLUMN "price_in_cents" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "inventory_items" ALTER COLUMN "price_in_cents" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "inventory_items" ALTER COLUMN "cost_in_cents" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "inventory_items" DROP COLUMN IF EXISTS "status";