import {
    createSession,
    generateSessionToken,
    setSessionTokenCookie
} from '$lib/server/auth';
import { validateMagicLinkToken } from '$lib/server/auth/email';
import { error, redirect, type RequestEvent } from '@sveltejs/kit';

export async function GET(event: RequestEvent) {
	const { url } = event;
	const token = url.searchParams.get('token');

	if (!token) {
		throw error(400, 'Invalid or missing magic link token.');
	}

	let userId: string | null = null;
	try {
		userId = await validateMagicLinkToken(token);
	} catch (e) {
		console.error('Magic link validation error:', e);
		throw error(500, 'An unexpected error occurred.');
	}

	if (!userId) {
		throw error(400, 'Invalid or expired magic link token.');
	}

	const sessionToken = generateSessionToken();
	const session = await createSession(sessionToken, userId);
	setSessionTokenCookie(event, sessionToken, session.expires_at);

	redirect(302, '/app');
}