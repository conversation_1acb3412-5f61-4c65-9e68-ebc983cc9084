<!--
  src/lib/components/ui/NotFound.svelte
  A reusable component to gracefully handle 'Not Found' errors for specific resources.
  It displays an informative message, provides a manual return button, and automatically
  redirects the user back to a safe list page after a short delay.
-->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { TIMING } from '$lib/config';
	import { onMount } from 'svelte';
	import IconArrowLeft from '~icons/icon-park-outline/arrow-left';
	import IconSearch from '~icons/icon-park-outline/search';

	let {
		resourceName,
		returnHref
	}: {
		resourceName: string;
		returnHref: string;
	} = $props();

	let countdown = $state(3);

	onMount(() => {
		const timer = setInterval(() => {
			countdown -= 1;
			if (countdown === 0) {
				clearInterval(timer);
				goto(returnHref);
			}
		}, TIMING.REDIRECT_DELAY);

		const redirectTimeout = setTimeout(() => {
			goto(returnHref);
		}, TIMING.REDIRECT_DELAY * 3);

		return () => {
			clearInterval(timer);
			clearTimeout(redirectTimeout);
		};
	});
</script>

<div class="hero bg-base-200 min-h-screen">
	<div class="hero-content text-center">
		<div class="bg-base-100 rounded-box max-w-lg space-y-6 p-8 shadow-xl md:p-12">
			<div class="flex flex-col items-center space-y-4">
				<IconSearch class="text-primary h-24 w-24" />
				<h1 class="text-base-content text-3xl font-semibold md:text-4xl">
					{resourceName} Not Found
				</h1>
				<p class="text-base-content/80">
					The {resourceName.toLowerCase()} you're looking for doesn't exist or has been moved.
				</p>
			</div>

			<div class="flex flex-col items-center gap-4 pt-6">
				<a href={returnHref} class="btn btn-primary">
					<IconArrowLeft class="h-5 w-5" />
					Return to {resourceName} List
				</a>
				<p class="text-base-content/60 text-sm">
					Redirecting in {countdown} second{countdown === 1 ? '' : 's'}...
				</p>
			</div>
		</div>
	</div>
</div>
