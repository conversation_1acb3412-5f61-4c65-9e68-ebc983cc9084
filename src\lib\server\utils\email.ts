// <!--
// This module provides a utility function for sending emails using Resend.
// It configures the Resend client based on an environment variable.

// Required Environment Variables:
// - RESEND_API_KEY: Your API key for the Resend service.
// - EMAIL_FROM: The 'From' address for outgoing emails (e.g., "Your App Name" <<EMAIL>>).
// -->
import { dev } from '$app/environment';
import { env } from '$env/dynamic/private';
import { Resend } from 'resend';

let resend: Resend | null = null;
if (env.RESEND_API_KEY) {
	resend = new Resend(env.RESEND_API_KEY);
}

interface MailOptions {
	to: string;
	subject: string;
	text: string;
	html: string;
}

/**
 * Sends an email.
 * In development mode, if RESEND_API_KEY is not set, it logs the email to the console instead of sending.
 * @param mailOptions - Options for the email (to, subject, text, html).
 */
export async function sendEmail(mailOptions: MailOptions): Promise<void> {
	if (!env.EMAIL_FROM && !dev) {
		console.error('EMAIL_FROM environment variable is not set in production.');
		throw new Error('Email service is not configured.');
	}

	const mailData = {
		from: env.EMAIL_FROM || '"Hairloom App" <<EMAIL>>',
		to: mailOptions.to,
		subject: mailOptions.subject,
		text: mailOptions.text,
		html: mailOptions.html
	};

	if (dev) {
		console.log('--- Email Details (Development Mode) ---');
		console.log('From:', mailData.from);
		console.log('To:', mailData.to);
		console.log('Subject:', mailData.subject);
		console.log('Text:', mailData.text.substring(0, 200) + '...');
		console.log('HTML:', mailOptions.html.substring(0, 500) + '...');
		console.log('-------------------------------------------');
	}

	if (!resend) {
		if (dev) {
			console.log('RESEND_API_KEY not set. Skipping email sending in dev mode.');
			return;
		} else {
			console.error('RESEND_API_KEY is not set. Cannot send email in production.');
			throw new Error('Email service is not configured.');
		}
	}

	try {
		const { data, error } = await resend.emails.send(mailData);

		if (error) {
			console.error('Error sending email via Resend:', error);
			throw new Error('Failed to send email.');
		}

		console.log('Email sent successfully via Resend. Message ID:', data?.id);
	} catch (error) {
		console.error('Caught exception sending email:', error);
		throw new Error('Failed to send email due to an exception.');
	}
}
