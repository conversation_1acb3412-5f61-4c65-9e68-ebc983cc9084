<!--
  Inventory Item Detail / Edit Page
-->
<script lang="ts">
	import InventoryItemForm from '$lib/components/inventory/InventoryItemForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { updateInventoryItemSchema } from '$lib/schemas/inventory';
	import { confirm } from '$lib/ui/confirm';
	import { toast } from '$lib/ui/toast';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	const form = createRuneForm(updateInventoryItemSchema, {
		name: data.item.name ?? '',
		description: data.item.description ?? '',
		barcode: data.item.barcode ?? '',
		quantity: data.item.quantity ?? 0,
		price_in_cents: data.item.price_in_cents ?? undefined,
		cost_in_cents: data.item.cost_in_cents ?? 0,
		reorder_threshold: data.item.reorder_threshold ?? undefined,
		supplier: data.item.supplier ?? '',
		image_base64: data.item.image_base64 ?? ''
	});



	let deleteForm: HTMLFormElement;
	async function handleDelete(event: MouseEvent) {
		event.preventDefault();
		const ok = await confirm({
			title: 'Delete Item',
			message: 'Are you sure you want to delete this inventory item?',
			confirmText: 'Delete',
			cancelText: 'Cancel',
			confirmClass: 'btn-error'
		});
		if (ok) {
			deleteForm.requestSubmit();
		}
	}

	function handleSuccess() {
		toast.success('Item updated');
	}
</script>

<!-- Edit Inventory Item Page -->
<div class="mx-auto w-full max-w-6xl space-y-6 pb-24">
	<!-- Page Header -->
	<div class="bg-base-100/80 backdrop-blur-sm border border-base-300/30 rounded-xl shadow-sm p-6">
		<div class="flex items-center gap-3 mb-2">
			<div class="w-2 h-2 bg-secondary/40 rounded-full"></div>
			<h1 class="text-2xl font-semibold text-base-content/90">Edit Inventory Item</h1>
		</div>
		<p class="text-base-content/60 ml-5">
			Update your item details and keep your inventory accurate
		</p>
	</div>

	<div class="grid grid-cols-1 gap-8 lg:grid-cols-4">
		<!-- Main Form Section -->
		<div class="lg:col-span-3">
			<InventoryItemForm
				{form}
				action="?/update"
				submitLabel="Save Changes"
				onSuccess={handleSuccess}
				isNewItem={false}
			/>
		</div>

		<!-- Sidebar with Actions -->
		<div class="space-y-4">
			<!-- Item Info Card -->
			<div class="card bg-base-100/80 border border-base-300/30 shadow-sm">
				<div class="card-body p-4">
					<div class="flex items-center gap-2 mb-3">
						<div class="w-1.5 h-1.5 bg-info rounded-full"></div>
						<h3 class="text-base font-semibold text-base-content/90">Item Details</h3>
					</div>
					<div class="space-y-2 text-sm">
						<div class="flex justify-between">
							<span class="text-base-content/60">Created:</span>
							<span class="font-medium">{new Date(data.item.created_at).toLocaleDateString()}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-base-content/60">Updated:</span>
							<span class="font-medium">{new Date(data.item.updated_at).toLocaleDateString()}</span>
						</div>
						{#if data.item.barcode}
							<div class="flex justify-between">
								<span class="text-base-content/60">Barcode:</span>
								<span class="font-mono text-xs bg-base-200/50 px-2 py-1 rounded">{data.item.barcode}</span>
							</div>
						{/if}
					</div>
				</div>
			</div>

			<!-- Quick Actions Card -->
			<div class="card bg-base-100/80 border border-base-300/30 shadow-sm">
				<div class="card-body p-4">
					<div class="flex items-center gap-2 mb-3">
						<div class="w-1.5 h-1.5 bg-success rounded-full"></div>
						<h3 class="text-base font-semibold text-base-content/90">Quick Actions</h3>
					</div>
					<div class="space-y-2">
						<a
							href="/app/inventory"
							class="btn btn-ghost btn-sm w-full transition-colors duration-200 hover:bg-base-200/50"
						>
							← Back to Inventory
						</a>
						<a
							href="/app/inventory/new"
							class="btn btn-primary btn-sm w-full transition-all duration-200 hover:shadow-md"
						>
							+ Add New Item
						</a>
					</div>
				</div>
			</div>

			<!-- Danger Zone -->
			<div class="card bg-base-100/80 border border-error/30 shadow-sm">
				<div class="card-body p-4">
					<div class="flex items-center gap-2 mb-3">
						<div class="w-1.5 h-1.5 bg-error rounded-full"></div>
						<h3 class="text-base font-semibold text-error">Danger Zone</h3>
					</div>
					<p class="text-sm text-base-content/70 mb-3">
						This action is permanent and cannot be undone. All data associated with this item will be lost.
					</p>
					<div class="card-actions">
						<form method="POST" action="?/delete" bind:this={deleteForm} class="w-full">
							<button
								type="button"
								class="btn btn-error btn-sm w-full transition-all duration-200 hover:shadow-md"
								onclick={handleDelete}
							>
								Delete Item
							</button>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
