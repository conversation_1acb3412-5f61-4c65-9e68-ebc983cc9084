import { updateTemplateContentSchema, updateTemplateStatusSchema } from '$lib/schemas/template';
import { db } from '$lib/server/db';
import { templates } from '$lib/server/db/schema';
import { error, fail, isRedirect, redirect } from '@sveltejs/kit';
import { and, eq, isNull, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	const user = locals.user;
	if (!user) {
		redirect(302, '/login');
	}

	const templateId = params.id;
	const template = await db.query.templates.findFirst({
		where: and(
			eq(templates.id, templateId),
			or(eq(templates.user_id, user.id), isNull(templates.user_id))
		)
	});

	if (!template) {
		error(404, 'Template not found');
	}

	// If it's a system template or not a draft, user can't edit. Redirect to preview.
	if (!template.user_id || template.status !== 'draft') {
		redirect(302, `/app/templates/${template.id}/preview`);
	}

	return {
		template
	};
};

export const actions: Actions = {
	updateTemplate: async ({ request, locals, params }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { updateTemplate: { errors: { root: ['Unauthorized'] } } });
		}

		const templateId = params.id;
		const formData = Object.fromEntries(await request.formData());

		const template = await db.query.templates.findFirst({
			columns: { status: true },
			where: and(eq(templates.id, templateId), eq(templates.user_id, user.id))
		});

		if (!template) {
			return fail(404, {
				updateTemplate: { data: formData, errors: { root: ['Template not found.'] } }
			});
		}
		if (template.status !== 'draft') {
			return fail(403, {
				updateTemplate: {
					data: formData,
					errors: { root: ['Only draft templates can be edited.'] }
				}
			});
		}

		const validation = updateTemplateContentSchema.safeParse(formData);

		if (!validation.success) {
			return {
				updateTemplate: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			};
		}

		const { name, description, template_definition } = validation.data;

		try {
			const [updatedTemplate] = await db
				.update(templates)
				.set({
					name,
					description,
					template_definition,
					updated_at: new Date()
				})
				.where(and(eq(templates.id, templateId), eq(templates.user_id, user.id)))
				.returning();

			// The full template is returned on success so the page can re-render with fresh data
			return { updateTemplate: { success: true, updatedTemplate } };
		} catch (err) {
			if (isRedirect(err)) throw err;
			console.error(err);
			return fail(500, {
				updateTemplate: { data: formData, errors: { root: ['Failed to update template.'] } }
			});
		}
	},

	updateStatus: async ({ request, locals, params }) => {
		const user = locals.user;
		if (!user) return fail(401, { updateStatus: { errors: { root: ['Unauthorized'] } } });

		const formData = await request.formData();
		const validation = updateTemplateStatusSchema.safeParse(Object.fromEntries(formData));

		if (!validation.success) {
			return {
				updateStatus: {
					data: Object.fromEntries(formData),
					errors: validation.error.flatten().fieldErrors
				}
			};
		}

		const { status: newStatus } = validation.data;
		const templateId = params.id;

		const template = await db.query.templates.findFirst({
			columns: { status: true },
			where: and(eq(templates.id, templateId), eq(templates.user_id, user.id))
		});

		if (!template) {
			return fail(404, {
				updateStatus: {
					data: Object.fromEntries(formData),
					errors: { root: ['Template not found.'] }
				}
			});
		}

		const currentStatus = template.status;
		const allowedTransitions: Record<string, string[]> = {
			draft: ['active'],
			active: ['archived'],
			archived: ['active']
		};

		if (!allowedTransitions[currentStatus]?.includes(newStatus)) {
			return fail(400, {
				updateStatus: {
					data: Object.fromEntries(formData),
					errors: { root: [`Invalid status transition from ${currentStatus} to ${newStatus}.`] }
				}
			});
		}

		await db
			.update(templates)
			.set({ status: newStatus as 'draft' | 'active' | 'archived', updated_at: new Date() })
			.where(and(eq(templates.id, templateId), eq(templates.user_id, user.id)));

		// If publishing from editor, redirect to preview page to see the read-only version
		if (currentStatus === 'draft' && newStatus === 'active') {
			redirect(302, `/app/templates/${templateId}/preview`);
		}

		return { updateStatus: { success: true, message: `Template status updated to ${newStatus}.` } };
	},

	deleteTemplate: async ({ locals, params }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { deleteTemplate: { errors: { root: ['Unauthorized'] } } });
		}

		const templateId = params.id;

		try {
			const [deleted] = await db
				.delete(templates)
				.where(and(eq(templates.id, templateId), eq(templates.user_id, user.id)))
				.returning({ id: templates.id });

			if (!deleted) {
				return fail(404, {
					deleteTemplate: {
						errors: { root: ['Template not found or you do not have permission to delete it.'] }
					}
				});
			}
		} catch (err) {
			if (isRedirect(err)) throw err;
			console.error(err);
			return fail(500, {
				deleteTemplate: { errors: { root: ['Failed to delete template.'] } }
			});
		}

		throw redirect(302, '/app/templates');
	}
};
