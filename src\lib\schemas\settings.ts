import { z } from 'zod/v4';
import {
	email<PERSON>ield,
	first<PERSON><PERSON><PERSON><PERSON>,
	login<PERSON><PERSON><PERSON><PERSON>ield,
	passwordConfirmationRefine,
	passwordField
} from './shared-primitives';

export const updateProfileSchema = z.object({
	first_name: firstNameField
});

export const updateEmailSchema = z.object({
	email: email<PERSON>ield,
	password: loginP<PERSON><PERSON><PERSON>ield
});

export const updatePasswordSchema = z
	.object({
		current_password: loginPassword<PERSON>ield,
		new_password: passwordField,
		confirm_password: z.string()
	})
	.refine(
		passwordConfirmationRefine('new_password', 'confirm_password'),
		{
			message: 'New passwords do not match',
			path: ['confirm_password']
		}
	);

export const deleteAccountSchema = z.object({
	password: z.string().min(1, 'Password is required to delete your account')
});
