# Security Deployment Checklist

## 🔒 Row Level Security (RLS) Setup

### 1. Run the RLS Migration on Neon

Connect to your Neon database and execute the RLS migration:

```bash
# Option 1: Run the SQL file directly in Neon Console
# Copy and paste the contents of src/lib/server/db/migrations/enable-rls.sql

# Option 2: Use psql with your Neon connection string
psql "your-neon-connection-string" -f src/lib/server/db/migrations/enable-rls.sql
```

### 2. Verify RLS is Working

Run the test script to verify <PERSON><PERSON> is properly configured:

```bash
npm run tsx src/lib/server/db/test-rls.ts
```

Expected output:
- ✅ RLS Status shows `rowsecurity: true`
- ✅ Users can only see their own data
- ✅ No items visible without user context

## 🛡️ Security Headers Verification

The following security headers are now automatically added:

- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `X-XSS-Protection: 1; mode=block`
- `Permissions-Policy: camera=self, microphone=(), geolocation=(), payment=()`
- `Content-Security-Policy: [comprehensive policy]`

### Verify Headers

Test your deployed application with:

```bash
curl -I https://your-domain.com
```

Or use online tools like:
- https://securityheaders.com/
- https://observatory.mozilla.org/

## 🍪 Cookie Security

Session cookies now use:
- `sameSite: 'strict'` (enhanced CSRF protection)
- `httpOnly: true` (XSS protection)
- `secure: true` (in production)

## 🔍 Testing Checklist

### Before Deployment:

1. **RLS Testing**
   - [ ] Run RLS test script
   - [ ] Verify users can't access each other's data
   - [ ] Test with multiple user accounts

2. **Authentication Testing**
   - [ ] Test login/logout flows
   - [ ] Verify session expiration
   - [ ] Test WebAuthn/passkey functionality

3. **Authorization Testing**
   - [ ] Test inventory item access controls
   - [ ] Test client data access controls
   - [ ] Test API endpoint authorization

4. **Input Validation Testing**
   - [ ] Test barcode scanning with invalid inputs
   - [ ] Test image upload with various file types
   - [ ] Test form submissions with malicious data

### After Deployment:

1. **Security Headers**
   - [ ] Verify all security headers are present
   - [ ] Test CSP doesn't break functionality
   - [ ] Check for any console errors

2. **Functionality Testing**
   - [ ] Test barcode scanning works
   - [ ] Test image uploads work
   - [ ] Test all form submissions work
   - [ ] Test camera access works

## 🚨 Known Limitations

### Content Security Policy (CSP)
The current CSP includes `'unsafe-inline'` and `'unsafe-eval'` for development compatibility. For production, consider:

1. **Remove unsafe-inline for scripts**:
   - Use nonces or hashes for inline scripts
   - Move all inline JavaScript to external files

2. **Remove unsafe-eval**:
   - Required for Svelte in development
   - Should work without it in production builds

### RLS Context Setting
- RLS context is set per request in hooks.server.ts
- If a request fails to set context, application-level auth still protects data
- Monitor logs for "Failed to set RLS context" errors

## 🔧 Troubleshooting

### RLS Not Working
1. Check if tables have RLS enabled:
   ```sql
   SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE tablename IN ('inventory_items', 'clients');
   ```

2. Check if policies exist:
   ```sql
   SELECT * FROM pg_policies WHERE tablename = 'inventory_items';
   ```

3. Verify user context is being set:
   ```sql
   SELECT current_setting('app.current_user_id', true);
   ```

### Security Headers Not Applied
1. Check if hooks.server.ts is being executed
2. Verify no other middleware is overriding headers
3. Test with curl to see actual headers

### Cookie Issues
1. Verify `sameSite: 'strict'` doesn't break OAuth flows
2. Check if `secure: true` works with your domain setup
3. Test cross-origin requests if needed

## 📊 Monitoring

### Security Metrics to Monitor
1. **Failed RLS context settings** - Check application logs
2. **Authentication failures** - Monitor rate limiting triggers
3. **Invalid barcode scans** - Check for potential attack attempts
4. **CSP violations** - Monitor browser console for CSP errors

### Log Monitoring
Watch for these log patterns:
- "Failed to set RLS context"
- "Invalid barcode format detected"
- "CSRF: Invalid origin"
- Rate limiting messages

## 🎯 Next Steps

### Additional Security Enhancements
1. **Implement Content Security Policy nonces**
2. **Add rate limiting to API endpoints**
3. **Implement request size limits**
4. **Add input sanitization for rich text fields**
5. **Consider implementing CAPTCHA for sensitive operations**

### Performance Monitoring
1. **Monitor RLS query performance**
2. **Check if setting user context adds significant latency**
3. **Optimize database queries with RLS policies**
