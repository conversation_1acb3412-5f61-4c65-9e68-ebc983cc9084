export function formatCurrency(priceInCents: number | null | undefined): string {
	if (priceInCents === null || priceInCents === undefined) {
		return '---';
	}

	// Ensure the value is a number before formatting
	const numericPrice = Number(priceInCents);
	if (isNaN(numericPrice)) {
		// Handle cases where conversion might fail, though unlikely with TypeScript types
		return '---';
	}

	return new Intl.NumberFormat('en-US', {
		style: 'currency',
		currency: 'USD'
	}).format(numericPrice / 100);
}
