<!--
	@component
	Refined card component that follows the coy/playful/refined aesthetic
-->
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		title?: string;
		subtitle?: string;
		class?: string;
		padding?: 'none' | 'sm' | 'md' | 'lg';
		hover?: boolean;
		children: Snippet;
	}

	let { 
		title, 
		subtitle, 
		class: className = '', 
		padding = 'md',
		hover = false,
		children 
	}: Props = $props();

	const paddingClasses = {
		none: '',
		sm: 'p-4',
		md: 'p-6',
		lg: 'p-8'
	};

	const baseClasses = 'bg-base-100/80 border border-base-300/30 shadow-sm rounded-xl';
	const hoverClasses = hover ? 'transition-all duration-200 hover:shadow-md hover:border-base-300/50' : '';
	const classes = `${baseClasses} ${hoverClasses} ${className}`;
</script>

<div class={classes}>
	{#if title || subtitle}
		<div class="border-b border-base-300/20 {paddingClasses[padding]} pb-4 mb-6">
			{#if title}
				<h3 class="text-lg font-semibold text-base-content/90">{title}</h3>
			{/if}
			{#if subtitle}
				<p class="text-sm text-base-content/60 mt-1">{subtitle}</p>
			{/if}
		</div>
		<div class={paddingClasses[padding]}>
			{@render children()}
		</div>
	{:else}
		<div class={paddingClasses[padding]}>
			{@render children()}
		</div>
	{/if}
</div>
