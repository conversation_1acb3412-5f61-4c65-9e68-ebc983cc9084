<script lang="ts">
	import { enhance } from '$app/forms';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { createTemplateSchema } from '$lib/schemas/template';
	import IconAdd from '~icons/icon-park-outline/add-one';
	import IconCopy from '~icons/icon-park-outline/copy';
	import IconEdit from '~icons/icon-park-outline/edit';
	import IconEye from '~icons/icon-park-outline/preview-open';

	let { data } = $props();

	const createTemplateForm = createRuneForm(createTemplateSchema, {});
</script>

<div class="container mx-auto p-4 md:p-6">
	<div class="mb-6 flex items-center justify-between">
		<h1 class="text-3xl font-bold">Templates</h1>
		<RuneForm form={createTemplateForm}>
			{#snippet children(form)}
				<form
					method="POST"
					action="?/createTemplate"
					use:enhance={() => {
						form.setSubmitting(true);
						// No need to handle update, it's a redirect
					}}
					onsubmit={form.handleSubmit()}
				>
					<button class="btn btn-primary" type="submit" disabled={form.isSubmitting}>
						<IconAdd class="h-5 w-5" />
						{#if form.isSubmitting}Creating...{:else}Create New Template{/if}
					</button>
				</form>
			{/snippet}
		</RuneForm>
	</div>

	<!-- User Templates -->
	<section class="mb-12">
		<h2 class="mb-4 border-b pb-2 text-2xl font-semibold">Your Templates</h2>
		{#if data.userTemplates.length > 0}
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				{#each data.userTemplates as template (template.id)}
					<div class="card card-compact bg-base-100 shadow-xl">
						<div class="card-body">
							<div class="flex items-start justify-between">
								<h3 class="card-title">{template.name}</h3>
								<span
									class="badge"
									class:badge-success={template.status === 'active'}
									class:badge-warning={template.status === 'draft'}
									class:badge-ghost={template.status === 'archived'}
								>
									{template.status}
								</span>
							</div>
							<p class="text-base-content/70 flex-grow text-sm">
								{template.description || 'No description'}
							</p>
							<div class="card-actions mt-4 justify-end">
								{#if template.status === 'draft'}
									<a href="/app/templates/{template.id}" class="btn btn-sm btn-outline btn-primary">
										<IconEdit /> Edit
									</a>
								{:else}
									<a href="/app/templates/{template.id}/preview" class="btn btn-sm btn-outline">
										<IconEye /> View
									</a>
								{/if}
							</div>
						</div>
					</div>
				{/each}
			</div>
		{:else}
			<div class="bg-base-200 rounded-lg p-8 text-center">
				<p>You haven't created any templates yet.</p>
				<p class="text-base-content/60 text-sm">Click "Create New Template" to get started.</p>
			</div>
		{/if}
	</section>

	<!-- System Templates -->
	<section>
		<h2 class="mb-4 border-b pb-2 text-2xl font-semibold">System Templates</h2>
		{#if data.systemTemplates.length > 0}
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				{#each data.systemTemplates as template (template.id)}
					<div class="card card-compact bg-base-100 shadow-xl">
						<div class="card-body">
							<h3 class="card-title">{template.name}</h3>
							<p class="text-base-content/70 flex-grow text-sm">
								{template.description || 'No description'}
							</p>
							<div class="card-actions mt-4 justify-end">
								<form method="POST" action="?/copyTemplate" use:enhance>
									<input type="hidden" name="templateId" value={template.id} />
									<button class="btn btn-sm btn-outline" type="submit">
										<IconCopy /> Copy to My Templates
									</button>
								</form>
								<a href="/app/templates/{template.id}/preview" class="btn btn-sm btn-outline">
									<IconEye /> Preview
								</a>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{:else}
			<div class="bg-base-200 rounded-lg p-8 text-center">
				<p>There are no system templates available.</p>
			</div>
		{/if}
	</section>
</div>
