import { forgotPasswordSchema, useRecoveryCodeSchema } from '$lib/schemas/auth';
import { createSession, generateSessionToken, setSessionTokenCookie } from '$lib/server/auth';
import { verifyAndUseRecoveryCode } from '$lib/server/auth/account-recovery';
import {
	createPasswordResetAttempt,
	invalidateUserPasswordResetAttempts,
	sendPasswordResetEmail
} from '$lib/server/auth/password-reset';
import { RefillingTokenBucket, Throttler } from '$lib/server/auth/rate-limit';
import { getUserFromEmail } from '$lib/server/auth/user';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

// Rate limiters
const ipBucket = new RefillingTokenBucket(5, 60 * 5); // 5 attempts per 5 minutes for any action from an IP
const userForgotPasswordBucket = new RefillingTokenBucket(3, 60 * 10); // 3 forgot password attempts per 10 mins for a user ID
const userRecoveryCodeBucket = new RefillingTokenBucket(5, 60 * 10); // 5 recovery code attempts per 10 mins for a user ID

const forgotPasswordRateLimiter = new Throttler([10, 30, 60]);

export const load: PageServerLoad = async ({ locals }) => {
	if (locals.user) {
		redirect(302, '/app');
	}
	return {};
};

export const actions: Actions = {
	forgotPassword: async (event) => {
		const clientIP = event.request.headers.get('X-Forwarded-For') || event.getClientAddress();
		if (!ipBucket.check(clientIP, 1)) {
			return fail(429, {
				forgotPassword: {
					message: 'Too many requests from this IP. Please try again later.'
				}
			});
		}

		const formData = Object.fromEntries(await event.request.formData());
		const validation = forgotPasswordSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				forgotPassword: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}

		const { email } = validation.data;

		const user = await getUserFromEmail(email);

		if (user === null) {
			console.log(`Password reset attempt for non-existent email: ${email}`);
			ipBucket.consume(clientIP, 1);
			return {
				forgotPassword: {
					success: true,
					message: 'If your email is in our system, you will receive a password reset link.'
				}
			};
		}

		if (!userForgotPasswordBucket.check(user.id, 1)) {
			return fail(429, {
				forgotPassword: {
					message: 'Too many password reset attempts for this account. Please try again later.',
					data: formData
				}
			});
		}

		ipBucket.consume(clientIP, 1);
		userForgotPasswordBucket.consume(user.id, 1);

		await invalidateUserPasswordResetAttempts(user.id);
		const attemptData = await createPasswordResetAttempt(user.id, user.email);

		if (!attemptData) {
			return fail(500, {
				forgotPassword: {
					message: 'Could not create password reset request. Please try again later.',
					data: formData
				}
			});
		}

		await sendPasswordResetEmail(
			user.email,
			attemptData.plaintextUrlToken,
			attemptData.plaintextOtp,
			event.url.origin
		);

		return redirect(302, `/reset-password/verify-email?token=${attemptData.plaintextUrlToken}`);
	},

	useRecoveryCode: async (event) => {
		const clientIP = event.request.headers.get('X-Forwarded-For') || event.getClientAddress();
		if (!ipBucket.check(clientIP, 1)) {
			return fail(429, {
				useRecoveryCode: {
					message: 'Too many requests from this IP. Please try again later.'
				}
			});
		}

		const formData = Object.fromEntries(await event.request.formData());
		const validation = useRecoveryCodeSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				useRecoveryCode: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}

		const { email, recovery_code } = validation.data;
		const user = await getUserFromEmail(email);

		if (user === null) {
			ipBucket.consume(clientIP, 1);
			return fail(400, {
				useRecoveryCode: {
					message: 'Invalid email or recovery code.',
					data: formData
				}
			});
		}

		if (!userRecoveryCodeBucket.check(user.id, 1)) {
			return fail(429, {
				useRecoveryCode: {
					message: 'Too many recovery code attempts for this account. Please try again later.',
					data: formData
				}
			});
		}

		ipBucket.consume(clientIP, 1);
		userRecoveryCodeBucket.consume(user.id, 1);

		const isValidCode = await verifyAndUseRecoveryCode(user.id, recovery_code);

		if (!isValidCode) {
			return fail(400, {
				useRecoveryCode: {
					message: 'Invalid email or recovery code.',
					data: formData
				}
			});
		}

		const sessionToken = generateSessionToken();
		const session = await createSession(sessionToken, user.id);
		setSessionTokenCookie(event, sessionToken, session.expires_at);

		return redirect(302, '/app');
	}
};
