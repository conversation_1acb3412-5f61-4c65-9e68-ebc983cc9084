/* 
  Enhanced Animation Utilities for Hairloom CRM
  Modern microinteractions and playful animations
*/

/* ===== KEYFRAME ANIMATIONS ===== */

/* Gentle bounce for interactive elements - uses consolidated scale value */
@keyframes gentleBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(var(--animation-scale-hover)); }
}

/* Playful wiggle for attention - uses consolidated rotation values */
@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(calc(-1 * var(--animation-rotate-wiggle))); }
  75% { transform: rotate(var(--animation-rotate-wiggle)); }
}

/* Smooth slide up for modals and dropdowns - uses consolidated translate value */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(var(--animation-translate-slide));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade in with scale for cards - uses consolidated scale value */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(var(--animation-scale-initial));
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse glow for important elements - uses consolidated shadow value */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 var(--animation-shadow-glow) rgba(var(--color-primary), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--color-primary), 0.6);
  }
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Shimmer effect for loading states */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Typewriter effect for text */
@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

/* Gradient shift for backgrounds */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Slow, gentle floating animation for background elements */
@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.8;
  }
  33% {
    transform: translateY(-8px) translateX(4px);
    opacity: 1;
  }
  66% {
    transform: translateY(4px) translateX(-2px);
    opacity: 0.9;
  }
}

/* Slow, subtle pulse for ambient elements */
@keyframes pulseSlow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Purposeful data pulse animation */
@keyframes pulseData {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
    box-shadow: 0 0 0 0 currentColor;
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 0 0 8px transparent;
  }
}

/* Gradient background shift */
@keyframes gradientShiftBg {
  0%, 100% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

/* SVG path drawing animation with smooth leaping effect */
@keyframes drawPath {
  0% {
    stroke-dashoffset: 100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  45% {
    stroke-dashoffset: 0%;
    opacity: 1;
  }
  55% {
    stroke-dashoffset: -20%;
    opacity: 0.8;
  }
  90% {
    stroke-dashoffset: -100%;
    opacity: 0;
  }
  100% {
    stroke-dashoffset: -100%;
    opacity: 0;
  }
}

/* Variant path animations for variety */
@keyframes drawPathSlow {
  0% {
    stroke-dashoffset: 100%;
    opacity: 0;
  }
  8% {
    opacity: 1;
  }
  50% {
    stroke-dashoffset: 0%;
    opacity: 1;
  }
  65% {
    stroke-dashoffset: -25%;
    opacity: 0.7;
  }
  92% {
    stroke-dashoffset: -100%;
    opacity: 0;
  }
  100% {
    stroke-dashoffset: -100%;
    opacity: 0;
  }
}

@keyframes drawPathFast {
  0% {
    stroke-dashoffset: 100%;
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  35% {
    stroke-dashoffset: 0%;
    opacity: 1;
  }
  45% {
    stroke-dashoffset: -15%;
    opacity: 0.9;
  }
  85% {
    stroke-dashoffset: -100%;
    opacity: 0;
  }
  100% {
    stroke-dashoffset: -100%;
    opacity: 0;
  }
}

/* ===== UTILITY CLASSES ===== */

/* Hover animations */
.hover-bounce:hover {
  animation: gentleBounce 0.6s ease-in-out;
}

.hover-wiggle:hover {
  animation: wiggle 0.5s ease-in-out;
}

.hover-float:hover {
  animation: float 2s ease-in-out infinite;
}

/* Click animations */
.click-scale:active {
  transform: scale(0.95);
  transition: transform 0.1s ease-out;
}

.click-bounce:active {
  animation: gentleBounce 0.3s ease-out;
}

/* Loading animations */
.shimmer-loading {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.2) 20%, 
    rgba(255, 255, 255, 0.5) 60%, 
    rgba(255, 255, 255, 0)
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Entrance animations */
.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Background decoration animations */
.animate-float-slow {
  animation: floatSlow 8s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulseSlow 6s ease-in-out infinite;
}

/* Purposeful background animations */
.animate-pulse-data {
  animation: pulseData 3s ease-in-out infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradientShiftBg 20s ease-in-out infinite;
}

.animate-draw-path-1 {
  animation: drawPath 18s ease-in-out infinite;
  animation-delay: 0s;
}

.animate-draw-path-2 {
  animation: drawPathSlow 22s ease-in-out infinite;
  animation-delay: 5s;
}

.animate-draw-path-3 {
  animation: drawPathFast 14s ease-in-out infinite;
  animation-delay: 11s;
}

.animate-draw-path-4 {
  animation: drawPath 16s ease-in-out infinite;
  animation-delay: 3s;
}

.animate-draw-path-5 {
  animation: drawPathSlow 20s ease-in-out infinite;
  animation-delay: 8s;
}

.animate-draw-path-6 {
  animation: drawPathFast 12s ease-in-out infinite;
  animation-delay: 15s;
}

/* Gradient utilities for background elements */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Background decoration enhancements */
.backdrop-blur-3xl {
  backdrop-filter: blur(64px);
}

.blur-3xl {
  filter: blur(64px);
}

/* Refined animation timing for background elements */
.animate-ambient-1 {
  animation: floatSlow 12s ease-in-out infinite;
  animation-delay: 0s;
}

.animate-ambient-2 {
  animation: pulseSlow 10s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-ambient-3 {
  animation: floatSlow 14s ease-in-out infinite;
  animation-delay: 4s;
}

.animate-ambient-4 {
  animation: pulseSlow 8s ease-in-out infinite;
  animation-delay: 1s;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-float-slow,
  .animate-pulse-slow,
  .animate-ambient-1,
  .animate-ambient-2,
  .animate-ambient-3,
  .animate-ambient-4,
  .animate-pulse-data,
  .animate-gradient-shift,
  .animate-draw-path-1,
  .animate-draw-path-2,
  .animate-draw-path-3,
  .animate-draw-path-4,
  .animate-draw-path-5,
  .animate-draw-path-6 {
    animation: none;
  }

  /* Show static versions for reduced motion */
  .animate-draw-path-1,
  .animate-draw-path-2,
  .animate-draw-path-3,
  .animate-draw-path-4,
  .animate-draw-path-5,
  .animate-draw-path-6 {
    stroke-dashoffset: 0;
  }
}

.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

/* Attention animations */
.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Stagger animations for lists */
.stagger-item {
  opacity: 0;
  animation: fadeInScale 0.5s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }
.stagger-item:nth-child(7) { animation-delay: 0.7s; }
.stagger-item:nth-child(8) { animation-delay: 0.8s; }

/* ===== ENHANCED TRANSITIONS ===== */

/* Smooth transitions for interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.transition-elastic {
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* ===== PLAYFUL MICROINTERACTIONS ===== */

/* Button enhancements */
.btn-playful {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-playful::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-playful:hover::before {
  width: 300px;
  height: 300px;
}

/* Card hover effects */
.card-playful {
  transition: all 0.3s ease;
  position: relative;
}

.card-playful::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.card-playful:hover::after {
  opacity: 1;
}

.card-playful:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Input focus effects */
.input-playful {
  position: relative;
  transition: all 0.3s ease;
}

.input-playful:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.1);
}

/* ===== ACCESSIBILITY CONSIDERATIONS ===== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .hover-float:hover,
  .animate-pulse-glow,
  .animate-gradient-shift {
    animation: none !important;
  }
}

/* ===== MOBILE OPTIMIZATIONS ===== */

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
}

/* Ensure minimum touch target size - Use more specific selectors to avoid overriding DaisyUI */
@media (max-width: 1024px) {
  /* Only apply to buttons that are specifically marked for mobile optimization */
  .btn-sm.mobile-optimized {
    min-height: var(--size-touch-target);
    min-width: var(--size-touch-target);
  }

  .btn-circle.mobile-optimized {
    min-height: var(--size-touch-target);
    min-width: var(--size-touch-target);
  }

  /* Only apply to inputs that are specifically marked for mobile optimization */
  .input-lg.mobile-optimized {
    font-size: 1rem;
  }

  /* Only apply to cards that are specifically marked for mobile optimization */
  .card-body.mobile-optimized {
    padding: 0.75rem;
  }

  /* Only apply to form controls that are specifically marked for mobile optimization */
  .form-control.mobile-optimized {
    margin-bottom: 1rem;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Use transform and opacity for better performance */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* Contain layout shifts */
.contain-layout {
  contain: layout style paint;
}
