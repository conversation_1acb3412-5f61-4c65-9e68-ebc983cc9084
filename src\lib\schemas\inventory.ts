// ALPHA FEATURE: Inventory Management Validation Schemas
// These schemas support the inventory management system that is currently in alpha status.
// They provide comprehensive validation for inventory items including cost/price handling,
// barcode validation, quantity tracking, and image processing.
// The schemas distinguish between purchase cost (required, can be 0) and selling price (optional).

import { z } from 'zod/v4';
import { barcodeField, descriptionField, imageBase64Field, nameField, quantityField, reorderThresholdField, supplierField } from './shared-primitives';

// Selling price schema - what you charge customers (optional)
const optionalPriceSchema = z.preprocess((val) => {
	if (val === '' || val === null || val === undefined) return undefined;
	const num = parseFloat(String(val));
	return isNaN(num) ? undefined : num * 100;
}, z.number().int().min(0).optional());

// Purchase cost schema - what you pay to acquire the item (required, can be 0)
const costSchema = z.preprocess(
	(val) => {
		// Handle empty string as 0 (user acknowledged no cost)
		if (val === '' || val === null || val === undefined) return 0;
		const num = parseFloat(String(val));
		return isNaN(num) ? 0 : num * 100;
	},
	z.number().int().min(0, 'Purchase cost must be 0 or greater.')
);

export const baseInventoryItemSchema = z.object({
	name: nameField,
	description: descriptionField,
	barcode: barcodeField,
	quantity: quantityField,
	price_in_cents: optionalPriceSchema, // Selling price - what you charge customers (optional)
	image_base64: imageBase64Field,
	cost_in_cents: costSchema, // Purchase cost - what you pay to acquire the item (required)
	reorder_threshold: reorderThresholdField,
	supplier: supplierField
});

export const insertInventoryItemSchema = baseInventoryItemSchema;
export const updateInventoryItemSchema = baseInventoryItemSchema;

export type InsertInventoryItem = z.infer<typeof insertInventoryItemSchema>;
export type UpdateInventoryItem = z.infer<typeof updateInventoryItemSchema>;
