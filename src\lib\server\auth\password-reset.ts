import { db } from '$lib/server/db';
import {
    password_reset_tokens,
    users,
    type PasswordResetToken,
    type User
} from '$lib/server/db/schema';
import { sendEmail } from '$lib/server/utils/email';
import { eq } from 'drizzle-orm';
import { createTokenPair, hashUrlToken, TOKEN_DURATIONS } from './token-manager';
import { hashOtp } from './utils';

export async function createPasswordResetAttempt(
	userId: string,
	userEmail: string
): Promise<{ plaintextUrlToken: string; plaintextOtp: string; expiresAt: Date } | null> {
	// Invalidate all previous reset attempts for this user
	await invalidateUserPasswordResetAttempts(userId);

	const tokenPair = await createTokenPair({
		durationMinutes: TOKEN_DURATIONS.PASSWORD_RESET
	});

	try {
		await db.insert(password_reset_tokens).values({
			id: tokenPair.hashedUrlTokenId,
			user_id: userId,
			email: userEmail,
			hashed_otp_code: tokenPair.hashedOtpForDb,
			expires_at: tokenPair.expiresAt
		});
		return {
			plaintextUrlToken: tokenPair.plaintextUrlToken,
			plaintextOtp: tokenPair.plaintextOtp,
			expiresAt: tokenPair.expiresAt
		};
	} catch (error) {
		console.error('Failed to create password reset attempt:', error);
		return null;
	}
}

export async function getPasswordResetAttemptData(
	plaintextUrlToken: string
): Promise<{ tokenRecord: PasswordResetToken; user: User } | null> {
	const hashedUrlTokenId = hashUrlToken(plaintextUrlToken);

	const result = await db
		.select({
			tokenRecord: password_reset_tokens,
			user: users
		})
		.from(password_reset_tokens)
		.innerJoin(users, eq(password_reset_tokens.user_id, users.id))
		.where(eq(password_reset_tokens.id, hashedUrlTokenId))
		.limit(1);

	if (result.length === 0 || !result[0]) {
		return null;
	}

	const { tokenRecord, user } = result[0];

	if (new Date() >= new Date(tokenRecord.expires_at)) {
		await db.delete(password_reset_tokens).where(eq(password_reset_tokens.id, tokenRecord.id));
		return null;
	}
	return { tokenRecord, user };
}

export async function verifyPasswordResetOtp(
	hashedUrlTokenIdForAttempt: string,
	plaintextOtp: string
): Promise<boolean> {
	const result = await db
		.select({ hashed_otp_code: password_reset_tokens.hashed_otp_code })
		.from(password_reset_tokens)
		.where(eq(password_reset_tokens.id, hashedUrlTokenIdForAttempt))
		.limit(1);

	if (result.length === 0 || !result[0] || !result[0].hashed_otp_code) {
		return false;
	}

	const newlyHashedOtp = await hashOtp(plaintextOtp);
	return newlyHashedOtp === result[0].hashed_otp_code;
}

export async function invalidateUserPasswordResetAttempts(userId: string): Promise<void> {
	await db.delete(password_reset_tokens).where(eq(password_reset_tokens.user_id, userId));
}

export async function invalidatePasswordResetAttempt(hashedUrlTokenId: string): Promise<void> {
	await db.delete(password_reset_tokens).where(eq(password_reset_tokens.id, hashedUrlTokenId));
}

export async function sendPasswordResetEmail(
	email: string,
	urlToken: string,
	otp: string,
	origin: string
): Promise<void> {
	const resetLink = `${origin}/reset-password?token=${urlToken}`;

	const subject = 'Your Password Reset Request for HairLoom';
	const text = `Hello,\n\nPlease use the following link and OTP to reset your password for HairLoom.\nReset Link: ${resetLink}\nYour One-Time Password (OTP) is: ${otp}\n\nIf you did not request this, please ignore this email.\n\nThanks,\nThe HairLoom Team`;
	const html = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6;">
      <h2>Password Reset Request for HairLoom</h2>
      <p>Hello,</p>
      <p>Please use the link below to reset your password. You will be asked for the OTP.</p>
      <p><strong>Reset Link:</strong> <a href="${resetLink}">${resetLink}</a></p>
      <p><strong>Your One-Time Password (OTP): ${otp}</strong></p>
      <p>If you did not request a password reset, you can safely ignore this email.</p>
      <hr>
      <p>Thanks,<br>The HairLoom Team</p>
    </div>
  `;

	console.log(`Sending password reset email to ${email} with link: ${resetLink} and OTP: ${otp}`);
	await sendEmail({ to: email, subject, text, html });
}
