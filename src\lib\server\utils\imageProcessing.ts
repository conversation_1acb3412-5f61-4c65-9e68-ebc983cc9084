import sharp from 'sharp';

export interface ImageProcessingOptions {
	maxWidth?: number;
	maxHeight?: number;
	quality?: number;
	format?: 'jpeg' | 'png' | 'webp';
}

/**
 * Process and resize a base64 image
 * @param base64Image - Base64 encoded image string (with data URL prefix)
 * @param options - Processing options
 * @returns Processed base64 image string
 */
export async function processImage(
	base64Image: string,
	options: ImageProcessingOptions = {}
): Promise<string> {
	const {
		maxWidth = 1600,
		maxHeight = 1200,
		quality = 80,
		format = 'jpeg'
	} = options;

	try {
		// Validate image format
		if (!isValidBase64Image(base64Image)) {
			throw new Error('Invalid image format');
		}

		// Extract the base64 data from the data URL
		const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');

		// Check image size (max 10MB)
		const inputBuffer = Buffer.from(base64Data, 'base64');
		const sizeInBytes = inputBuffer.length;
		if (sizeInBytes > 10 * 1024 * 1024) {
			throw new Error('Image size must be less than 10MB');
		}

		// Process the image with Sharp
		let sharpInstance = sharp(inputBuffer);

		// Get image metadata to check current dimensions
		const metadata = await sharpInstance.metadata();
		
		// Only resize if the image is larger than the max dimensions
		if (metadata.width && metadata.height) {
			if (metadata.width > maxWidth || metadata.height > maxHeight) {
				sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
					fit: 'inside',
					withoutEnlargement: true
				});
			}
		}

		// Convert to the specified format with quality settings
		let outputBuffer: Buffer;
		
		switch (format) {
			case 'jpeg':
				outputBuffer = await sharpInstance
					.jpeg({ quality, progressive: true })
					.toBuffer();
				break;
			case 'png':
				outputBuffer = await sharpInstance
					.png({ quality, progressive: true })
					.toBuffer();
				break;
			case 'webp':
				outputBuffer = await sharpInstance
					.webp({ quality })
					.toBuffer();
				break;
			default:
				outputBuffer = await sharpInstance
					.jpeg({ quality, progressive: true })
					.toBuffer();
		}

		// Convert back to base64 with data URL prefix
		const outputBase64 = outputBuffer.toString('base64');
		return `data:image/${format};base64,${outputBase64}`;

	} catch (error) {
		console.error('Error processing image:', error);
		throw new Error('Failed to process image');
	}
}

/**
 * Validate if a string is a valid base64 image
 * @param base64Image - Base64 encoded image string
 * @returns boolean indicating if the image is valid
 */
export function isValidBase64Image(base64Image: string): boolean {
	try {
		// Check if it has the correct data URL format
		const dataUrlRegex = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;
		if (!dataUrlRegex.test(base64Image)) {
			return false;
		}

		// Extract and validate the base64 data
		const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
		
		// Check if it's valid base64
		const buffer = Buffer.from(base64Data, 'base64');
		return buffer.toString('base64') === base64Data;
	} catch {
		return false;
	}
}

/**
 * Get image dimensions from base64 string
 * @param base64Image - Base64 encoded image string
 * @returns Object with width and height, or null if invalid
 */
export async function getImageDimensions(base64Image: string): Promise<{ width: number; height: number } | null> {
	try {
		const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
		const inputBuffer = Buffer.from(base64Data, 'base64');
		
		const metadata = await sharp(inputBuffer).metadata();
		
		if (metadata.width && metadata.height) {
			return {
				width: metadata.width,
				height: metadata.height
			};
		}
		
		return null;
	} catch {
		return null;
	}
}

/**
 * Calculate the file size of a base64 image in bytes
 * @param base64Image - Base64 encoded image string
 * @returns File size in bytes
 */
export function getBase64ImageSize(base64Image: string): number {
	try {
		const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
		return Buffer.from(base64Data, 'base64').length;
	} catch {
		return 0;
	}
}
