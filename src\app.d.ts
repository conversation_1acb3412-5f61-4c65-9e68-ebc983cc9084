// for information about these interfaces
import 'unplugin-icons/types/svelte';

/// <reference types="@sveltejs/kit" />

declare global {
	// If you use flash messages in your PageData, add this:
	interface PageData {
		flash?: { type: 'success' | 'error'; message: string };
	}

	namespace App {
		interface Locals {
			user: import('$lib/server/db/schema').User | null;
			session: import('$lib/server/db/schema').UserSession | null;
			// Add other user properties like email, name, roles, etc., as needed
		}
		interface PrivateEnv {
			POSTGRES_URL: string;
			RESEND_API_KEY: string;
			EMAIL_FROM: string;
			ENCRYPTION_SECRET: string;
			TWO_FACTOR_AUTHENTICATION: string;
			GOOGLE_CLIENT_ID: string;
			GOOGLE_CLIENT_SECRET: string;
			WEBAUTHN_RP_ID: string;
			WEBAUTHN_ORIGIN: string;
		}
		interface PublicEnv {
			PUBLIC_VERCEL_ENV: 'development' | 'preview' | 'production';
		}
	}
}

export { };

