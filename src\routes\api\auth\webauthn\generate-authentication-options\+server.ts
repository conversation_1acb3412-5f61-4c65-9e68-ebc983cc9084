import { RP_ID } from '$lib/server/auth/webauthn_internals';
import { db } from '$lib/server/db';
import { users, webauthn_credentials } from '$lib/server/db/schema';
import { generateAuthenticationOptions, type AuthenticatorTransport } from '@simplewebauthn/server';
import { error, json } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, cookies, url }) => {
	const { email } = await request.json();

	if (!email) {
		throw error(400, 'Email is required');
	}

	// Find user by email
	const user = await db.query.users.findFirst({
		where: eq(users.email, email)
	});

	if (!user) {
		throw error(404, 'User not found');
	}

	const userCredentials = await db.query.webauthn_credentials.findMany({
		where: eq(webauthn_credentials.user_id, user.id)
	});

	if (userCredentials.length === 0) {
		throw error(400, 'No passkeys found for this user.');
	}

	const options = await generateAuthenticationOptions({
		allowCredentials: userCredentials.map((cred) => ({
			id: cred.credential_id,
			type: 'public-key',
			transports: cred.transports?.split(',') as AuthenticatorTransport[] | undefined
		})),
		userVerification: 'preferred',
		rpID: RP_ID
	});

	// Store challenge in cookie (for compatibility with existing login flow)
	cookies.set('webauthn_authentication_challenge', options.challenge, {
		path: '/',
		httpOnly: true,
		secure: url.protocol === 'https:',
		sameSite: 'strict',
		maxAge: 60 * 5 // 5 minutes
	});

	// Also store challenge in database (for future use)
	await db.update(users).set({ current_challenge: options.challenge }).where(eq(users.id, user.id));

	return json(options);
};
