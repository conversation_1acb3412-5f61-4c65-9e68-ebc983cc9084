import * as auth from '$lib/server/auth';
import { db } from '$lib/server/db';
import { themes, type Theme } from '$lib/stores/themeStore';
import type { Handle } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { sql } from 'drizzle-orm';

const defaultTheme: Theme = 'light';
const themeCookieName = 'theme_preference';

export const handle: Handle = async ({ event, resolve }) => {
	// CSRF Protection for state-changing requests
	if (event.request.method === 'POST' && event.route.id?.startsWith('/(app)')) {
		const origin = event.request.headers.get('origin');
		const host = event.request.headers.get('host');
		
		// Allow same-origin requests and explicitly trusted origins
		if (!origin || (origin !== `https://${host}` && origin !== `http://${host}`)) {
			// Only allow localhost for development
			if (!origin?.startsWith('http://localhost:') && !origin?.startsWith('https://localhost:')) {
				throw error(403, 'CSRF: Invalid origin');
			}
		}
	}

	// --- Start of Authentication Logic (from original hook) ---
	const sessionToken = event.cookies.get(auth.sessionCookieName);

	if (!sessionToken) {
		event.locals.user = null;
		event.locals.session = null;
		// No return here yet, proceed to theme logic then resolve
	} else {
		const { session, user } = await auth.validateSessionToken(sessionToken);

		if (session) {
			auth.setSessionTokenCookie(event, sessionToken, session.expires_at);
		} else {
			auth.deleteSessionTokenCookie(event);
		}

		event.locals.user = user;
		event.locals.session = session;

		// Set RLS context for authenticated users
		if (user) {
			try {
				// Use parameterized query to prevent SQL injection
				await db.execute(sql`SELECT set_config('app.current_user_id', ${user.id}, true)`);
			} catch (error) {
				console.error('Failed to set RLS context:', error);
				// Continue without RLS context - application-level auth will still work
			}
		}
	}
	// --- End of Authentication Logic ---

	// --- Start of Theme Logic ---
	const currentThemeCookieValue = event.cookies.get(themeCookieName);
	let themeToApply: Theme = defaultTheme;

	if (currentThemeCookieValue && themes.includes(currentThemeCookieValue as Theme)) {
		themeToApply = currentThemeCookieValue as Theme;
	}
	// --- End of Theme Logic ---

	const response = await resolve(event, {
		transformPageChunk: ({ html }) => {
			// Replace %sveltekit.html_attributes% with the data-theme attribute
			// SvelteKit will place other attributes like lang="en" from app.html itself.
			return html.replace('%sveltekit.html_attributes%', `data-theme="${themeToApply}"`);
		}
	});

	// Add security headers
	response.headers.set('X-Frame-Options', 'DENY');
	response.headers.set('X-Content-Type-Options', 'nosniff');
	response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
	response.headers.set('X-XSS-Protection', '1; mode=block');
	response.headers.set('Permissions-Policy', 'camera=self, microphone=(), geolocation=(), payment=()');

	// Content Security Policy - adjust as needed for your app
	const csp = [
		"default-src 'self'",
		"script-src 'self' 'unsafe-inline' 'unsafe-eval'", // unsafe-eval needed for Svelte in dev
		"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
		"font-src 'self' https://fonts.gstatic.com",
		"img-src 'self' data: blob:",
		"connect-src 'self'",
		"media-src 'self'",
		"object-src 'none'",
		"base-uri 'self'",
		"form-action 'self'",
		"frame-ancestors 'none'"
	].join('; ');

	response.headers.set('Content-Security-Policy', csp);

	return response;
};
