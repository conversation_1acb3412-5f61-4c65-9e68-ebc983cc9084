<!--
  - src/lib/components/templates/TemplateEditor.svelte
  -
  - Purpose:
  - This is the main user interface for creating and editing form templates. It allows users
  - to manage the template's overall properties (name, description) and its fields.
  -
  - Key Features:
  - - Displays a list of all fields in the template.
  - - Allows adding new fields, which will open the `FieldEditorModal`.
  - - Allows editing existing fields, also using the `FieldEditorModal`.
  - - Supports reordering fields via drag-and-drop.
  - - Provides a mechanism to save the entire template.
  - - Emits events for parent components to handle state changes.
  -
  - Integration:
  - This component is designed to be used within the `src/routes/(app)/app/templates/[id]/+page.svelte`
  - route. It receives the initial template data as a prop and manages a local, mutable copy
  - for editing. When the user saves, it triggers a form submission.
-->
<script lang="ts">
	import { RUNE_FORM_CONTEXT, type RuneFormContext } from '$lib/runes-form/symbols.js';
	import { clientUpdateTemplateContentSchema, type FormField } from '$lib/schemas/template';
	import { getContext } from 'svelte';
	import IconAdd from '~icons/icon-park-outline/add-one';
	import IconDrag from '~icons/icon-park-outline/drag';
	import FieldEditorModal from './FieldEditorModal.svelte';

	let {
		children
	}: {
		children: any;
	} = $props();

	const form =
		getContext<RuneFormContext<typeof clientUpdateTemplateContentSchema>>(RUNE_FORM_CONTEXT);

	const fields = $derived(form.values.template_definition.fields);

	let isModalOpen = $state(false);
	let currentField = $state<FormField | null>(null);

	function openModalForNew() {
		currentField = null;
		isModalOpen = true;
	}

	function openModalForEdit(field: FormField) {
		currentField = { ...field }; // Work on a copy
		isModalOpen = true;
	}

	function handleFieldSave(field: FormField) {
		const currentFields = form.values.template_definition.fields;
		if (currentField) {
			const index = currentFields.findIndex((f: FormField) => f.id === field.id);
			if (index !== -1) {
				currentFields[index] = field;
			}
		} else {
			currentFields.push(field);
		}
		isModalOpen = false;
	}

	let dragStartIndex: number | null = null;

	function handleDragStart(index: number) {
		dragStartIndex = index;
	}

	function handleDrop(targetIndex: number) {
		if (dragStartIndex === null || dragStartIndex === targetIndex) {
			dragStartIndex = null;
			return;
		}

		const item = form.values.template_definition.fields.splice(dragStartIndex, 1)[0];
		form.values.template_definition.fields.splice(targetIndex, 0, item);

		dragStartIndex = null;
	}

	function handleDelete(fieldId: string) {
		form.values.template_definition.fields = form.values.template_definition.fields.filter(
			(f: FormField) => f.id !== fieldId
		);
	}
</script>

{@render children()}

<div class="card bg-base-100/80 shadow-xl">
	<div class="card-body">
		<div class="flex items-center justify-between">
			<h2 class="card-title text-2xl">Form Fields</h2>
			<button type="button" class="btn btn-neutral" onclick={openModalForNew}>
				<IconAdd class="h-5 w-5" />
				Add Field
			</button>
		</div>

		<div class="border-base-300 mt-4 min-h-[200px] rounded-lg border-2 border-dashed p-4">
			{#if fields.length > 0}
				<ul class="space-y-2">
					{#each fields as field, index (field.id)}
						<li
							draggable="true"
							ondragstart={() => handleDragStart(index)}
							ondragover={(e) => e.preventDefault()}
							ondrop={(e) => {
								e.preventDefault();
								handleDrop(index);
							}}
							class="bg-base-200 flex items-center gap-4 rounded-lg p-3 shadow"
						>
							<IconDrag class="text-base-content/50 h-6 w-6 cursor-move" />
							<div class="flex-grow">
								<p class="font-semibold">{field.label}</p>
								<p class="text-base-content/60 text-sm">
									Type: {field.type}
									{#if field.required}
										<span class="badge badge-xs badge-secondary ml-2">Required</span>
									{/if}
								</p>
							</div>
							<div class="flex gap-2">
								<button
									type="button"
									class="btn btn-sm btn-ghost"
									onclick={() => openModalForEdit(field)}>Edit</button
								>
								<button
									type="button"
									class="btn btn-sm btn-ghost text-error"
									onclick={() => handleDelete(field.id)}>Delete</button
								>
							</div>
						</li>
					{/each}
				</ul>
			{:else}
				<div class="flex h-full flex-col items-center justify-center text-center">
					<p class="font-semibold">No fields yet</p>
					<p class="text-base-content/60 text-sm">Click "Add Field" to build your form.</p>
				</div>
			{/if}
		</div>
	</div>
</div>

{#if isModalOpen}
	<FieldEditorModal
		field={currentField}
		onSave={(e) => handleFieldSave(e)}
		onClose={() => (isModalOpen = false)}
	/>
{/if}
