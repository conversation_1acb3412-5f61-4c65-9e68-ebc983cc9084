/**
 * A Svelte action to format an input field as a currency input.
 * It handles user input to ensure that the value is always a valid currency format.
 *
 * @param node - The input element to apply the action to.
 * @param initialValueInCents - The initial value of the input in cents.
 */
export function currencyInput(node: HTMLInputElement, initialValueInCents: number | undefined) {
	let valueInCents = initialValueInCents || 0;

	const formatValue = (cents: number) => {
		if (isNaN(cents) || cents === 0) {
			return '';
		}
		const dollars = cents / 100;
		// Use toLocaleString for robust, locale-aware currency formatting
		return dollars.toLocaleString('en-US', {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2
		});
	};

	const updateNodeValue = (cents: number) => {
		node.value = formatValue(cents);
	};

	const handleInput = (event: Event) => {
		// Remove all non-digit characters
		const rawValue = (event.target as HTMLInputElement).value.replace(/\D/g, '');
		const newCents = parseInt(rawValue, 10) || 0;

		if (valueInCents !== newCents) {
			valueInCents = newCents;
			updateNodeValue(valueInCents);
			node.dispatchEvent(
				new CustomEvent('change', {
					detail: { valueInCents }
				})
			);
		}
	};

	const handleBlur = () => {
		updateNodeValue(valueInCents);
	};

	const handleFocus = () => {
		if (valueInCents === 0) {
			node.value = '';
		}
	};

	// Set initial value
	updateNodeValue(valueInCents);

	// Attach event listeners
	node.addEventListener('input', handleInput);
	node.addEventListener('blur', handleBlur);
	node.addEventListener('focus', handleFocus);
	// Use keydown to handle backspace on an empty field correctly
	node.addEventListener('keydown', (e) => {
		if (e.key === 'Backspace' && node.value === '') {
			valueInCents = 0;
			node.dispatchEvent(
				new CustomEvent('change', {
					detail: { valueInCents }
				})
			);
		}
	});

	return {
		update(newInitialValue: number | undefined) {
			valueInCents = newInitialValue || 0;
			updateNodeValue(valueInCents);
		},
		destroy() {
			node.removeEventListener('input', handleInput);
			node.removeEventListener('blur', handleBlur);
			node.removeEventListener('focus', handleFocus);
		}
	};
} 