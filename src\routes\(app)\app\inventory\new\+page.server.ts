import { insertInventoryItemSchema } from '$lib/schemas/inventory';
import { db } from '$lib/server/db';
import { inventory_items } from '$lib/server/db/schema';
import { processImage } from '$lib/server/utils/imageProcessing';
import { fail } from '@sveltejs/kit';
import type { Actions } from './$types';

export const actions: Actions = {
	create: async ({ request, locals }) => {
		if (!locals.user) return fail(401, { create: { errors: { root: ['Unauthorized'] } } });

		const formData = Object.fromEntries(await request.formData());
		const validation = insertInventoryItemSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				create: { data: formData, errors: validation.error.flatten().fieldErrors }
			});
		}

		const values = validation.data;

		try {
			// Process image if provided
			let processedImageBase64: string | null = values.image_base64 || null;
			if (values.image_base64 && values.image_base64.trim() !== '') {
				try {
					processedImageBase64 = await processImage(values.image_base64, {
						maxWidth: 1600,
						maxHeight: 1200,
						quality: 80,
						format: 'jpeg'
					});
				} catch (imageError) {
					console.error('Image processing error:', imageError);
					return fail(400, {
						create: { data: formData, errors: { image_base64: ['Failed to process image. Please try a different image.'] } }
					});
				}
			} else if (values.image_base64 === '') {
				// No image provided
				processedImageBase64 = null;
			}

			const result = await db.insert(inventory_items).values({
				...values,
				image_base64: processedImageBase64,
				user_id: locals.user.id
			}).returning();

			// Return success result so toast can be shown before redirect
			return { create: { success: true, item: result[0] } };
		} catch (err) {
			console.error(err);
			return fail(500, {
				create: { data: formData, errors: { root: ['Failed to create item.'] } }
			});
		}
	}
};
