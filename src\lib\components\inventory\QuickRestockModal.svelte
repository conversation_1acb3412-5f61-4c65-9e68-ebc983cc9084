<!--
  ALPHA FEATURE: Inventory Management - Quick Restock Modal Component

  Handles barcode scanning and the quick restock workflow:
  1. Scan item barcode
  2. Zero inventory quantity
  3. Add reorder threshold amount to shopping list (or prompt for quantity)

  This component integrates the inventory and shopping list systems for streamlined restocking.
  It provides a mobile-optimized interface for scanning items and automatically managing
  the restock process without manual data entry.

  This component is part of the inventory management system that is currently in alpha status.
-->
<script lang="ts">
	import BarcodeScanner from '$lib/components/barcode/BarcodeScanner.svelte';
	import { toast } from '$lib/ui/toast';
	import IconCheck from '~icons/icon-park-outline/check';
	import IconClose from '~icons/icon-park-outline/close';
	import IconScan from '~icons/icon-park-outline/scan';
	import IconShoppingCart from '~icons/icon-park-outline/shopping-cart';

	interface Props {
		isOpen: boolean;
		onClose: () => void;
	}

	interface ScannedItem {
		id: string;
		name: string;
		description: string | null;
		barcode: string;
		current_quantity: number;
		reorder_threshold: number | null;
	}

	interface QuickRestockResponse {
		success?: boolean;
		needsQuantity?: boolean;
		item?: ScannedItem | {
			id: string;
			name: string;
			description: string | null;
			barcode: string;
			previous_quantity: number;
			quantity_added_to_shopping_list: number;
			used_reorder_threshold: boolean;
		};
		error?: string;
	}

	let { isOpen, onClose }: Props = $props();

	// Component state
	let isProcessing = $state(false);
	let showScanner = $state(false);
	let showQuantityPrompt = $state(false);
	let scannedItem = $state<ScannedItem | null>(null);
	let customQuantity = $state(1);
	let manualBarcodeInput = $state('');
	let showManualInput = $state(false);

	// Reset state when modal opens/closes
	$effect(() => {
		if (isOpen) {
			resetState();
		}
	});

	function resetState() {
		isProcessing = false;
		showScanner = false;
		showQuantityPrompt = false;
		scannedItem = null;
		customQuantity = 1;
		manualBarcodeInput = '';
		showManualInput = false;
	}

	function handleClose() {
		resetState();
		onClose();
	}

	function startScanning() {
		showScanner = true;
		showManualInput = false;
	}

	function stopScanning() {
		showScanner = false;
	}

	function toggleManualInput() {
		showManualInput = !showManualInput;
		if (showManualInput) {
			showScanner = false;
		}
	}

	async function handleBarcodeScanned(barcode: string) {
		stopScanning();
		await processBarcode(barcode);
	}

	async function handleManualBarcodeSubmit() {
		const trimmedBarcode = manualBarcodeInput.trim();
		if (!trimmedBarcode) {
			toast.error('Please enter a barcode');
			return;
		}
		await processBarcode(trimmedBarcode);
		manualBarcodeInput = '';
		showManualInput = false;
	}

	async function processBarcode(barcode: string) {
		if (isProcessing) return;

		isProcessing = true;
		try {
			const response = await fetch('/api/quick-restock', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ barcode })
			});

			const result: QuickRestockResponse = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to process quick restock');
			}

			if (result.needsQuantity && result.item) {
				// Item found but no reorder threshold - prompt for quantity
				// Type guard to ensure we have the correct item type
				if ('current_quantity' in result.item) {
					scannedItem = result.item as ScannedItem;
					showQuantityPrompt = true;
					toast.info(`Found: ${result.item.name}. Please specify quantity to add to shopping list.`);
				}
			} else if (result.success && result.item) {
				// Successfully processed
				// Type guard to ensure we have the correct item type
				if ('quantity_added_to_shopping_list' in result.item) {
					const item = result.item as {
						id: string;
						name: string;
						description: string | null;
						barcode: string;
						previous_quantity: number;
						quantity_added_to_shopping_list: number;
						used_reorder_threshold: boolean;
					};
					const message = item.used_reorder_threshold
						? `Quick restock complete! Zeroed inventory and added ${item.quantity_added_to_shopping_list} ${item.name} to shopping list (using reorder threshold).`
						: `Quick restock complete! Zeroed inventory and added ${item.quantity_added_to_shopping_list} ${item.name} to shopping list.`;

					toast.success(message);
					handleClose();
				}
			}
		} catch (error) {
			console.error('Error processing barcode:', error);
			toast.error(error instanceof Error ? error.message : 'Failed to process barcode');
		} finally {
			isProcessing = false;
		}
	}

	async function handleQuantitySubmit() {
		if (!scannedItem || customQuantity < 1) return;

		isProcessing = true;
		try {
			const response = await fetch('/api/quick-restock', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					barcode: scannedItem.barcode,
					quantity: customQuantity
				})
			});

			const result: QuickRestockResponse = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to process quick restock');
			}

			if (result.success) {
				toast.success(`Quick restock complete! Zeroed inventory and added ${customQuantity} ${scannedItem.name} to shopping list.`);
				handleClose();
			}
		} catch (error) {
			console.error('Error processing quantity:', error);
			toast.error(error instanceof Error ? error.message : 'Failed to process restock');
		} finally {
			isProcessing = false;
		}
	}
</script>

<!-- Modal -->
{#if isOpen}
	<div class="modal modal-open fixed inset-0 z-50">
		<div class="modal-box w-11/12 max-w-2xl">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<h3 class="font-bold text-xl flex items-center gap-3">
					<div class="bg-secondary/10 p-2 rounded-full">
						<IconScan class="h-6 w-6 text-secondary" />
					</div>
					Quick Restock
				</h3>
				<button class="btn btn-sm btn-circle btn-ghost" onclick={handleClose}>
					<IconClose class="h-4 w-4" />
				</button>
			</div>

			<!-- Content -->
			{#if showQuantityPrompt && scannedItem}
				<!-- Quantity Prompt -->
				<div class="space-y-6">
					<div class="alert alert-info">
						<IconShoppingCart class="h-5 w-5" />
						<div>
							<h4 class="font-semibold">Item Found: {scannedItem.name}</h4>
							<p class="text-sm">This item doesn't have a reorder threshold set. Please specify how many to add to your shopping list.</p>
						</div>
					</div>

					<div class="card bg-base-200">
						<div class="card-body">
							<h4 class="card-title text-lg">{scannedItem.name}</h4>
							{#if scannedItem.description}
								<p class="text-base-content/70">{scannedItem.description}</p>
							{/if}
							<div class="stats stats-horizontal shadow">
								<div class="stat">
									<div class="stat-title">Current Inventory</div>
									<div class="stat-value text-2xl">{scannedItem.current_quantity}</div>
								</div>
							</div>
						</div>
					</div>

					<div class="form-control">
						<label class="floating-label">
							<input
								type="number"
								class="input input-bordered w-full"
								placeholder="1"
								min="1"
								bind:value={customQuantity}
								disabled={isProcessing}
							/>
							<span>Quantity to Add to Shopping List</span>
						</label>
					</div>

					<div class="flex gap-3 justify-end">
						<button class="btn btn-ghost" onclick={() => showQuantityPrompt = false} disabled={isProcessing}>
							Back
						</button>
						<button 
							class="btn btn-primary" 
							onclick={handleQuantitySubmit}
							disabled={isProcessing || customQuantity < 1}
						>
							{#if isProcessing}
								<span class="loading loading-spinner loading-sm"></span>
								Processing...
							{:else}
								<IconCheck class="h-4 w-4" />
								Add to Shopping List
							{/if}
						</button>
					</div>
				</div>
			{:else}
				<!-- Scanner Interface -->
				<div class="space-y-6">
					<div class="text-center">
						<p class="text-base-content/70 mb-4">
							Scan an item's barcode to quickly zero its inventory and add the reorder amount to your shopping list.
						</p>
					</div>

					<!-- Scanner Controls -->
					<div class="flex flex-col gap-3">
						{#if !showScanner && !showManualInput}
							<button class="btn btn-primary btn-lg" onclick={startScanning} disabled={isProcessing}>
								<IconScan class="h-5 w-5" />
								Start Barcode Scanner
							</button>
							<button class="btn btn-outline" onclick={toggleManualInput} disabled={isProcessing}>
								Enter Barcode Manually
							</button>
						{/if}

						{#if showScanner}
							<div class="space-y-4">
								<BarcodeScanner
									isActive={showScanner}
									onScan={handleBarcodeScanned}
									onError={(error) => {
										console.error('Scanner error:', error);
										toast.error('Scanner error: ' + error);
									}}
								/>
								<div class="flex gap-2 justify-center">
									<button class="btn btn-ghost" onclick={stopScanning}>
										Stop Scanner
									</button>
									<button class="btn btn-outline btn-sm" onclick={toggleManualInput}>
										Enter Manually
									</button>
								</div>
							</div>
						{/if}

						{#if showManualInput}
							<div class="space-y-4">
								<div class="form-control">
									<label class="floating-label">
										<input
											type="text"
											class="input input-bordered w-full"
											placeholder="Enter barcode..."
											bind:value={manualBarcodeInput}
											disabled={isProcessing}
											onkeydown={(e) => {
												if (e.key === 'Enter') {
													handleManualBarcodeSubmit();
												}
											}}
										/>
										<span>Barcode</span>
									</label>
								</div>
								<div class="flex gap-2 justify-center">
									<button class="btn btn-ghost" onclick={toggleManualInput}>
										Cancel
									</button>
									<button 
										class="btn btn-primary" 
										onclick={handleManualBarcodeSubmit}
										disabled={isProcessing || !manualBarcodeInput.trim()}
									>
										{#if isProcessing}
											<span class="loading loading-spinner loading-sm"></span>
											Processing...
										{:else}
											Process Barcode
										{/if}
									</button>
								</div>
							</div>
						{/if}
					</div>

					{#if isProcessing && !showQuantityPrompt}
						<div class="flex items-center justify-center py-8">
							<span class="loading loading-spinner loading-lg"></span>
						</div>
					{/if}
				</div>
			{/if}
		</div>
		<div
			class="modal-backdrop"
			onclick={handleClose}
			onkeydown={(e) => {
				if (e.key === 'Escape') {
					handleClose();
				}
			}}
			role="button"
			tabindex="0"
			aria-label="Close modal"
		></div>
	</div>
{/if}
