import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async () => {
	// ALPHA FEATURE: Inventory management system
	// This feature has been moved to alpha status and is not available in the beta release.
	// The complete inventory system includes:
	// - Item management with barcode scanning
	// - Quantity tracking and reorder thresholds
	// - Cost/price tracking for purchase vs selling prices
	// - Image upload and storage
	// - Integration with shopping list for restocking
	// - Quick restock workflow via barcode scanning
	//
	// Database schema and validation schemas are preserved for future re-implementation.
	// All route files and components remain in the codebase but are inaccessible via navigation.
	throw redirect(302, '/app');
};
