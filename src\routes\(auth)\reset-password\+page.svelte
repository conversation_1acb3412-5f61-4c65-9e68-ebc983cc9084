<!--
This Svelte component is for resetting the user's password.
It expects a token in the URL (managed by +page.server.ts and +layout.server.ts in parent or current dir) which is implicitly passed along with the form submission.
Users can enter and submit their new password.
It uses Svelte Runes for reactivity and $props() to access form data and page data (like the token) from server actions.
The `enhance` function from $app/forms is used for progressive enhancement of the form.
Error messages or success messages (form?.message) from the server are displayed.
-->
<script lang="ts">
	/**
	 * - Handles form submission with progressive enhancement, providing immediate feedback.
	 * - Displays success or error messages using toasts.
	 */
	import { enhance } from '$app/forms';
	import Errors from '$lib/runes-form/Errors.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { resetPasswordSchema } from '$lib/schemas/auth';
	import { toast } from '$lib/ui/toast';
	import Icon<PERSON><PERSON> from '~icons/icon-park-outline/key';
	import type { ActionData, PageData } from './$types.js';

	let { data, form: actionForm }: { data: PageData; form?: ActionData } = $props();

	const form = createRuneForm(resetPasswordSchema, {
		password: '',
		confirm_password: ''
	});

	$effect(() => {
		// Zod validation errors
		if (actionForm?.errors) {
			form.setErrors(actionForm.errors);
			const errorCount = Object.values(actionForm.errors).filter((e) => e).length;
			if (errorCount > 0) {
				const fieldErrors = Object.keys(actionForm.errors).filter((k) => k !== 'root').length;
				if (fieldErrors > 0) {
					toast.error(`Please correct the ${fieldErrors} error(s) below.`);
				}
			}
		}
	});
</script>

<div class="hero bg-base-200 min-h-screen">
	<div class="hero-content">
		<div class="card glass bg-base-100/50 w-full max-w-md shrink-0 shadow-xl">
			<div class="card-body">
				<h1 class="card-title text-2xl">Reset Your Password</h1>
				<p class="text-base-content/70">Enter a new password for your account.</p>
				<div class="divider"></div>

				<RuneForm {form}>
					{#snippet children(form)}
						<form
							method="POST"
							class="space-y-4"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ update }) => {
									await update();
									form.setSubmitting(false);
								};
							}}
							onsubmit={form.handleSubmit()}
						>
							<input type="hidden" name="token" value={data.token} />
							<div class="form-control">
								<label for="password" class="label"
									><span class="label-text">New Password</span></label
								>
								<div class="relative">
									<span
										class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
									>
										<IconKey class="text-base-content/40 h-5 w-5" />
									</span>
									<input
										id="password"
										name="password"
										type="password"
										class="input input-bordered w-full pl-10"
										bind:value={form.values.password}
										onblur={() => form.setTouched('password', true)}
									/>
								</div>
								<Errors name="password" />
							</div>

							<div class="form-control">
								<label for="confirm_password" class="label"
									><span class="label-text">Confirm New Password</span></label
								>
								<div class="relative">
									<span
										class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
									>
										<IconKey class="text-base-content/40 h-5 w-5" />
									</span>
									<input
										id="confirm_password"
										name="confirm_password"
										type="password"
										class="input input-bordered w-full pl-10"
										bind:value={form.values.confirm_password}
										onblur={() => form.setTouched('confirm_password', true)}
									/>
								</div>
								<Errors name="confirm_password" />
							</div>

							<div class="mt-2">
								<Errors name="root" />
							</div>

							<div class="form-control mt-6">
								<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
										Resetting...
									{:else}
										Reset Password
									{/if}
								</button>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			</div>
		</div>
	</div>
</div>
