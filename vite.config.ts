/// <reference types="vitest" />
import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';
import { svelteTesting } from '@testing-library/svelte/vite';
import fs from 'fs';
import path from 'path';
import Icons from 'unplugin-icons/vite';
import { defineConfig, type UserConfig } from 'vite';

export default defineConfig(({ command }) => {
	const config: UserConfig = {
		mode: process.env.MODE,
		plugins: [
			tailwindcss(),
			sveltekit(),
			Icons({
				compiler: 'svelte',
				autoInstall: true
			})
		],
		test: {
			workspace: [
				{
					extends: './vite.config.ts',
					plugins: [svelteTesting()],
					test: {
						name: 'client',
						environment: 'jsdom',
						clearMocks: true,
						include: ['src/**/*.svelte.{test,spec}.{js,ts}'],
						exclude: ['src/lib/server/auth/**'],
						setupFiles: ['./vitest-setup-client.ts']
					}
				},
				{
					extends: './vite.config.ts',
					test: {
						name: 'server',
						environment: 'node',
						include: ['src/**/*.{test,spec}.{js,ts}'],
						exclude: ['src/**/*.svelte.{test,spec}.{js,ts}']
					}
				}
			]
		}
	};

	if (command === 'serve') {
		config.server = {
			host: true,
			https: {
				key: fs.readFileSync(path.resolve(__dirname, 'certs/192-168-2-10.nip.io-key.pem')),
				cert: fs.readFileSync(path.resolve(__dirname, 'certs/192-168-2-10.nip.io.pem'))
			}
		};
	}

	return config;
});
