<!--
ALP<PERSON> FEATURE: Shopping List Management - Main Shopping List Page

This page provided the main shopping list interface including:
- Adding items from inventory to shopping list with quantity management
- Purchase tracking and completion workflow
- Cost estimation using purchase prices vs selling prices
- Bulk operations for completing shopping and updating inventory
- Search and filtering capabilities (purchased vs unpurchased items)
- Integration with inventory for automatic restocking after shopping completion
- Mobile-optimized responsive design with touch-friendly controls
- Reorder suggestions based on inventory thresholds

This page is part of the shopping list system that is currently in alpha status.
All functionality remains intact but is inaccessible via navigation.
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { addShoppingListItemSchema } from '$lib/schemas/shoppingList';
	import { toast } from '$lib/ui/toast';
	import { formatCurrency } from '$lib/utils/format';
	import IconInventory from '~icons/icon-park-outline/box';
	import IconCheck from '~icons/icon-park-outline/check-one';
	import IconTrash from '~icons/icon-park-outline/delete';
	import IconEdit from '~icons/icon-park-outline/edit';
	import IconPlus from '~icons/icon-park-outline/plus';
	import IconShoppingCart from '~icons/icon-park-outline/shopping-cart-one';
	import type { ActionData, PageData } from './$types';

	type ShoppingListItem = PageData['shoppingListItems'][number];

	let { data, form }: { data: PageData; form: ActionData } = $props();

	// Filter state
	let showPurchased = $state(true);
	let searchTerm = $state('');

	// Form state
	let showAddForm = $state(false);
	let editingQuantity: string | null = $state(null);
	let tempQuantities: Record<string, number> = $state({});

	// Filtered shopping list items
	let filteredItems: ShoppingListItem[] = $state([]);

	$effect(() => {
		let items = data.shoppingListItems;

		// Filter by purchased status
		if (!showPurchased) {
			items = items.filter((item) => !item.purchased);
		}

		// Filter by search term
		if (searchTerm.trim()) {
			const search = searchTerm.toLowerCase();
			items = items.filter(
				(item) =>
					item.inventory_item.name.toLowerCase().includes(search) ||
					item.inventory_item.description?.toLowerCase().includes(search) ||
					item.inventory_item.sku?.toLowerCase().includes(search)
			);
		}
		filteredItems = items;
	});

	// Summary stats
	let totalItems = $derived(data.shoppingListItems.length);
	let purchasedItems = $derived(data.shoppingListItems.filter(item => item.purchased).length);
	let unpurchasedItems = $derived(totalItems - purchasedItems);
	let estimatedTotal = $derived(getEstimatedTotal());
	let purchasedTotal = $derived(getPurchasedTotal());

	// Add item form
	const addForm = createRuneForm(addShoppingListItemSchema, {
		inventory_item_id: '',
		quantity: 1
	});

	// Handle form submission results
	$effect(() => {
		if (form?.success) {
			toast.success('Shopping list updated successfully');
			addForm.reset();
			showAddForm = false;
			editingQuantity = null;
			tempQuantities = {};
		} else if (form?.message && typeof form.message === 'string') {
			toast.error(form.message);
		}
	});

	function startEditQuantity(itemId: string, currentQuantity: number) {
		editingQuantity = itemId;
		tempQuantities[itemId] = currentQuantity;
	}

	function cancelEditQuantity() {
		editingQuantity = null;
		tempQuantities = {};
	}

	function getEstimatedTotal() {
		return data.shoppingListItems
			.filter(item => !item.purchased)
			.reduce((total, item) => {
				// Use purchase cost if available, otherwise fallback to selling price
				const cost = item.inventory_item.cost_in_cents ?? item.inventory_item.price_in_cents ?? 0;
				return total + (cost * item.quantity);
			}, 0);
	}

	function getPurchasedTotal() {
		return data.shoppingListItems
			.filter(item => item.purchased)
			.reduce((total, item) => {
				// Use purchase cost if available, otherwise fallback to selling price
				const cost = item.inventory_item.cost_in_cents ?? item.inventory_item.price_in_cents ?? 0;
				return total + (cost * item.quantity);
			}, 0);
	}
</script>

<div class="space-y-6">
	<!-- Header with stats and controls -->
	<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
		<div>
			<div class="flex items-center gap-3 mb-2">
				<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
				<h1 class="text-2xl font-semibold text-base-content/90 flex items-center gap-2">
					<IconShoppingCart class="h-5 w-5" />
					Shopping List
				</h1>
			</div>
			<div class="flex gap-4 text-sm text-base-content/60 ml-5">
				<span>{totalItems} total</span>
				<span>{purchasedItems} purchased</span>
				<span>{unpurchasedItems} remaining</span>
			</div>
			<div class="flex gap-4 text-sm font-medium mt-2 ml-5">
				<span class="text-primary/80">Budget: {formatCurrency(estimatedTotal)}</span>
				{#if purchasedTotal > 0}
					<span class="text-success/80">Spent: {formatCurrency(purchasedTotal)}</span>
				{/if}
			</div>
			<div class="text-xs text-base-content/50 mt-1 ml-5">
				* Costs based on purchase prices where available, selling prices otherwise
			</div>
		</div>
		
		<!-- Mobile-Optimized Action Buttons -->
		<div class="flex flex-col gap-2 sm:flex-row sm:flex-wrap">
			<button
				class="btn btn-outline btn-lg sm:btn-sm w-full sm:w-auto"
				onclick={() => showAddForm = !showAddForm}
			>
				<IconPlus class="h-4 w-4" />
				Add Item
			</button>
			<a href="/app/inventory/new" class="btn btn-primary btn-lg sm:btn-sm w-full sm:w-auto">
				<IconInventory class="h-4 w-4" />
				New Inventory Item
			</a>

			<!-- Mobile-Optimized Bulk Actions -->
			{#if unpurchasedItems > 0}
				<form method="POST" action="?/bulkMarkPurchased" use:enhance={() => {
					return async ({ result, update }) => {
						await update();
						if (result.type === 'success') {
							await invalidateAll();
							toast.success('All items marked as purchased');
						}
					};
				}}>
					<button type="submit" class="btn btn-success btn-lg sm:btn-sm w-full sm:w-auto">
						<IconCheck class="h-4 w-4" />
						Mark All Purchased
					</button>
				</form>

				<form method="POST" action="?/bulkCompleteAndUpdateInventory" use:enhance={() => {
					return async ({ result, update }) => {
						await update();
						if (result.type === 'success') {
							await invalidateAll();
							toast.success('Shopping completed and inventory updated');
						}
					};
				}}>
					<button type="submit" class="btn btn-success btn-lg sm:btn-sm w-full sm:w-auto transition-colors duration-200">
						<IconInventory class="h-4 w-4" />
						<span class="hidden sm:inline">Complete & Update Inventory</span>
						<span class="sm:hidden">Complete Shopping</span>
					</button>
				</form>
			{/if}

			{#if purchasedItems > 0}
				<form method="POST" action="?/bulkClearPurchased" use:enhance={() => {
					return async ({ result, update }) => {
						await update();
						if (result.type === 'success') {
							await invalidateAll();
							toast.success('Purchased items cleared');
						}
					};
				}}>
					<button type="submit" class="btn btn-outline btn-lg sm:btn-sm w-full sm:w-auto">
						<IconTrash class="h-4 w-4" />
						Clear Purchased
					</button>
				</form>
			{/if}
		</div>
	</div>

	<!-- Reorder Suggestions -->
	{#if data.reorderSuggestions && data.reorderSuggestions.length > 0}
		<div class="alert alert-warning">
			<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
			</svg>
			<div class="flex-1">
				<h3 class="font-bold">Items Need Reordering</h3>
				<div class="text-sm">
					{data.reorderSuggestions.length} item{data.reorderSuggestions.length === 1 ? '' : 's'} below reorder threshold:
					{data.reorderSuggestions.map(item => item.name).join(', ')}
				</div>
			</div>
			<form method="POST" action="?/addReorderSuggestions" use:enhance={() => {
				return async ({ result, update }) => {
					await update();
					if (result.type === 'success') {
						await invalidateAll();
						toast.success('Reorder suggestions added to shopping list');
					}
				};
			}}>
				<button type="submit" class="btn btn-sm">
					<IconPlus class="h-4 w-4" />
					Add to List
				</button>
			</form>
		</div>
	{/if}

	<!-- Search and filter controls -->
	<div class="flex flex-col gap-4 sm:flex-row sm:items-center">
		<input
			type="search"
			placeholder="Search shopping list..."
			class="input input-bordered flex-1"
			bind:value={searchTerm}
		/>
		<div class="form-control">
			<label class="label cursor-pointer gap-2">
				<input 
					type="checkbox" 
					class="checkbox checkbox-sm" 
					bind:checked={showPurchased}
				/>
				<span class="label-text">Show purchased items</span>
			</label>
		</div>
	</div>

	<!-- Add item form -->
	{#if showAddForm}
		<div class="card bg-base-200 border border-dashed">
			<div class="card-body">
				<h3 class="card-title text-lg">Add Item to Shopping List</h3>
				<form 
					method="POST" 
					action="?/add"
					use:enhance={() => {
						return async ({ result, update }) => {
							await update();
							if (result.type === 'success') {
								await invalidateAll();
							}
						};
					}}
				>
					<div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
						<div class="sm:col-span-2">
							<label class="select select-bordered w-full">
								<span class="label">Select Item <span class="text-error">*</span></span>
								<select
									id="inventory_item_id"
									name="inventory_item_id"
									bind:value={addForm.values.inventory_item_id}
									required
								>
									<option value="">Choose an inventory item...</option>
									{#each data.availableInventoryItems as item}
										<option value={item.id}>
											{item.name} {item.sku ? `(${item.sku})` : ''}
										</option>
									{/each}
								</select>
							</label>
						</div>
						<div>
							<label class="floating-label">
								<input
									id="quantity"
									name="quantity"
									type="number"
									class="input input-bordered w-full"
									placeholder="1"
									min="1"
									bind:value={addForm.values.quantity}
									required
								/>
								<span>Quantity <span class="text-error">*</span></span>
							</label>
						</div>
					</div>
					<div class="card-actions justify-end mt-4">
						<button 
							type="button" 
							class="btn btn-ghost"
							onclick={() => showAddForm = false}
						>
							Cancel
						</button>
						<button type="submit" class="btn btn-primary">
							Add to List
						</button>
					</div>
				</form>
			</div>
		</div>
	{/if}

	<!-- Shopping list items -->
	{#if filteredItems.length > 0}
		<div class="space-y-3">
			{#each filteredItems as item (item.id)}
				{@const itemCost = item.inventory_item.cost_in_cents ?? item.inventory_item.price_in_cents ?? 0}
				{@const hasCost = item.inventory_item.cost_in_cents !== undefined}
				<div class="card bg-base-100 shadow-sm border" class:opacity-60={item.purchased}>
					<div class="card-body p-3 lg:p-4">
						<div class="flex items-start gap-3 lg:gap-4">
							<!-- Purchased checkbox -->
							<form
								method="POST"
								action="?/togglePurchased"
								use:enhance={() => {
									return async ({ result, update }) => {
										await update();
										if (result.type === 'success') {
											await invalidateAll();
										}
									};
								}}
							>
								<input type="hidden" name="id" value={item.id} />
								<input type="hidden" name="purchased" value={(!item.purchased).toString()} />
								<button type="submit" class="btn btn-circle btn-sm btn-ghost">
									{#if item.purchased}
										<IconCheck class="h-5 w-5 text-success" />
									{:else}
										<div class="w-5 h-5 border-2 border-base-content/30 rounded"></div>
									{/if}
								</button>
							</form>

							<!-- Item image -->
							<div class="avatar">
								<div class="w-12 h-12 rounded">
									{#if item.inventory_item.image_base64}
										<img src={item.inventory_item.image_base64} alt={item.inventory_item.name} />
									{:else}
										<div class="bg-base-200 flex items-center justify-center w-full h-full">
											<IconInventory class="h-6 w-6 text-base-content/50" />
										</div>
									{/if}
								</div>
							</div>

							<!-- Mobile-Optimized Item Details -->
							<div class="flex-1 min-w-0">
								<!-- First Line: Item Name -->
								<h3 class="font-semibold text-sm lg:text-base leading-tight mb-1" class:line-through={item.purchased}>
									{item.inventory_item.name}
								</h3>

								<!-- Second Line: Price and Quantity Info -->
								<div class="flex flex-wrap items-center gap-2 text-xs lg:text-sm text-base-content/70">
									{#if item.inventory_item.sku}
										<span class="badge badge-outline badge-xs">{item.inventory_item.sku}</span>
									{/if}
									<span class="font-medium" title={hasCost ? "Purchase Cost" : "Selling Price (no purchase cost set)"}>{formatCurrency(itemCost)}</span>
									<span class="text-xs">× {item.quantity}</span>
									<span class="font-semibold text-primary" title="Total Shopping Cost">{formatCurrency(itemCost * item.quantity)}</span>
								</div>

								<!-- Third Line: Description (if available) -->
								{#if item.inventory_item.description}
									<p class="text-xs lg:text-sm text-base-content/60 line-clamp-1 mt-1">{item.inventory_item.description}</p>
								{/if}
							</div>

							<!-- Action Buttons Area -->
							<div class="flex flex-col lg:flex-row items-end lg:items-center gap-2 lg:gap-3 shrink-0">
								<!-- Quantity controls -->
								<div class="flex items-center gap-1 lg:gap-2">
									{#if editingQuantity === item.id}
										<form
											method="POST"
											action="?/update"
											use:enhance={() => {
												return async ({ result, update }) => {
													await update();
													if (result.type === 'success') {
														editingQuantity = null;
														tempQuantities = {};
														await invalidateAll();
													}
												};
											}}
										>
											<input type="hidden" name="id" value={item.id} />
											<input
												type="number"
												name="quantity"
												min="1"
												class="input input-bordered input-xs lg:input-sm w-16 lg:w-20"
												bind:value={tempQuantities[item.id]}
												onkeydown={(e) => {
													if (e.key === 'Enter') {
														e.currentTarget.form?.requestSubmit();
													} else if (e.key === 'Escape') {
														cancelEditQuantity();
													}
												}}
											/>
											<button type="submit" class="btn btn-xs lg:btn-sm btn-ghost">
												<IconCheck class="h-3 w-3 lg:h-4 lg:w-4" />
											</button>
										</form>
										<button
											type="button"
											class="btn btn-xs lg:btn-sm btn-ghost"
											onclick={cancelEditQuantity}
										>
											×
										</button>
									{:else}
										<button
											class="btn btn-xs lg:btn-sm btn-ghost"
											onclick={() => startEditQuantity(item.id, item.quantity)}
										>
											<IconEdit class="h-3 w-3 lg:h-4 lg:w-4" />
										</button>
										<span class="font-mono text-xs lg:text-sm min-w-[2.5rem] lg:min-w-[3rem] text-center">
											Qty: {item.quantity}
										</span>
									{/if}
								</div>

								<!-- Action Buttons -->
								<div class="flex items-center gap-1 lg:gap-2">
									<!-- Complete with inventory update button -->
									{#if !item.purchased}
										<form
											method="POST"
											action="?/completeAndUpdateInventory"
											use:enhance={() => {
												return async ({ result, update }) => {
													await update();
													if (result.type === 'success') {
														await invalidateAll();
														toast.success('Item completed and inventory updated');
													}
												};
											}}
										>
											<input type="hidden" name="id" value={item.id} />
											<input type="hidden" name="updateInventory" value="true" />
											<button type="submit" class="btn btn-success btn-xs lg:btn-sm" title="Mark purchased and add to inventory">
												<IconInventory class="h-3 w-3 lg:h-4 lg:w-4" />
												<IconCheck class="h-2 w-2 lg:h-3 lg:w-3" />
											</button>
										</form>
									{/if}

									<!-- Remove button -->
									<form
										method="POST"
										action="?/remove"
										use:enhance={() => {
											return async ({ result, update }) => {
												await update();
												if (result.type === 'success') {
													await invalidateAll();
												}
											};
										}}
									>
										<input type="hidden" name="id" value={item.id} />
										<button
											type="submit"
											class="btn btn-xs lg:btn-sm btn-ghost text-error hover:bg-error/20"
											onclick={(e) => {
												if (!confirm('Remove this item from your shopping list?')) {
													e.preventDefault();
												}
											}}
										>
											<IconTrash class="h-3 w-3 lg:h-4 lg:w-4" />
										</button>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Summary -->
		{#if unpurchasedItems > 0}
			<div class="card bg-primary/10 border border-primary/20">
				<div class="card-body p-4">
					<div class="flex justify-between items-center">
						<span class="font-semibold">Shopping Total ({unpurchasedItems} items):</span>
						<span class="text-lg font-bold">{formatCurrency(getEstimatedTotal())}</span>
					</div>
				</div>
			</div>
		{/if}
	{:else}
		<div class="card bg-base-200 border border-dashed">
			<div class="card-body items-center text-center">
				<IconShoppingCart class="h-16 w-16 text-base-content/30" />
				<h2 class="card-title">
					{#if searchTerm}
						No items match your search
					{:else if !showPurchased && purchasedItems > 0}
						All items purchased!
					{:else}
						Your shopping list is empty
					{/if}
				</h2>
				<p class="text-base-content/70">
					{#if searchTerm}
						Try adjusting your search term or clearing the filter.
					{:else if !showPurchased && purchasedItems > 0}
						Great job! You can view purchased items by checking "Show purchased items" above.
					{:else}
						Add items from your inventory to start building your shopping list.
					{/if}
				</p>
				<div class="card-actions justify-center gap-2 mt-4">
					{#if !showAddForm && data.availableInventoryItems.length > 0}
						<button class="btn btn-primary" onclick={() => showAddForm = true}>
							<IconPlus class="h-4 w-4" />
							Add Items
						</button>
					{/if}
					<a href="/app/inventory/new" class="btn btn-outline">
						<IconInventory class="h-4 w-4" />
						New Inventory Item
					</a>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.line-clamp-1 {
		display: -webkit-box;
		line-clamp: 1;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>