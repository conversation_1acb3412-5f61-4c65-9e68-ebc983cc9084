import { db } from '$lib/server/db';
import { webauthn_credentials } from '$lib/server/db/schema';
import { json } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

// Get user's WebAuthn credentials
export const GET: RequestHandler = async ({ locals }) => {
	// Check if user is authenticated
	if (!locals.user) {
		return new Response('Unauthorized', { status: 401 });
	}

	try {
		const credentials = await db.query.webauthn_credentials.findMany({
			where: eq(webauthn_credentials.user_id, locals.user.id),
			orderBy: (webauthn_credentials, { desc }) => [desc(webauthn_credentials.created_at)]
		});

		// Don't return sensitive fields like public key
		const sanitizedCredentials = credentials.map((cred) => ({
			id: cred.id,
			device_type: cred.device_type,
			transports: cred.transports,
			created_at: cred.created_at,
			last_used_at: cred.last_used_at,
			name: `Passkey (${cred.device_type || 'Device'})`
		}));

		return json({ credentials: sanitizedCredentials });
	} catch (err) {
		console.error('Error fetching WebAuthn credentials:', err);
		return json({ error: 'Failed to fetch credentials' }, { status: 500 });
	}
};
