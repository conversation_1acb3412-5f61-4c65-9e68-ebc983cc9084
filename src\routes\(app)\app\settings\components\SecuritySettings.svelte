<!--
	@component
	Security Settings Module - Handles password management, passkeys, and account security
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { registerPasskey } from '$lib/client/webauthn.js';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { updatePasswordSchema } from '$lib/schemas/settings';
	import { addPasskeySchema, deletePasskeySchema } from '$lib/schemas/webauthn.js';
	import { toast } from '$lib/ui/toast';
	import IconKey from '~icons/icon-park-outline/key';
	import IconLock from '~icons/icon-park-outline/lock';
	import type { ActionData, PageData } from '../$types';
	import { goto } from '$app/navigation';
	import ReauthenticationModal from '$lib/components/auth/ReauthenticationModal.svelte';

	interface Props {
		data: PageData;
		actionData?: ActionData;
	}

	let { data, actionData }: Props = $props();

	const passwordForm = createRuneForm(updatePasswordSchema, {
		current_password: '',
		new_password: '',
		confirm_password: ''
	});
	const addPasskeyForm = createRuneForm(addPasskeySchema, { name: '' });
	const deletePasskeyForm = createRuneForm(deletePasskeySchema, {
		credentialId: '',
		password: ''
	});

	let isDeleteModalOpen = $state(false);
	let selectedCredentialId = $state<string | null>(null);
	let showAddPasswordForm = $state(false);
	let confirmAddPassword = $state(false);

	// State for the unified reauthentication modal
	let reauth = $state({
		show: false,
		action: '',
		title: '',
		message: '',
		onClose: (_result: { success: boolean; payload?: any }) => {}
	});

	function openDeleteModal(credentialId: string) {
		selectedCredentialId = credentialId;

		reauth = {
			show: true,
			action: `?/reauthenticateAndDeleteCredential`,
			title: 'Delete Passkey',
			message: 'To delete this passkey, please confirm your identity.',
			onClose: async (result) => {
				if (result.success && result.payload) {
					const formData = new FormData();
					formData.append('credentialId', credentialId);
					formData.append('authMethod', result.payload.authMethod);
					if (result.payload.authMethod === 'password') {
						formData.append('password', result.payload.password);
					} else {
						formData.append('authentication', JSON.stringify(result.payload.authentication));
					}

					// Use fetch to submit the form data
					const response = await fetch('?/reauthenticateAndDeleteCredential', {
						method: 'POST',
						body: formData
					});
					const actionResult = await response.json();

					// Manually process action result
					if (actionResult.type === 'success' && actionResult.data.deleteCredential.success) {
						toast.success('Passkey deleted successfully.');
					} else {
						const error =
							actionResult.data.deleteCredential?.errors?.root?.[0] || 'An unknown error occurred.';
						toast.error(`Deletion failed: ${error}`);
					}
				}
				reauth.show = false;
			}
		};
	}

	// Handle action results
	$effect(() => {
		if (!actionData) return;

		// Password Form
		if ('password' in actionData && actionData.password) {
			const password = actionData.password;
			if ('errors' in password && password.errors) {
				passwordForm.setErrors(password.errors);
				if ('root' in password.errors && password.errors.root) {
					toast.error(password.errors.root[0]);
				} else {
					toast.error('Please correct the password errors.');
				}
			} else if ('success' in password && password.success) {
				toast.success('Password updated. Please log in again.');
				passwordForm.reset();
			}
		}

		// Handle Delete Credential Action
		if ('deleteCredential' in actionData && actionData.deleteCredential) {
			const result = actionData.deleteCredential;
			if (result && typeof result === 'object') {
				if ('errors' in result && result.errors) {
					deletePasskeyForm.setErrors(result.errors as any);
					const rootError = (result.errors as any).root?.[0];
					toast.error(rootError || 'Please correct the errors below.');
				} else if ('success' in result && result.success) {
					toast.success('Passkey deleted successfully.');
					isDeleteModalOpen = false;
					selectedCredentialId = null;
				}
			}
		}

		// Handle Add Passkey Action
		if ('add-passkey' in actionData && actionData['add-passkey']) {
			const result = actionData['add-passkey'];
			if (result && typeof result === 'object') {
				if ('errors' in result && result.errors) {
					addPasskeyForm.setErrors(result.errors as any);
					const rootError = (result.errors as any).root?.[0];
					toast.error(rootError || 'Please correct the device name.');
				} else if ('success' in result && result.success && 'name' in result && result.name) {
					registerPasskey(result.name as string).catch((err: any) => {
						console.error('Registration failed after server validation', err);
					});
					addPasskeyForm.reset();
				}
			}
		}
	});
</script>

<div class="space-y-6">
	<!-- Password Management -->
	{#if data.hasPassword}
		<div class="collapse collapse-arrow bg-base-200/50">
			<input type="checkbox" />
			<div class="collapse-title text-xl font-medium">
				<IconLock class="mr-2 h-5 w-5 inline" />
				Change Password
			</div>
			<div class="collapse-content">
				<RuneForm form={passwordForm}>
					{#snippet children(form)}
						<form
							action="?/updatePassword"
							method="POST"
							class="space-y-4"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ result, update }) => {
									await update();
									form.setSubmitting(false);
									if (result.type === 'success') {
										form.reset();
									}
								};
							}}
							onsubmit={form.handleSubmit()}
						>
							<div class="join join-vertical w-full">
								<Field name="current_password">
									{#snippet children(field)}
										<div class="join-item">
											<label class="floating-label">
												<input
													type="password"
													id="current_password"
													name="current_password"
													class="input input-bordered w-full"
													placeholder="••••••••"
													value={String(field.value ?? '')}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<span>Current Password</span>
											</label>
											<Errors name="current_password" />
										</div>
									{/snippet}
								</Field>
								<Field name="new_password">
									{#snippet children(field)}
										<div class="join-item">
											<label class="floating-label">
												<input
													type="password"
													id="new_password"
													name="new_password"
													class="input input-bordered w-full"
													placeholder="••••••••"
													value={String(field.value ?? '')}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<span>New Password</span>
											</label>
											<Errors name="new_password" />
										</div>
									{/snippet}
								</Field>
								<Field name="confirm_password">
									{#snippet children(field)}
										<div class="join-item">
											<label class="floating-label">
												<input
													type="password"
													id="new_password_confirm"
													name="confirm_password"
													class="input input-bordered w-full"
													placeholder="••••••••"
													value={String(field.value ?? '')}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<span>Confirm New Password</span>
											</label>
											<Errors name="confirm_password" />
										</div>
									{/snippet}
								</Field>
							</div>
							<Errors name="root" />
							<div class="flex justify-end">
								<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
										Changing...
									{:else}
										Change Password
									{/if}
								</button>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			</div>
		</div>
	{/if}

	<!-- Passkey Management -->
	<div class="collapse collapse-arrow bg-base-200/50">
		<input type="checkbox" checked />
		<div class="collapse-title text-xl font-medium">
			<IconKey class="mr-2 h-5 w-5 inline" />
			Passkeys (WebAuthn)
		</div>
		<div class="collapse-content">
			<p class="text-base-content/70 text-sm mb-4">
				Add or manage your passkeys for passwordless sign-in.
			</p>

			<!-- Add New Passkey -->
			<div class="card bg-base-100/50 mb-6">
				<div class="card-body">
					<h3 class="card-title text-lg">Add New Passkey</h3>
					<RuneForm form={addPasskeyForm}>
						{#snippet children(runeForm)}
							<form
								class="mt-4 flex items-start gap-2"
								method="POST"
								action="?/add-passkey"
								use:enhance={() => {
									runeForm.setSubmitting(true);
									return async ({ update }) => {
										await update({ reset: false });
										runeForm.setSubmitting(false);
									};
								}}
								onsubmit={runeForm.handleSubmit()}
							>
								<Field name="name">
									{#snippet children(field)}
										<div class="form-control grow">
											<label for="name" class="label sr-only">Device Name</label>
											<div class="relative">
												<span
													class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
												>
													<IconKey class="text-base-content/40 h-5 w-5" />
												</span>
												<input
													id="name"
													name="name"
													type="text"
													placeholder="e.g., My Laptop, Work Phone"
													class="input input-bordered w-full pl-10"
													bind:value={field.value}
													onblur={field.handleBlur}
												/>
											</div>
											<Errors name="name" />
											<Errors name="root" />
										</div>
									{/snippet}
								</Field>
								<button
									type="submit"
									class="btn btn-primary h-[3rem]"
									disabled={runeForm.isSubmitting}
								>
									{#if runeForm.isSubmitting}
										<span class="loading loading-spinner"></span>
										Adding...
									{:else}
										Add Device
									{/if}
								</button>
							</form>
						{/snippet}
					</RuneForm>
				</div>
			</div>

			<!-- Registered Devices -->
			<div class="card bg-base-100/50">
				<div class="card-body">
					<h3 class="card-title text-lg">Registered Devices</h3>
					<div class="overflow-x-auto">
						{#if (data as any).credentials && (data as any).credentials.length > 0}
							<table class="table">
								<thead>
									<tr>
										<th>Device Name</th>
										<th>Added On</th>
										<th>Last Used</th>
										<th></th>
									</tr>
								</thead>
								<tbody>
									{#each (data as any).credentials as credential (credential.id)}
										<tr>
											<td class="font-medium">{credential.name ?? 'Unnamed Device'}</td>
											<td>{new Date(credential.created_at).toLocaleDateString()}</td>
											<td>
												{credential.last_used_at
													? new Date(credential.last_used_at).toLocaleDateString()
													: 'Never'}
											</td>
											<td class="text-right">
												<button
													class="btn btn-ghost btn-sm text-error"
													onclick={() => openDeleteModal(credential.credential_id)}
												>
													Delete
												</button>
											</td>
										</tr>
									{/each}
								</tbody>
							</table>
						{:else}
							<p class="text-base-content/70 py-4 text-center">
								You haven't added any passkeys yet.
							</p>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- CAUTION ZONE -->
	{#if !data.hasPassword}
		<div class="collapse collapse-arrow bg-warning/10 border-warning/20">
			<input type="checkbox" />
			<div class="collapse-title text-xl font-medium text-warning">
				⚠️ Caution Zone
			</div>
			<div class="collapse-content">
				<div class="rounded-box border-warning/50 border p-4">
					<h3 class="font-bold">Add a Password</h3>
					<div class="alert alert-warning mt-2">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-6 w-6 shrink-0 stroke-current"
							fill="none"
							viewBox="0 0 24 24"
							><path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
							></path></svg
						>
						<span
							><b>Security Warning:</b> Adding a password creates an additional, often weaker, way
							for your account to be compromised. We strongly recommend using secure passkeys as
							your only sign-in method.</span
						>
					</div>

					{#if !showAddPasswordForm}
						<div class="card-actions justify-end pt-4">
							<button
								class="btn {confirmAddPassword ? 'btn-error' : 'btn-warning'}"
								onclick={() => {
									if (confirmAddPassword) {
										showAddPasswordForm = true;
									} else {
										confirmAddPassword = true;
									}
								}}
								onblur={() => {
									confirmAddPassword = false;
								}}
							>
								{#if confirmAddPassword}
									Are you sure? This is less secure.
								{:else}
									Add a password anyways
								{/if}
							</button>
						</div>
					{:else}
						<RuneForm form={passwordForm}>
							{#snippet children(form)}
								<form
									action="?/addPassword"
									method="POST"
									class="space-y-4 pt-4"
									use:enhance={() => {
										form.setSubmitting(true);
										return async ({ result, update }) => {
											await update();
											form.setSubmitting(false);
											if (result.type === 'success') {
												form.reset();
												showAddPasswordForm = false;
												confirmAddPassword = false;
											}
										};
									}}
									onsubmit={form.handleSubmit()}
								>
									<div class="join join-vertical w-full">
										<Field name="new_password">
											{#snippet children(field)}
												<div class="form-control join-item">
													<label for="new_password_add" class="label">
														<span class="label-text">New Password</span>
													</label>
													<input
														type="password"
														id="new_password_add"
														name="new_password"
														class="input input-bordered w-full"
														value={String(field.value ?? '')}
														oninput={field.handleChange}
														onblur={field.handleBlur}
													/>
													<Errors name="new_password" />
												</div>
											{/snippet}
										</Field>
										<Field name="confirm_password">
											{#snippet children(field)}
												<div class="form-control join-item">
													<label for="new_password_confirm_add" class="label">
														<span class="label-text">Confirm New Password</span>
													</label>
													<input
														type="password"
														id="new_password_confirm_add"
														name="confirm_password"
														class="input input-bordered w-full"
														value={String(field.value ?? '')}
														oninput={field.handleChange}
														onblur={field.handleBlur}
													/>
													<Errors name="confirm_password" />
												</div>
											{/snippet}
										</Field>
									</div>
									<Errors name="root" />
									<div class="flex justify-end">
										<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
											{#if form.isSubmitting}
												<span class="loading loading-spinner"></span>
												Adding...
											{:else}
												Add Password
											{/if}
										</button>
									</div>
								</form>
							{/snippet}
						</RuneForm>
					{/if}
				</div>
			</div>
		</div>
	{/if}

	<!-- Danger Zone -->
	<div class="collapse collapse-arrow bg-error/10 border-error/20">
		<input type="checkbox" />
		<div class="collapse-title text-xl font-medium text-error">
			🗑️ Danger Zone
		</div>
		<div class="collapse-content">
			<div class="rounded-box border-error/50 border p-4">
				<h3 class="font-bold">Delete Account</h3>
				<p class="text-sm">
					Once you delete your account, there is no going back. Please be certain.
				</p>
				<div class="card-actions justify-end">
					<button
						class="btn btn-error"
						onclick={() => {
							reauth = {
								show: true,
								action: `?/reauthenticateAndDeleteAccount`,
								title: 'Delete Account',
								message:
									'This is a permanent action. To delete your account, please confirm your identity.',
								onClose: async (result) => {
									if (result.success && result.payload) {
										const formData = new FormData();
										formData.append('authMethod', result.payload.authMethod);
										if (result.payload.authMethod === 'password') {
											formData.append('password', result.payload.password);
										} else {
											formData.append(
												'authentication',
												JSON.stringify(result.payload.authentication)
											);
										}
										const response = await fetch('?/reauthenticateAndDeleteAccount', {
											method: 'POST',
											body: formData
										});

										// This will trigger a redirect on success
										if (response.redirected) {
											await goto(response.url);
										} else {
											const actionResult = await response.json();
											const error =
												actionResult.data?.delete?.errors?.root?.[0] ||
												'An unknown error occurred.';
											toast.error(`Deletion failed: ${error}`);
										}
									}
									reauth.show = false;
								}
							};
						}}
					>
						Delete My Account
					</button>
				</div>
			</div>
		</div>
	</div>
</div>

<ReauthenticationModal {...reauth} />

{#if isDeleteModalOpen}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="text-lg font-bold">Confirm Deletion</h3>
			<p class="py-4">To delete this passkey, please enter your password to confirm.</p>
			<RuneForm form={deletePasskeyForm}>
				{#snippet children(form)}
					<form
						method="POST"
						action="?/deleteCredential"
						use:enhance={() => {
							form.setSubmitting(true);
							return async ({ update }) => {
								await update({ reset: false });
								form.setSubmitting(false);
							};
						}}
						onsubmit={form.handleSubmit()}
					>
						<input type="hidden" name="credentialId" bind:value={form.values.credentialId} />
						<Field name="password">
							{#snippet children(field)}
								<div class="form-control">
									<label for="password_delete_passkey" class="label">
										<span class="label-text">Current Password</span>
									</label>
									<div class="relative">
										<span
											class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
										>
											<IconKey class="text-base-content/40 h-5 w-5" />
										</span>
										<input
											id="password_delete_passkey"
											name="password"
											type="password"
											class="input input-bordered w-full pl-10"
											bind:value={field.value}
											onblur={() => form.setTouched('password', true)}
											required
										/>
									</div>
									<Errors name="password" />
									<Errors name="root" />
								</div>
							{/snippet}
						</Field>

						<div class="modal-action">
							<button
								type="button"
								class="btn"
								onclick={() => (isDeleteModalOpen = false)}
								disabled={form.isSubmitting}>Cancel</button
							>
							<button type="submit" class="btn btn-error" disabled={form.isSubmitting}>
								{#if form.isSubmitting}
									<span class="loading loading-spinner"></span>
									Deleting...
								{:else}
									Delete Passkey
								{/if}
							</button>
						</div>
					</form>
				{/snippet}
			</RuneForm>
		</div>
		<button
			class="modal-backdrop"
			onclick={() => (isDeleteModalOpen = false)}
			aria-label="Close delete confirmation modal"
		></button>
	</div>
{/if}
