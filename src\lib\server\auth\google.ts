import { env } from '$env/dynamic/private';
import { Google } from 'arctic';

function createGoogleClient() {
	if (!env.GOOGLE_CLIENT_ID || !env.GOOGLE_CLIENT_SECRET || !env.GOOGLE_REDIRECT_URI) {
		// If you are not using Google login, you can ignore this error.
		// Otherwise, please add the required environment variables.
		// You can create a Google OAuth app at: https://console.developers.google.com/
		console.warn(
			'Google OAuth environment variables are not set. Google login will not be available.'
		);
		// Return a mock/dummy object or null to prevent crash
		return null;
	}

	return new Google(env.GOOGLE_CLIENT_ID, env.GOOGLE_CLIENT_SECRET, env.GOOGLE_REDIRECT_URI);
}

export const google = createGoogleClient();
