import { z } from 'zod/v4';
import {
    emailField,
    loginCredentialsSchema,
    registrationCredentialsSchema
} from './shared-primitives';

export const loginSchema = loginCredentialsSchema;

export const signupSchema = registrationCredentialsSchema;

export const forgotPasswordSchema = z.object({
	email: email<PERSON>ield
});

import { ERROR_MESSAGES } from '$lib/config';

export const resetPasswordSchema = z
	.object({
		password: z.string().min(8, { message: ERROR_MESSAGES.PASSWORD_TOO_SHORT }),
		confirm_password: z.string()
	})
	.refine((data) => data.password === data.confirm_password, {
		message: "Passwords don't match",
		path: ['confirm_password']
	});

export const accountRecoverySchema = z.object({
	recovery_code: z.string().min(1, { message: ERROR_MESSAGES.REQUIRED_FIELD })
});

export const useRecoveryCodeSchema = z.object({
	email: z.email({ message: 'Invalid email address' }),
	recovery_code: z.string().min(1, { message: 'Recovery code is required' })
});

export const verifyCodeSchema = z.object({
	code: z.string().length(8, 'Verification code must be 8 characters long.')
});

export const resendCodeSchema = z.object({});
