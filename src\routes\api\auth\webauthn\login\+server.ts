import {
	createSession,
	deleteWebAuthnChallengeCookie,
	generateSessionToken,
	getWebAuthnChallengeCookie,
	setSessionTokenCookie,
	setWebAuthnChallengeCookie
} from '$lib/server/auth';
import { startAuthentication, verifyAuthentication } from '$lib/server/auth/webauthn';
import { json, type HttpError } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const NO_CREDENTIALS_FOUND_MESSAGE = 'No WebAuthn credentials found for this user.';
const USER_NOT_FOUND_MESSAGE = 'User not found. Consider generic error for security.';
const NO_CREDENTIALS_FOUND_ERROR = 'No passkeys registered for this user.';

// Step 1: Begin authentication process
export const POST: RequestHandler = async (event) => {
	try {
		const options = await startAuthentication();
		setWebAuthnChallengeCookie(event, options.challenge);
		return json({ options });
	} catch (err) {
		console.error('Error starting WebAuthn authentication:', err);
		return json({ error: 'Failed to start authentication' }, { status: 500 });
	}
};

// Step 2: Verify and complete authentication
export const PUT: RequestHandler = async (event) => {
	const { request } = event;
	const challenge = getWebAuthnChallengeCookie(event);

	if (!challenge) {
		return json({ error: 'Missing challenge cookie' }, { status: 400 });
	}

	try {
		const response = await request.json();

		const result = await verifyAuthentication(response, challenge);

		// Clean up the cookie regardless of the outcome
		deleteWebAuthnChallengeCookie(event);

		if (result.verified) {
			const sessionToken = generateSessionToken();
			const session = await createSession(sessionToken, result.userId);
			setSessionTokenCookie(event, sessionToken, session.expires_at);
			return json({ success: true, message: 'Authentication successful' });
		} else {
			return json({ error: 'Authentication failed' }, { status: 401 });
		}
	} catch (err) {
		const httpError = err as HttpError;
		console.error('Error verifying WebAuthn authentication:', err);

		// Clean up the cookie
		deleteWebAuthnChallengeCookie(event);

		const errorMessage =
			httpError.body?.message || 'An unknown error occurred during authentication.';
		return json({ error: errorMessage }, { status: httpError.status || 500 });
	}
};
