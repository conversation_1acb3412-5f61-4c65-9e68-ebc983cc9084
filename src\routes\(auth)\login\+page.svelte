<!--
This Svelte component represents the login/registration page.
It uses Svelte Runes for reactivity and $props() to access form data from server actions.
The `enhance` function from $app/forms is used for progressive enhancement of the form.
It displays a form with email and password inputs, and buttons for Login and Register actions.
Error messages from the server (form?.message) are displayed below the form.
-->
<script lang="ts">
	import UnifiedAuth from '$lib/components/auth/UnifiedAuth.svelte';
	import type { ActionData, PageData } from './$types';

	let { data, form }: { data: PageData; form?: ActionData } = $props();

	const socialProviders = [
		{ name: 'GitHub', url: '/login/github' },
		{ name: 'Google', url: '/login/google' }
	];
</script>

<!-- Login Page -->
<div
	class="min-h-screen bg-base-100 relative"
	data-testid="login-page"
>
	<!-- Subtle background decoration -->
	<div class="absolute inset-0 overflow-hidden pointer-events-none">
		<div class="absolute top-20 left-20 w-2 h-2 bg-primary/20 rounded-full"></div>
		<div class="absolute top-1/3 right-32 w-1.5 h-1.5 bg-secondary/15 rounded-full"></div>
		<div class="absolute bottom-32 left-1/3 w-2.5 h-2.5 bg-accent/10 rounded-full"></div>
	</div>

	<!-- Content -->
	<div class="relative flex items-center justify-center min-h-screen p-4">
		<UnifiedAuth passkeyOptions={data.passkeyOptions} {socialProviders} {form} />
	</div>
</div>
