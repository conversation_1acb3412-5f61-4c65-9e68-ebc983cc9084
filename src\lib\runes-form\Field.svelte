<!--
  @component
  Field
  @description
  This component acts as a bridge between a specific form input and the main `RuneForm` state. It uses Svelte's context API to access form state and a render prop (snippet) to provide field-specific state and handlers to its children.
-->
<script lang="ts">
	import get from 'just-safe-get';
	import set from 'just-safe-set';
	import { getContext, type Snippet } from 'svelte';
	import type { z } from 'zod/v4';
	import { RUNE_FORM_CONTEXT, type RuneFormContext } from './symbols.js';

	type T = $$Generic<z.ZodType>;

	// This is the shape of the object passed to the snippet
	type FieldContext = {
		value: unknown;
		errors: string[] | undefined;
		touched: boolean | undefined;
		handleChange: (e: Event) => void;
		handleBlur: () => void;
	};

	let {
		name,
		children
	}: {
		name: string;
		// The snippet expects a single argument, which is an object matching FieldContext.
		// Svel<PERSON> expects this to be typed as a tuple, hence `[FieldContext]`.
		children: Snippet<[FieldContext]>;
	} = $props();

	// By making `Field.svelte` a generic component (`$$Generic<z.ZodType>`),
	// Svelte can automatically infer the type `T` from the parent `RuneForm`'s context.
	// This replaces the `any` type, providing full type safety for the form object
	// and resolving the "'$$_dleiF5.$$slot_def' is of type 'unknown'" error.
	const form = getContext<RuneFormContext<T>>(RUNE_FORM_CONTEXT);

	// Create the field object that will be passed to the snippet.
	// We use getters to ensure the values are always reactive and up-to-date.
	const field: FieldContext = {
		get value() {
			return get(form.values as any, name);
		},
		get errors() {
			return form.errors[name];
		},
		get touched() {
			return form.touched[name];
		},
		handleChange: (e: Event) => {
			const target = e.target as HTMLInputElement;
			let value: string | boolean | number | FileList | null;

			switch (target.type) {
				case 'checkbox':
					value = target.checked;
					break;
				case 'number':
				case 'range':
					value = target.valueAsNumber;
					break;
				case 'file':
					value = target.files;
					break;
				default:
					value = target.value;
			}
			set(form.values as any, name, value);
		},
		handleBlur: () => {
			form.setTouched(name, true);
		}
	};
</script>

{@render children(field)}
