import { ERROR_MESSAGES } from '$lib/config';
import { json, type RequestEvent } from '@sveltejs/kit';
import { ZodError } from 'zod/v4';

/**
 * Standard API response format for consistent client-side handling
 */
export interface ApiResponse<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	details?: any;
	code?: string;
	timestamp?: string;
}

/**
 * Standard error codes used throughout the application
 */
export const ERROR_CODES = {
	UNAUTHORIZED: 'UNAUTHORIZED',
	FORBIDDEN: 'FORBIDDEN',
	NOT_FOUND: 'NOT_FOUND',
	VALIDATION_ERROR: 'VALIDATION_ERROR',
	RATE_LIMITED: 'RATE_LIMITED',
	INTERNAL_ERROR: 'INTERNAL_ERROR',
	CONFLICT: 'CONFLICT',
	BAD_REQUEST: 'BAD_REQUEST'
} as const;

/**
 * Utility class for creating standardized API responses
 */
export class ApiResponseBuilder {
	/**
	 * Create a successful response
	 */
	static success<T>(data: T, message?: string): ApiResponse<T> {
		return {
			success: true,
			data,
			...(message && { message }),
			timestamp: new Date().toISOString()
		};
	}

	/**
	 * Create an error response
	 */
	static error(
		message: string,
		code: string = ERROR_CODES.INTERNAL_ERROR,
		details?: any,
		statusCode: number = 500
	): { response: ApiResponse; status: number } {
		return {
			response: {
				success: false,
				error: message,
				code,
				details,
				timestamp: new Date().toISOString()
			},
			status: statusCode
		};
	}

	/**
	 * Create an unauthorized response
	 */
	static unauthorized(message: string = 'Unauthorized'): { response: ApiResponse; status: number } {
		return this.error(message, ERROR_CODES.UNAUTHORIZED, undefined, 401);
	}

	/**
	 * Create a forbidden response
	 */
	static forbidden(message: string = 'Forbidden'): { response: ApiResponse; status: number } {
		return this.error(message, ERROR_CODES.FORBIDDEN, undefined, 403);
	}

	/**
	 * Create a not found response
	 */
	static notFound(message: string = 'Resource not found'): { response: ApiResponse; status: number } {
		return this.error(message, ERROR_CODES.NOT_FOUND, undefined, 404);
	}

	/**
	 * Create a validation error response
	 */
	static validationError(
		message: string = ERROR_MESSAGES.GENERIC_ERROR,
		details?: any
	): { response: ApiResponse; status: number } {
		return this.error(message, ERROR_CODES.VALIDATION_ERROR, details, 400);
	}

	/**
	 * Create a rate limited response
	 */
	static rateLimited(
		message: string = 'Too many requests'
	): { response: ApiResponse; status: number } {
		return this.error(message, ERROR_CODES.RATE_LIMITED, undefined, 429);
	}

	/**
	 * Create a conflict response
	 */
	static conflict(message: string = ERROR_MESSAGES.GENERIC_ERROR): { response: ApiResponse; status: number } {
		return this.error(message, ERROR_CODES.CONFLICT, undefined, 409);
	}

	/**
	 * Create a bad request response
	 */
	static badRequest(
		message: string = ERROR_MESSAGES.GENERIC_ERROR,
		details?: any
	): { response: ApiResponse; status: number } {
		return this.error(message, ERROR_CODES.BAD_REQUEST, details, 400);
	}
}

/**
 * Helper function to handle Zod validation errors consistently
 */
export function handleValidationError(error: ZodError): { response: ApiResponse; status: number } {
	return ApiResponseBuilder.validationError(ERROR_MESSAGES.GENERIC_ERROR, error.flatten().fieldErrors);
}

/**
 * Helper function to create JSON responses with consistent format
 */
export function createJsonResponse<T>(data: T, message?: string): Response {
	return json(ApiResponseBuilder.success(data, message));
}

/**
 * Helper function to create error JSON responses
 */
export function createErrorResponse(
	message: string,
	code: string = ERROR_CODES.INTERNAL_ERROR,
	details?: any,
	statusCode: number = 500
): Response {
	const { response, status } = ApiResponseBuilder.error(message, code, details, statusCode);
	return json(response, { status });
}

/**
 * Middleware function to require authentication
 */
export function requireAuth<T extends RequestEvent>(
	handler: (event: T) => Promise<Response>
) {
	return async (event: T): Promise<Response> => {
		if (!event.locals.user) {
			const { response, status } = ApiResponseBuilder.unauthorized();
			return json(response, { status });
		}
		return handler(event);
	};
}

/**
 * Middleware function to handle common API patterns with error handling
 */
export function withErrorHandling<T extends RequestEvent>(
	handler: (event: T) => Promise<Response>
) {
	return async (event: T): Promise<Response> => {
		try {
			return await handler(event);
		} catch (error) {
			console.error('API Error:', error);
			
			// Handle specific error types
			if (error instanceof ZodError) {
				return createErrorResponse(
					'Validation failed',
					ERROR_CODES.VALIDATION_ERROR,
					error.flatten().fieldErrors,
					400
				);
			}

			// Handle generic errors
			const message = error instanceof Error ? error.message : 'An unknown error occurred';
			return createErrorResponse(message);
		}
	};
}

/**
 * Combined middleware for authenticated endpoints with error handling
 */
export function withAuthAndErrorHandling<T extends RequestEvent>(
	handler: (event: T) => Promise<Response>
) {
	return withErrorHandling(requireAuth(handler));
}
