<!--
  Enhanced Image Upload Component
  Supports both file upload and camera capture with image resizing
-->
<script lang="ts">
	import { toast } from '$lib/ui/toast';
	import IconCamera from '~icons/icon-park-outline/camera';
	import IconCheck from '~icons/icon-park-outline/check';
	import IconClose from '~icons/icon-park-outline/close';
	import IconUpload from '~icons/icon-park-outline/upload';

	interface Props {
		value?: string;
		onImageChange: (base64Image: string) => void;
		maxWidth?: number;
		maxHeight?: number;
		quality?: number;
		accept?: string;
		disabled?: boolean;
		title?: string;
		subtitle?: string;
	}

	let {
		value = '',
		onImageChange,
		maxWidth = 1600,
		maxHeight = 1200,
		quality = 0.8,
		accept = 'image/*',
		disabled = false,
		title = 'Add Image',
		subtitle = 'Upload a file or take a photo'
	}: Props = $props();

	let showCameraModal = $state(false);
	let videoElement = $state<HTMLVideoElement>();
	let canvasElement = $state<HTMLCanvasElement>();
	let stream: MediaStream | null = null;
	let isCapturing = $state(false);
	let isProcessing = $state(false);
	let cameraError = $state('');
	let uploadProgress = $state(0);

	async function handleFileUpload(event: Event) {
		const input = event.target as HTMLInputElement;
		if (!input.files || input.files.length === 0) return;

		isProcessing = true;
		try {
			const file = input.files[0];
			await processImage(file);
		} finally {
			isProcessing = false;
			// Reset input
			input.value = '';
		}
	}

	async function processImage(file: File) {
		uploadProgress = 10;
		try {
			uploadProgress = 30;
			const resizedImage = await resizeImage(file, maxWidth, maxHeight, quality);
			uploadProgress = 90;
			onImageChange(resizedImage);
			uploadProgress = 100;
			toast.success('Image processed successfully!');
		} catch (error) {
			console.error('Error processing image:', error);
			toast.error('Failed to process image. Please try again.');
		} finally {
			// Reset progress after a short delay
			setTimeout(() => {
				uploadProgress = 0;
			}, 500);
		}
	}

	async function resizeImage(file: File, maxW: number, maxH: number, qual: number): Promise<string> {
		return new Promise((resolve, reject) => {
			const img = new Image();
			const canvas = document.createElement('canvas');
			const ctx = canvas.getContext('2d');

			if (!ctx) {
				reject(new Error('Could not get canvas context'));
				return;
			}

			img.onload = () => {
				// Calculate new dimensions
				let { width, height } = img;
				
				if (width > height) {
					if (width > maxW) {
						height = (height * maxW) / width;
						width = maxW;
					}
				} else {
					if (height > maxH) {
						width = (width * maxH) / height;
						height = maxH;
					}
				}

				// Set canvas dimensions
				canvas.width = width;
				canvas.height = height;

				// Draw and compress
				ctx.drawImage(img, 0, 0, width, height);
				
				// Convert to base64 with compression
				const base64 = canvas.toDataURL('image/jpeg', qual);
				resolve(base64);
			};

			img.onerror = () => reject(new Error('Failed to load image'));
			img.src = URL.createObjectURL(file);
		});
	}

	async function openCamera() {
		if (disabled || isProcessing) return;

		// Clear any previous errors
		cameraError = '';

		try {
			// Check if mediaDevices is supported
			if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
				cameraError = 'Camera access not supported in this browser';
				toast.error('Camera access is not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.');
				return;
			}

			// Check if we're on HTTPS (required for camera access on most browsers)
			if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
				cameraError = 'Camera access requires HTTPS';
				toast.error('Camera access requires a secure connection (HTTPS). Please access this page over HTTPS.');
				return;
			}

			// Clean up any existing stream first
			if (stream) {
				stream.getTracks().forEach(track => track.stop());
				stream = null;
			}

			stream = await navigator.mediaDevices.getUserMedia({
				video: {
					facingMode: 'environment', // Prefer back camera
					width: { ideal: 1280 },
					height: { ideal: 720 }
				}
			});

			showCameraModal = true;

			// Wait for video element to be available
			await new Promise(resolve => setTimeout(resolve, 150));

			if (videoElement && stream) {
				videoElement.srcObject = stream;
				// Wait for video to be ready
				await new Promise((resolve) => {
					if (videoElement) {
						videoElement.onloadedmetadata = () => resolve(undefined);
					}
				});
			}
		} catch (error) {
			console.error('Error accessing camera:', error);

			// Provide specific error messages based on error type
			if (error instanceof Error) {
				if (error.name === 'NotAllowedError') {
					cameraError = 'Camera access denied. Please allow camera permissions and try again.';
					toast.error('Camera access denied. Please allow camera permissions in your browser and try again.');
				} else if (error.name === 'NotFoundError') {
					cameraError = 'No camera found on this device.';
					toast.error('No camera found. Please ensure your device has a camera and try again.');
				} else if (error.name === 'NotReadableError') {
					cameraError = 'Camera is already in use by another application.';
					toast.error('Camera is already in use. Please close other applications using the camera and try again.');
				} else {
					cameraError = `Camera error: ${error.message}`;
					toast.error(`Camera error: ${error.message}`);
				}
			} else {
				cameraError = 'Could not access camera. Please check permissions.';
				toast.error('Could not access camera. Please check permissions and try again.');
			}

			closeCamera(); // Clean up on error
		}
	}

	async function capturePhoto() {
		if (!videoElement || !canvasElement || isCapturing || !stream) return;

		isCapturing = true;
		cameraError = '';

		try {
			const video = videoElement;
			const canvas = canvasElement;

			// Verify video is ready
			if (video.videoWidth === 0 || video.videoHeight === 0) {
				throw new Error('Video not ready for capture');
			}

			const ctx = canvas.getContext('2d');
			if (!ctx) {
				throw new Error('Could not get canvas context');
			}

			// Set canvas size to video size
			canvas.width = video.videoWidth;
			canvas.height = video.videoHeight;

			// Draw video frame to canvas
			ctx.drawImage(video, 0, 0);

			// Convert to blob with better error handling
			const blob = await new Promise<Blob | null>((resolve) => {
				canvas.toBlob(resolve, 'image/jpeg', 0.9);
			});

			if (!blob) {
				throw new Error('Failed to create image from capture');
			}

			const file = new File([blob], 'camera-capture.jpg', { type: 'image/jpeg' });
			await processImage(file);
			closeCamera();
		} catch (error) {
			console.error('Error capturing photo:', error);
			cameraError = 'Failed to capture photo. Please try again.';
			toast.error(cameraError);
		} finally {
			isCapturing = false;
		}
	}

	function closeCamera() {
		// Clean up video stream
		if (stream) {
			stream.getTracks().forEach(track => track.stop());
			stream = null;
		}

		// Clean up video element
		if (videoElement) {
			videoElement.srcObject = null;
		}

		// Reset states
		showCameraModal = false;
		isCapturing = false;
		cameraError = '';
	}

	// Cleanup on component unmount
	$effect(() => {
		return () => {
			closeCamera();
		};
	});

	function removeImage() {
		onImageChange('');
	}
</script>

{#if value}
	<div class="flex items-center justify-between mb-3">
		<span class="text-sm font-medium text-base-content/90">{title}</span>
		<span class="text-xs text-success/80 flex items-center gap-1">
			<IconCheck class="h-3 w-3" />
			Image added
		</span>
	</div>
{/if}

<div class="w-full">
		{#if value}
			<!-- Image Preview -->
			<div class="bg-base-100/90 border border-base-300/30 rounded-xl shadow-sm overflow-hidden">
				<div class="p-4">
					<div class="relative">
						<img
							src={value}
							alt="Product photo"
							class="rounded-lg max-h-64 w-auto object-contain mx-auto"
						/>
						<!-- Quick remove button -->
						<div class="absolute top-2 right-2 opacity-0 hover:opacity-100 transition-opacity duration-200">
							<button
								type="button"
								class="btn btn-circle btn-xs bg-base-100/80 backdrop-blur-sm border-none shadow-sm hover:bg-error/10 text-error/80"
								onclick={removeImage}
								disabled={disabled || isProcessing}
								title="Remove image"
							>
								<IconClose class="h-3 w-3" />
							</button>
						</div>
					</div>
				</div>
				<!-- Mobile-Optimized Action Buttons -->
				<div class="border-t border-base-300/10 p-4">
					<div class="flex flex-col gap-2 lg:flex-row lg:flex-wrap lg:justify-center">
						<label
							class="btn btn-sm lg:btn-xs bg-base-100 border border-base-300/30 hover:border-primary/30 hover:bg-primary/5 text-base-content/80 transition-all duration-200 w-full lg:w-auto"
							class:opacity-50={disabled || isProcessing}
							for="file-input-replace"
						>
							<IconUpload class="h-3.5 w-3.5" />
							Replace
							<input
								id="file-input-replace"
								type="file"
								{accept}
								class="hidden"
								onchange={handleFileUpload}
								disabled={disabled || isProcessing}
							/>
						</label>
						<button
							type="button"
							class="btn btn-sm lg:btn-xs bg-base-100 border border-base-300/30 hover:border-secondary/30 hover:bg-secondary/5 text-base-content/80 transition-all duration-200 w-full lg:w-auto"
							class:opacity-50={disabled || isProcessing}
							onclick={openCamera}
							disabled={disabled || isProcessing}
						>
							<IconCamera class="h-3.5 w-3.5" />
							Retake Photo
						</button>
						<button
							type="button"
							class="btn btn-sm lg:btn-xs bg-base-100 border border-base-300/30 hover:border-error/30 hover:bg-error/5 text-base-content/80 transition-all duration-200 w-full lg:w-auto"
							class:opacity-50={disabled || isProcessing}
							onclick={removeImage}
							disabled={disabled || isProcessing}
						>
							<IconClose class="h-3.5 w-3.5" />
							Remove
						</button>
					</div>
				</div>
			</div>
		{:else}
			<!-- Upload Area -->
			<div
				id="image-upload-area"
				class="border-2 border-dashed border-base-300/50 rounded-xl p-6 text-center space-y-5 hover:border-primary/50 hover:bg-base-100/50 transition-all duration-200 {isProcessing ? 'border-primary/50 bg-primary/5' : ''}"
			>
				{#if isProcessing}
					<div class="space-y-4 py-4">
						<div class="loading loading-spinner loading-md text-primary/80 mx-auto"></div>
						<div class="space-y-2">
							<p class="text-sm font-medium text-base-content/80">Processing image...</p>
							{#if uploadProgress > 0}
								<div class="w-full max-w-sm mx-auto">
									<progress class="progress progress-primary w-full h-1.5" value={uploadProgress} max="100"></progress>
									<p class="text-xs text-base-content/60 mt-1">{uploadProgress}% complete</p>
								</div>
							{/if}
						</div>
					</div>
				{:else}
					<div class="space-y-5">
						<!-- Icon and Title -->
						<div class="space-y-3">
							<div class="mx-auto w-16 h-16 rounded-full bg-primary/5 border border-primary/10 flex items-center justify-center">
								<IconUpload class="h-8 w-8 text-primary/70" />
							</div>
							<div class="space-y-1">
								<p class="text-base font-semibold text-base-content/90">{title}</p>
								<p class="text-sm text-base-content/60">{subtitle}</p>
							</div>
						</div>

						<!-- Mobile-Optimized Action Buttons -->
						<div class="space-y-4">
							<div class="flex flex-col gap-3 justify-center items-center">
								<label
									class="btn btn-lg lg:btn-sm bg-base-100 border border-primary/20 hover:bg-primary/5 text-primary/80 transition-all duration-200 w-full max-w-sm lg:max-w-xs"
									class:opacity-50={disabled}
									for="file-input-new"
								>
									<IconUpload class="h-5 w-5 lg:h-4 lg:w-4" />
									Choose File
									<input
										id="file-input-new"
										type="file"
										{accept}
										class="hidden"
										onchange={handleFileUpload}
										{disabled}
									/>
								</label>

								<div class="divider text-xs text-base-content/40 my-2">or</div>

								<button
									type="button"
									class="btn btn-lg lg:btn-sm bg-base-100 border border-secondary/20 hover:bg-secondary/5 text-secondary/80 transition-all duration-200 w-full max-w-sm lg:max-w-xs"
									class:opacity-50={disabled}
									onclick={openCamera}
									{disabled}
								>
									<IconCamera class="h-5 w-5 lg:h-4 lg:w-4" />
									Take Photo
								</button>
							</div>
						</div>

						<!-- Help Text -->
						<div class="space-y-1 pt-1 border-t border-base-300/20">
							<p class="text-xs text-base-content/50">
								Supported: JPEG, PNG, WebP • Max: 10MB
							</p>
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>

<!-- Camera Modal -->
{#if showCameraModal}
	<div class="modal modal-open fixed inset-0 z-50">
		<div class="modal-box max-w-3xl bg-base-100/95 backdrop-blur-sm border border-base-300/30">
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center gap-3">
					<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
					<h3 class="font-semibold text-lg text-base-content/90">Take Photo</h3>
				</div>
				<button
					type="button"
					class="btn btn-sm btn-circle bg-base-100/80 border-base-300/30 hover:bg-base-200/50 transition-colors duration-200"
					onclick={closeCamera}
					disabled={isCapturing}
					aria-label="Close camera"
				>
					<IconClose class="h-4 w-4" />
				</button>
			</div>

			<div class="space-y-6">
				{#if cameraError}
					<div class="alert alert-error">
						<IconClose class="h-4 w-4" />
						<span>{cameraError}</span>
						<button class="btn btn-sm" onclick={openCamera}>Try Again</button>
					</div>
				{:else}
					<!-- Instructions -->
					<div class="text-center">
						<p class="text-base text-base-content/80 mb-2">Position your subject in the frame</p>
						<p class="text-sm text-base-content/60">Make sure it's well-lit and clearly visible</p>
					</div>

					<!-- Video Preview -->
					<div class="relative bg-black rounded-xl overflow-hidden shadow-lg" style="aspect-ratio: 4/3;">
						<video
							bind:this={videoElement}
							autoplay
							playsinline
							muted
							class="w-full h-full object-cover"
						></video>

						{#if isCapturing}
							<div class="absolute inset-0 bg-black/60 flex items-center justify-center">
								<div class="text-center text-white space-y-3">
									<span class="loading loading-spinner loading-lg"></span>
									<p class="text-lg font-medium">Capturing photo...</p>
									<p class="text-sm opacity-80">Please hold still</p>
								</div>
							</div>
						{/if}
					</div>
				{/if}

				<!-- Hidden canvas for capture -->
				<canvas bind:this={canvasElement} class="hidden"></canvas>

				<!-- Controls -->
				<div class="flex justify-center gap-3">
					<button
						type="button"
						class="btn bg-base-100 border border-base-300/30 hover:bg-base-200/50 text-base-content/80 transition-all duration-200"
						onclick={closeCamera}
						disabled={isCapturing}
					>
						Cancel
					</button>
					<button
						type="button"
						class="btn bg-primary/10 border border-primary/20 hover:bg-primary/20 text-primary transition-all duration-200"
						onclick={capturePhoto}
						disabled={isCapturing || !!cameraError}
					>
						{#if isCapturing}
							<span class="loading loading-spinner loading-sm"></span>
							Capturing...
						{:else}
							<IconCamera class="h-4 w-4" />
							Capture Photo
						{/if}
					</button>
				</div>

				<!-- Help Text -->
				<div class="text-center space-y-1">
					<p class="text-sm text-base-content/60">
						Hold your device steady for the best results
					</p>
					<p class="text-xs text-base-content/40">
						The photo will be automatically resized and optimized
					</p>
				</div>
			</div>
		</div>
		<button
			type="button"
			class="modal-backdrop"
			onclick={closeCamera}
			aria-label="Close camera modal"
		></button>
	</div>
{/if}
