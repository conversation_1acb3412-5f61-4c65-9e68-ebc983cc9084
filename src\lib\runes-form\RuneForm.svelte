<!--
  @component
  RuneForm
  @description
  This component is the cornerstone of the runes-form library. It orchestrates the entire form management process by leveraging Svelte 5 Runes for reactive state management and Zod for robust, type-safe schema validation.

  **Purpose and Reasoning:**

  1.  **State from Prop:** It accepts a form instance created by `createRuneForm`. This allows for "lifting state up" so the parent component owns and controls the form state.

  2.  **Context Provision:** The component uses Svelte's `setContext` to provide the entire form instance to all descendant components. This avoids prop-drilling and allows components like `Field`, `Errors`, etc., to easily access and interact with the form's state and methods. The context is uniquely identified by `RUNE_FORM_CONTEXT` to prevent collisions.

  3.  **Encapsulation of Logic:** By wrapping child components, it provides a natural and semantic structure for forms. Child components can then handle submission logic, like calling `form.validate()` before proceeding.
-->
<script lang="ts">
	import { setContext, type Snippet } from 'svelte';
	import type { ZodType } from 'zod/v4';
	import type { RunesForm } from './index.svelte.ts';
	import { RUNE_FORM_CONTEXT, type RuneFormContext } from './symbols.js';

	type T = $$Generic<ZodType>;

	let {
		form,
		children
	}: {
		form: RunesForm<T>;
		children: Snippet<[RuneFormContext<T>]>;
	} = $props();

	setContext<RuneFormContext<T>>(RUNE_FORM_CONTEXT, form);
</script>

{@render children(form)}
