# Hairloom Design System

## Design Philosophy

The Hairloom design system follows a **refined, coy, and playful** aesthetic that balances sophistication with approachability.

### Core Principles

- **Coy**: Subtle interactions and gentle color usage that doesn't demand attention
- **Playful**: Smooth animations, hover effects, and small delightful details
- **Refined**: Clean typography, proper spacing, sophisticated color palette, and polished interactions

## Visual Language

### Colors

- **Primary**: Used sparingly for key actions and accents
- **Secondary/Accent**: Supporting colors for variety and hierarchy
- **Base Content**: Text uses opacity variations (90%, 70%, 60%, 50%, 40%) for hierarchy
- **Backgrounds**: Subtle transparency and layering (`base-100/80`, `base-300/30`)

### Typography

- **Headers**: `font-semibold` or `font-medium` instead of `font-bold`
- **Body**: `text-base-content/90` for primary text, `/70` for secondary, `/60` for tertiary
- **Sizes**: More restrained sizing (text-2xl instead of text-3xl for headers)

### Spacing & Layout

- **Cards**: `rounded-xl` with subtle borders and shadows
- **Padding**: Generous but not excessive (`p-6` standard, `p-8` for important areas)
- **Gaps**: Consistent spacing using `gap-3`, `gap-4`, `gap-6`

## Component Patterns

### Buttons

```svelte
<!-- Primary Action -->
<button class="btn btn-primary transition-all duration-200 hover:shadow-md">
  Action
</button>

<!-- Secondary Action -->
<button class="btn btn-ghost transition-colors duration-200 hover:bg-base-200/50">
  Secondary
</button>
```

### Cards

```svelte
<div class="bg-base-100/80 border border-base-300/30 shadow-sm rounded-xl p-6">
  <!-- Content -->
</div>
```

### Form Inputs

```svelte
<input class="input input-bordered transition-all duration-200 focus:border-primary/50 focus:ring-2 focus:ring-primary/10" />
```

### Navigation Items

```svelte
<a class="transition-colors duration-200 hover:bg-primary/5 {active ? 'bg-primary/10 text-primary border-l-2 border-primary/50' : ''}">
  <!-- Content -->
</a>
```

## Decorative Elements

### Subtle Accents

- Small colored dots: `w-2 h-2 bg-primary/40 rounded-full`
- Status indicators: `w-1.5 h-1.5 bg-success rounded-full`
- Background decoration: Minimal, positioned absolutely

### Hover Effects

- **Buttons**: `hover:shadow-md`
- **Cards**: `hover:border-base-300/50`
- **Navigation**: `hover:bg-primary/5`

## Animation Guidelines

### Transitions

- **Duration**: `duration-200` for most interactions
- **Easing**: Default CSS easing (no custom curves)
- **Properties**: `transition-colors`, `transition-all`

### Avoid

- Scale transforms (`hover:scale-105`)
- Rotation effects
- Bounce animations
- Pulse effects (except for loading states)

## Layout Patterns

### Page Headers

```svelte
<div class="flex items-center gap-3 mb-2">
  <div class="w-2 h-2 bg-primary/40 rounded-full"></div>
  <h1 class="text-2xl font-semibold text-base-content/90">Page Title</h1>
</div>
<p class="text-base-content/60 ml-5">Subtitle or description</p>
```

### Content Containers

```svelte
<div class="bg-base-100/80 backdrop-blur-sm border border-base-300/30 rounded-xl shadow-sm">
  <div class="p-8">
    <!-- Content -->
  </div>
</div>
```

## Component Library

### Available Components

- `FormInput.svelte` - Standardized form inputs
- `Button.svelte` - Consistent button styling
- `Card.svelte` - Content containers
- `SettingsModule.svelte` - Settings section wrapper

### Usage

Import and use these components instead of writing custom markup to ensure consistency across the application.

## Maintenance

When adding new components or updating existing ones:

1. Follow the established color and spacing patterns
2. Use the standard transition durations and properties
3. Maintain the refined aesthetic - avoid overly bright or attention-grabbing elements
4. Test hover states and interactions
5. Ensure accessibility with proper contrast and focus states
