import { browser } from '$app/environment';
import { writable } from 'svelte/store';

export const themes = [
	'hairloomlight',
	'hairloomdark',
	'light',
	'dark',
	'cupcake',
	'bumblebee',
	'emerald',
	'corporate',
	'synthwave',
	'retro',
	'cyberpunk',
	'valentine',
	'halloween',
	'garden',
	'forest',
	'aqua',
	'lofi',
	'pastel',
	'fantasy',
	'wireframe',
	'black',
	'luxury',
	'dracula',
	'cmyk',
	'autumn',
	'business',
	'acid',
	'lemonade',
	'night',
	'coffee',
	'winter'
] as const;

export type Theme = (typeof themes)[number];

const defaultTheme: Theme = 'autumn';
const themeCookieName = 'theme_preference';

// Helper functions for cookies
function setCookie(name: string, value: string, days: number = 365) {
	if (!browser) return;
	let expires = '';
	if (days) {
		const date = new Date();
		date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
		expires = '; expires=' + date.toUTCString();
	}
	document.cookie = name + '=' + (value || '') + expires + '; path=/; SameSite=Lax';
}

function getCookie(name: string): string | null {
	if (!browser) return null; // Should be handled by server hook if !browser
	const nameEQ = name + '=';
	const ca = document.cookie.split(';');
	for (let i = 0; i < ca.length; i++) {
		let c = ca[i];
		while (c.charAt(0) == ' ') c = c.substring(1, c.length);
		if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
	}
	return null;
}

let clientInitialTheme: Theme;
if (browser) {
	const storedTheme = localStorage.getItem('theme') as Theme;
	const cookieTheme = getCookie(themeCookieName) as Theme;
	clientInitialTheme = storedTheme || cookieTheme || defaultTheme;
} else {
	// On the server, initialTheme is not directly used by this store for applying the theme.
	// The `handle` hook in `hooks.server.ts` reads the cookie and sets the theme attribute.
	// We set a default here, but it's less critical server-side for this store.
	clientInitialTheme = defaultTheme;
}
export const initialTheme = clientInitialTheme;

const { subscribe, set, update } = writable<Theme>(initialTheme);

export const themeStore = {
	subscribe,
	set: (theme: Theme) => {
		if (browser) {
			localStorage.setItem('theme', theme);
			setCookie(themeCookieName, theme);
			document.documentElement.setAttribute('data-theme', theme);
		}
		set(theme);
	},
	update
};

// Apply initial theme on load for client-side consistency,
// though SSR should have already set it.
if (browser) {
	document.documentElement.setAttribute('data-theme', initialTheme);
}
