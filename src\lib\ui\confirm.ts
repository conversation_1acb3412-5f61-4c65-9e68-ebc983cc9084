/**
 * Abstraction for the confirmation modal system.
 * This provides a simple async function to prompt the user for confirmation,
 * hiding the underlying store implementation from the components.
 */
import { confirmationStore, type ConfirmationConfig } from '$lib/stores/confirmationStore.svelte';

/**
 * A utility function to open a confirmation modal.
 * @param config - The configuration for the confirmation modal.
 * @returns A promise that resolves to `true` if the user confirms, and `false` otherwise.
 */
export function confirm(config: ConfirmationConfig): Promise<boolean> {
	return confirmationStore.confirm(config);
}
