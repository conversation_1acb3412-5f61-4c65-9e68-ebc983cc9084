import { env } from '$env/dynamic/private';
import { github } from '$lib/server/auth/github';
import { error, redirect } from '@sveltejs/kit';
import { generateState } from 'arctic';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ cookies, url: requestUrl }) => {
	if (!github) {
		throw error(500, 'GitHub login is not configured.');
	}
	const loginHint = requestUrl.searchParams.get('login_hint');
	const state = generateState();
	const url = await github.createAuthorizationURL(state, ['user:email']);
	url.searchParams.set('prompt', 'select_account');
	if (loginHint) {
		url.searchParams.set('login', loginHint);
	}

	cookies.set('github_oauth_state', state, {
		path: '/',
		secure: env.VERCEL_ENV === 'production',
		httpOnly: true,
		maxAge: 60 * 10, // 10 minutes
		sameSite: 'lax'
	});

	return redirect(302, url.toString());
};
