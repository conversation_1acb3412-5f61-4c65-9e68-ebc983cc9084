import { copyTemplateSchema } from '$lib/schemas/template';
import { db } from '$lib/server/db';
import { templates } from '$lib/server/db/schema';
import { fail, redirect } from '@sveltejs/kit';
import { and, eq, isNull, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	const user = locals.user;
	if (!user) {
		redirect(302, '/login');
	}

	const userTemplates = await db.query.templates.findMany({
		where: eq(templates.user_id, user.id),
		orderBy: (templates, { desc }) => [desc(templates.updated_at)]
	});

	const systemTemplates = await db.query.templates.findMany({
		where: isNull(templates.user_id),
		orderBy: (templates, { desc }) => [desc(templates.updated_at)]
	});

	return {
		userTemplates,
		systemTemplates
	};
};

export const actions: Actions = {
	createTemplate: async ({ locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { createTemplate: { errors: { root: ['Unauthorized'] } } });
		}

		const newTemplate = {
			user_id: user.id,
			name: 'Untitled Template',
			description: 'A new blank template.',
			template_definition: { fields: [] },
			status: 'draft' as const
		};

		const [result] = await db.insert(templates).values(newTemplate).returning({ id: templates.id });

		if (!result) {
			return fail(500, { createTemplate: { errors: { root: ['Failed to create template'] } } });
		}

		redirect(302, `/app/templates/${result.id}`);
	},

	copyTemplate: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { copyTemplate: { errors: { root: ['Unauthorized'] } } });
		}

		const formData = await request.formData();
		const validation = copyTemplateSchema.safeParse(Object.fromEntries(formData));

		if (!validation.success) {
			return fail(400, {
				copyTemplate: {
					errors: { root: ['Invalid template ID provided.'] }
				}
			});
		}
		const { templateId } = validation.data;

		const templateToCopy = await db.query.templates.findFirst({
			where: and(
				eq(templates.id, templateId),
				or(isNull(templates.user_id), eq(templates.user_id, user.id)) // Can copy system or own templates
			)
		});

		if (!templateToCopy) {
			return fail(404, { copyTemplate: { errors: { root: ['Template not found'] } } });
		}

		const newTemplate = {
			user_id: user.id,
			name: `${templateToCopy.name} (Copy)`,
			description: templateToCopy.description,
			template_definition: templateToCopy.template_definition,
			status: 'draft' as const
		};

		const [result] = await db.insert(templates).values(newTemplate).returning({ id: templates.id });

		if (!result) {
			return fail(500, { copyTemplate: { errors: { root: ['Failed to copy template'] } } });
		}

		redirect(302, `/app/templates/${result.id}`);
	}
};
