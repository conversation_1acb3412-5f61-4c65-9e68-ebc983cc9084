<!--
  - src/lib/components/templates/FieldEditorModal.svelte
  -
  - Purpose:
  - Provides a modal dialog for editing the properties of a single form field within a template.
  - This includes settings like label, name, field type, placeholder, and options for select/radio fields.
  -
  - Key Features:
  - - Can be initialized with an existing field to edit, or empty to create a new one.
  - - Uses local state to manage edits without affecting the parent component until saved.
  - - Dynamically shows/hides fields relevant to the selected field type (e.g., 'Options' for 'select').
  - - Emits 'save' and 'close' events to communicate with the parent `TemplateEditor`.
  - - Basic validation to ensure required properties are filled.
-->
<script lang="ts">
	import type { FieldType, FormField } from '$lib/schemas/template';
	import { slide } from 'svelte/transition';

	let {
		field,
		onSave,
		onClose
	}: {
		field: FormField | null;
		onSave: (field: FormField) => void;
		onClose: () => void;
	} = $props();

	let localField = $state<Partial<FormField>>(
		field
			? { ...field }
			: {
					id: crypto.randomUUID(),
					name: '',
					label: '',
					type: 'text',
					required: false,
					options: []
				}
	);

	let optionRefs = $state<HTMLInputElement[]>([]);

	const fieldTypes: FieldType[] = [
		'text',
		'textarea',
		'number',
		'date',
		'datetime-local',
		'time',
		'checkbox',
		'select',
		'radio',
		'email',
		'password',
		'url'
	];

	function addOption() {
		if (!localField.options) {
			localField.options = [];
		}
		localField.options.push({ label: '', value: '' });
		localField.options = [...localField.options]; // Trigger reactivity
	}

	function removeOption(index: number) {
		if (localField.options) {
			localField.options.splice(index, 1);
			localField.options = [...localField.options];
		}
	}

	function handleSave() {
		// Basic validation
		if (!localField.label?.trim() || !localField.name?.trim()) {
			alert('Label and Name are required.');
			return;
		}
		onSave(localField as FormField);
	}

	function close() {
		onClose();
	}

	function toCamelCase(str: string): string {
		return str
			.replace(/[^a-zA-Z0-9\s]/g, '')
			.split(' ')
			.map((word, index) => {
				if (index === 0) {
					return word.toLowerCase();
				}
				return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
			})
			.join('');
	}

	// When type changes, clear options if not applicable
	$effect(() => {
		if (localField.type !== 'select' && localField.type !== 'radio') {
			localField.options = [];
		}
	});

	// Auto-generate name from label for new fields
	$effect(() => {
		if (!field && localField.label) {
			localField.name = toCamelCase(localField.label);
		}
	});

	// When an option is added, focus the new input
	$effect(() => {
		if (optionRefs.length > 0) {
			const lastInput = optionRefs[optionRefs.length - 1];
			if (lastInput) {
				lastInput.focus();
			}
		}
	});
</script>

<div
	class="modal modal-open"
	role="dialog"
	tabindex="-1"
	onkeydown={(e: KeyboardEvent) => e.key === 'Escape' && close()}
>
	<div class="modal-box w-11/12 max-w-2xl" transition:slide>
		<h3 class="text-2xl font-bold">{field ? 'Edit Field' : 'Add New Field'}</h3>

		<div class="space-y-4 py-4">
			<div>
				<label class="floating-label">
					<input
						id="field-label"
						type="text"
						class="input input-bordered w-full"
						placeholder="Field Label"
						bind:value={localField.label}
					/>
					<span>Label</span>
				</label>
			</div>
			<div>
				<label class="floating-label">
					<input
						id="field-name"
						type="text"
						class="input input-bordered w-full"
						placeholder="field_name"
						bind:value={localField.name}
						disabled={!!field}
					/>
					<span>Name / ID (unique, no spaces)</span>
				</label>
			</div>
			<div>
				<label class="select select-bordered">
					<span class="label">Field Type</span>
					<select id="field-type" bind:value={localField.type}>
						{#each fieldTypes as type}
							<option value={type}>{type}</option>
						{/each}
					</select>
				</label>
			</div>
			<div>
				<label class="floating-label">
					<input
						id="field-placeholder"
						type="text"
						class="input input-bordered w-full"
						placeholder="Enter placeholder text"
						bind:value={localField.placeholder}
					/>
					<span>Placeholder</span>
				</label>
			</div>
			<div class="form-control">
				<label class="label cursor-pointer">
					<span class="label-text">Required Field</span>
					<input type="checkbox" class="toggle toggle-primary" bind:checked={localField.required} />
				</label>
			</div>

			{#if localField.type === 'select' || localField.type === 'radio'}
				<div class="divider">Options</div>
				<div class="space-y-2">
					{#each localField.options || [] as option, i (i)}
						<div class="flex items-center gap-2">
							<input
								bind:this={optionRefs[i * 2]}
								type="text"
								placeholder="Label"
								class="input input-sm input-bordered flex-1"
								bind:value={option.label}
							/>
							<input
								bind:this={optionRefs[i * 2 + 1]}
								type="text"
								placeholder="Value"
								class="input input-sm input-bordered flex-1"
								bind:value={option.value}
							/>
							<button class="btn btn-sm btn-ghost text-error" onclick={() => removeOption(i)}
								>Remove</button
							>
						</div>
					{/each}
				</div>
				<button class="btn btn-sm btn-outline mt-2" onclick={addOption}>Add Option</button>
			{/if}
		</div>

		<div class="modal-action">
			<button class="btn" onclick={close}>Cancel</button>
			<button class="btn btn-primary" onclick={handleSave}>Save Field</button>
		</div>
	</div>
	<div
		class="modal-backdrop"
		role="button"
		tabindex="-1"
		aria-label="Close modal"
		onclick={close}
		onkeydown={(e) => {
			if (e.key === 'Enter' || e.key === ' ') {
				close();
			}
		}}
	></div>
</div>
