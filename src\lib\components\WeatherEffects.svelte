<!--
This component renders various weather effects using svelte-confetti.
The effects are designed to be visually distinct and can be cycled through via a button.
It is positioned to render behind other page content.
When the user has indicated a preference for reduced motion, this component will not render any effects or the control button.
-->
<script lang="ts">
	import { onMount } from 'svelte';
	import { Confetti } from 'svelte-confetti';
	import IconCloudy from '~icons/icon-park-outline/cloudy';
	import IconHeavyRain from '~icons/icon-park-outline/heavy-rain';
	import IconLeaf from '~icons/icon-park-outline/leaf';
	import IconMoon from '~icons/icon-park-outline/moon';
	import IconSun from '~icons/icon-park-outline/sun-one';

	type WeatherEffect = 'none' | 'sunny' | 'autumn' | 'rain' | 'snow';
	const effects: WeatherEffect[] = ['none', 'sunny', 'autumn', 'rain', 'snow'];
	let effectIndex = $state(0);
	let currentEffect = $derived(effects[effectIndex]);

	let prefersReducedMotion = $state(false);
	onMount(() => {
		const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
		prefersReducedMotion = mediaQuery.matches;
		const listener = (e: MediaQueryListEvent) => (prefersReducedMotion = e.matches);
		mediaQuery.addEventListener('change', listener);
		return () => mediaQuery.removeEventListener('change', listener);
	});

	function cycleEffect() {
		effectIndex = (effectIndex + 1) % effects.length;
	}

	const icons = {
		none: IconMoon,
		sunny: IconSun,
		autumn: IconLeaf,
		rain: IconHeavyRain,
		snow: IconCloudy
	};

	let CurrentIcon = $derived(icons[currentEffect]);

	// --- SVG Definitions for Confetti Particles ---
	const svgToUrl = (svg: string) => `url("data:image/svg+xml;utf8,${encodeURIComponent(svg)}")`;

	// Autumn Leaf
	const leafSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24px" height="24px"><path d="M13.6,2.6C12.9,2.8,12,3.3,11.2,4.3C8.4,7.8,9,12.2,9,12.2c-0.6,0.1-1.2-0.2-1.8-0.5c-2.3-1.1-5.1,0.6-5.1,0.6 c-0.8,0.5-0.9,1.6-0.3,2.2c1.7,1.9,4.2,1.3,4.2,1.3l-0.5,0.7c0,0-2.4,1-2.9,2.9c-0.5,1.9,0.5,3.7,0.5,3.7c0.6,0.6,1.6,0.7,2.2-0.1 c0,0,1.5-1.1,0.5-2.9c-0.6-1.1,0.5-2.7,0.5-2.7l0.8-0.4c0,0,4.6-0.5,8.2-4.1c3.1-3.1,3.4-7,3.4-7S14.6,2.3,13.6,2.6z"/></svg>`;
	const autumnColors = ['#D95C2B', '#A94E27', '#E8A03E', '#C76B29'];
	const leafUrls = autumnColors.map((c) => svgToUrl(leafSvg.replace('currentColor', c)));



	// Snowflake
	const snowflakeSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24px" height="24px"><path d="M21.6 13.3h-3.6l-2.4 4.1 2.4 4.2h-4.1l-2.2-2.3-2.2 2.3H5.4l2.4-4.2-2.4-4.1h4.1l2.2 2.3 2.2-2.3h6.3zM12 11.3L9.8 7.2H5.4l2.4 4.1H2.4v-1h5.4L5.4 6.2H2.4V5.1h5.4L5.5 1l4.3 4.1h4.5l2.4-4.1-2.4 4.1h5.4v1h-5.4l2.4 4.1-2.4 4.1v1h-5.4L10.8 7.2 12 11.3z"/></svg>`;
	const snowflakeUrls = ['#FFFFFF', '#F0F8FF', '#E6E6FA'].map((c) =>
		svgToUrl(snowflakeSvg.replace('currentColor', c))
	);
</script>

{#if !prefersReducedMotion}
	{#if currentEffect === 'sunny'}
		<div
			class="pointer-events-none fixed top-[-50px] left-0 flex h-screen w-screen justify-center overflow-hidden"
		>
			<Confetti
				x={[-5, 5]}
				y={[0, 0.1]}
				delay={[0, 4000]}
				colorArray={['#FFD700', '#FFA500', '#FFF700']}
				infinite
				duration={8000}
				amount={150}
				fallDistance="110vh"
				size={4}
				rounded
			/>
		</div>
	{:else if currentEffect === 'autumn'}
		<div
			class="pointer-events-none fixed top-[-50px] left-0 flex h-screen w-screen justify-center overflow-hidden"
		>
			<!-- Main fall -->
			<Confetti
				x={[-5, 5]}
				y={[0, 0.1]}
				delay={[0, 10000]}
				colorArray={leafUrls}
				infinite
				duration={15000}
				amount={50}
				fallDistance="110vh"
				size={22}
				xSpread={0.5}
			/>
			<!-- Slower, smaller leaves for depth -->
			<Confetti
				x={[-4, 4]}
				y={[0, 0.1]}
				delay={[2000, 12000]}
				colorArray={leafUrls}
				infinite
				duration={20000}
				amount={25}
				fallDistance="110vh"
				size={15}
				xSpread={0.4}
			/>
		</div>
	{:else if currentEffect === 'rain'}
		<div
			class="pointer-events-none fixed top-[-50px] left-0 flex h-screen w-screen justify-center overflow-hidden"
		>
			<!-- Fast, light rain layer -->
			<Confetti
				x={[-8, 8]}
				y={[0, 0.1]}
				delay={[0, 2000]}
				colorArray={['#A2C2FF', '#B5D8FF']}
				infinite
				duration={2500}
				amount={80}
				fallDistance="110vh"
				size={6}
				xSpread={0.03}
				rounded
			/>
			<!-- Medium rain layer for depth -->
			<Confetti
				x={[-6, 6]}
				y={[0, 0.15]}
				delay={[500, 3000]}
				colorArray={['#C8E4FF', '#A2C2FF']}
				infinite
				duration={3000}
				amount={60}
				fallDistance="110vh"
				size={4}
				xSpread={0.02}
				rounded
			/>
		</div>
	{:else if currentEffect === 'snow'}
		<div
			class="pointer-events-none fixed top-[-50px] left-0 flex h-screen w-screen justify-center overflow-hidden"
		>
			<!-- Large, slow flakes -->
			<Confetti
				x={[-5, 5]}
				y={[0, 0.1]}
				delay={[0, 15000]}
				colorArray={snowflakeUrls}
				infinite
				duration={25000}
				amount={75}
				fallDistance="110vh"
				size={18}
				xSpread={0.4}
			/>
			<!-- Small, slightly faster flakes for depth -->
			<Confetti
				x={[-6, 6]}
				y={[0, 0.1]}
				delay={[0, 15000]}
				colorArray={snowflakeUrls}
				infinite
				duration={20000}
				amount={75}
				fallDistance="110vh"
				size={10}
				xSpread={0.3}
			/>
		</div>
	{/if}

	<button
		onclick={cycleEffect}
		class="btn btn-primary fixed right-4 bottom-4 z-50 capitalize"
		title="Change Weather Effect"
	>
		<CurrentIcon class="mr-2 h-5 w-5" />
		{currentEffect}
	</button>
{/if}
