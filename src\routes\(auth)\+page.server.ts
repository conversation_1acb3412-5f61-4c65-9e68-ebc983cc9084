import { redirect } from '@sveltejs/kit';

import type { PageServerLoadEvent } from './$types';

export function load(event: PageServerLoadEvent) {
	const { user, session } = event.locals;

	// If not logged in, send to login page.
	if (user === null || session === null) {
		return redirect(302, '/login');
	}

	// If logged in and email verified, redirect to the main application.
	// MFA (WebAuthn) status is handled during the login flow itself or by specific MFA-requiring endpoints.
	// A valid session for an email-verified user is considered app-ready here.
	const isAppReady = user.email_verified;

	if (isAppReady) {
		return redirect(302, '/app');
	}

	// --- Intermediate states for logged-in users not yet fully authenticated for /app ---
	// If they land on this specific `/(auth)/` page, guide them.

	// If email is not verified, redirect to verification page.
	// This is the only remaining check after isAppReady, as 2FA/MFA specific redirects are removed from this page.
	if (!user.email_verified) {
		return redirect(302, '/verify-email');
	}

	// Fallback: This point implies user is logged in but not email_verified, which should have been caught above.
	// Or, user is email_verified, which should have been caught by `isAppReady`.
	// Redirecting to /app as a safety measure if this state is somehow reached and they are indeed email_verified.
	// If not email_verified, they will be redirected by the check above.
	// If the logic is sound, this redirect might be unreachable but serves as a safe default.
	return redirect(302, '/app');
}
