import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async () => {
	// ALPHA FEATURE: Shopping List system
	// This feature has been moved to alpha status and is not available in the beta release.
	// The complete shopping list system includes:
	// - Adding items from inventory to shopping list
	// - Quantity management and cost estimation
	// - Purchase tracking and completion workflow
	// - Bulk operations for completing shopping and updating inventory
	// - Integration with inventory for automatic restocking
	// - Cost tracking using purchase prices vs selling prices
	// - Reorder suggestions based on inventory thresholds
	// 
	// Database schema and validation schemas are preserved for future re-implementation.
	// All route files and components remain in the codebase but are inaccessible via navigation.
	throw redirect(302, '/app');
};
