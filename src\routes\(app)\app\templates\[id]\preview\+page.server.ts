import { db } from '$lib/server/db';
import { templates } from '$lib/server/db/schema';
import { error, redirect } from '@sveltejs/kit';
import { and, eq, isNull, or } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
	const user = locals.user;
	if (!user) {
		redirect(302, '/login');
	}

	const templateId = params.id;

	// A user can preview their own templates or system templates
	const template = await db.query.templates.findFirst({
		where: and(
			eq(templates.id, templateId),
			or(eq(templates.user_id, user.id), isNull(templates.user_id))
		)
	});

	if (!template) {
		error(404, 'Template not found');
	}

	return {
		template
	};
};
