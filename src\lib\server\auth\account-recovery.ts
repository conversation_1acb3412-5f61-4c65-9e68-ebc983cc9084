import { db } from '$lib/server/db';
import { user_account_recovery_codes } from '$lib/server/db/schema';
import * as bip39 from 'bip39';
import { and, eq, isNull } from 'drizzle-orm';
import { hashRecoveryCode, verifyRecoveryCodeHash } from './utils';

const NUMBER_OF_RECOVERY_CODES = 5; // Standard practice: offer multiple codes

function generateRandomHexCode(): string {
	// 16 bytes = 32 hex chars
	return Array.from(crypto.getRandomValues(new Uint8Array(16)))
		.map((b) => b.toString(16).padStart(2, '0'))
		.join('');
}

/**
 * Generates a new set of recovery codes for the user, invalidates old ones,
 * and stores the new hashed codes in the database.
 * @param userId The ID of the user.
 * @returns An object containing two arrays: hexCodes and mnemonics.
 */
export async function generateAndStoreRecoveryCodes(
	userId: string
): Promise<{ hexCodes: string[]; mnemonics: string[] }> {
	// Invalidate all existing recovery codes for this user
	await invalidateAllUserRecoveryCodes(userId);

	const hexCodes: string[] = [];
	const mnemonics: string[] = [];
	const codesToInsert: Array<typeof user_account_recovery_codes.$inferInsert> = [];

	for (let i = 0; i < NUMBER_OF_RECOVERY_CODES; i++) {
		const hex = generateRandomHexCode();
		hexCodes.push(hex);
		const mnemonic = bip39.entropyToMnemonic(hex);
		mnemonics.push(mnemonic);
		const hashedCode = await hashRecoveryCode(hex);
		codesToInsert.push({
			user_id: userId,
			hashed_code: hashedCode
			// created_at is default, used_at is null by default
		});
	}

	if (codesToInsert.length > 0) {
		await db.insert(user_account_recovery_codes).values(codesToInsert);
	}

	return { hexCodes, mnemonics };
}

export function mnemonicToHex(mnemonic: string): string {
	// Returns hex string (lowercase, no 0x prefix)
	return bip39.mnemonicToEntropy(mnemonic);
}

/**
 * Retrieves all active (unused) hashed recovery codes for a user.
 * @param userId The ID of the user.
 * @returns An array of hashed recovery codes.
 */
export async function getActiveHashedRecoveryCodes(userId: string): Promise<string[]> {
	const results = await db
		.select({ hashed_code: user_account_recovery_codes.hashed_code })
		.from(user_account_recovery_codes)
		.where(
			and(
				eq(user_account_recovery_codes.user_id, userId),
				isNull(user_account_recovery_codes.used_at)
			)
		);
	return results.map((r) => r.hashed_code);
}

/**
 * Verifies a plaintext recovery code (hex or mnemonic) against the user's stored hashed codes.
 * If valid, marks the code as used.
 * @param userId The ID of the user.
 * @param plaintextCode The plaintext recovery code to verify (hex or mnemonic).
 * @returns True if the code is valid and was successfully marked as used, false otherwise.
 */
export async function verifyAndUseRecoveryCode(
	userId: string,
	plaintextCode: string
): Promise<boolean> {
	// Accept either hex or mnemonic
	let codeToCheck = plaintextCode.trim();
	if (bip39.validateMnemonic(codeToCheck)) {
		try {
			codeToCheck = mnemonicToHex(codeToCheck);
		} catch (_e) {
			return false;
		}
	}

	const activeHashedCodes = await db
		.select()
		.from(user_account_recovery_codes)
		.where(
			and(
				eq(user_account_recovery_codes.user_id, userId),
				isNull(user_account_recovery_codes.used_at)
			)
		);

	if (activeHashedCodes.length === 0) {
		return false; // No active codes for this user
	}

	for (const storedCode of activeHashedCodes) {
		const isValid = await verifyRecoveryCodeHash(storedCode.hashed_code, codeToCheck);
		if (isValid) {
			// Found a match, mark it as used
			await db
				.update(user_account_recovery_codes)
				.set({ used_at: new Date() })
				.where(eq(user_account_recovery_codes.id, storedCode.id));
			return true;
		}
	}
	return false; // No match found
}

/**
 * Invalidates all recovery codes for a given user.
 * Typically used when new codes are generated or if the feature is disabled.
 * @param userId The ID of the user.
 */
export async function invalidateAllUserRecoveryCodes(userId: string): Promise<void> {
	await db
		.delete(user_account_recovery_codes)
		.where(eq(user_account_recovery_codes.user_id, userId));
}

/**
 * Checks if a user has any active recovery codes.
 * @param userId The ID of the user.
 * @returns True if the user has active recovery codes, false otherwise.
 */
export async function userHasActiveRecoveryCodes(userId: string): Promise<boolean> {
	const result = await db
		.select({ id: user_account_recovery_codes.id })
		.from(user_account_recovery_codes)
		.where(
			and(
				eq(user_account_recovery_codes.user_id, userId),
				isNull(user_account_recovery_codes.used_at)
			)
		)
		.limit(1);
	return result.length > 0;
}
