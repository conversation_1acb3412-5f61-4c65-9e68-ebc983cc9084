import { z } from 'zod/v4';

export const fieldTypeSchema = z.enum([
	'text',
	'textarea',
	'number',
	'date',
	'datetime-local',
	'time',
	'checkbox',
	'select',
	'radio',
	'email',
	'password',
	'url'
]);

export const formFieldOptionSchema = z.object({
	value: z.string().min(1, 'Option value cannot be empty.'),
	label: z.string().min(1, 'Option label cannot be empty.')
});

export const formFieldSchema = z
	.object({
		id: z.string().uuid('Field ID must be a valid UUID.'),
		name: z
			.string()
			.min(1, 'Field name cannot be empty.')
			.regex(
				/^[a-z][a-zA-Z0-9]*$/,
				'Field name must be a valid camelCase identifier (e.g., myFieldName).'
			),
		label: z.string().min(1, 'Field label cannot be empty.'),
		type: fieldTypeSchema,
		placeholder: z.string().optional(),
		required: z.boolean().optional(),
		options: z.array(formFieldOptionSchema).optional(),
		defaultValue: z.union([z.string(), z.number(), z.boolean(), z.array(z.string())]).optional()
	})
	.refine(
		(data) => {
			if (['select', 'radio'].includes(data.type)) {
				return Array.isArray(data.options) && data.options.length > 0;
			}
			return true;
		},
		{
			message: 'Select and Radio field types must have at least one option.',
			path: ['options']
		}
	)
	.refine(
		(data) => {
			if (Array.isArray(data.options)) {
				const values = data.options.map((o) => o.value);
				return new Set(values).size === values.length;
			}
			return true;
		},
		{
			message: 'Option values must be unique within a field.',
			path: ['options']
		}
	);

export const templateDefinitionSchema = z.object({
	fields: z.array(formFieldSchema).max(100, 'Template cannot have more than 100 fields.')
});

// ---------------------------------------------------------------------------
// Inferred Types from Schemas
// ---------------------------------------------------------------------------
export type FieldType = z.infer<typeof fieldTypeSchema>;
export type FormFieldOption = z.infer<typeof formFieldOptionSchema>;
export type FormField = z.infer<typeof formFieldSchema>;
export type TemplateDefinition = z.infer<typeof templateDefinitionSchema>;

// ---------------------------------------------------------------------------
// Template core (name / description / definition) validation
// ---------------------------------------------------------------------------

export const templateUpdateSchema = z.object({
	name: z
		.string()
		.trim()
		.min(1, 'Template name is required.')
		.max(255, 'Template name is too long (max 255 characters).'),
	description: z
		.string()
		.trim()
		.max(500, 'Description is too long (max 500 characters).')
		.optional(),
	template_definition: templateDefinitionSchema, // validated above
	status: z.enum(['draft', 'active', 'archived']).optional()
});

export const templateStatusSchema = z.enum(['draft', 'active', 'archived']);

export const updateTemplateContentSchema = z.object({
	name: z.string().min(1, 'Name is required').max(255),
	description: z.string().max(1000).optional(),
	template_definition: z.string().transform((str, ctx) => {
		try {
			const parsed = JSON.parse(str);
			const result = templateDefinitionSchema.safeParse(parsed);
			if (!result.success) {
				ctx.addIssue({
					code: 'custom',
					message: 'Invalid template definition structure'
				});
				return z.NEVER;
			}
			return result.data;
		} catch (e) {
			ctx.addIssue({
				code: 'custom',
				message: 'Invalid JSON'
			});
			return z.NEVER;
		}
	})
});

export const clientUpdateTemplateContentSchema = updateTemplateContentSchema
	.omit({ template_definition: true })
	.extend({
		template_definition: templateDefinitionSchema
	});

export const updateTemplateStatusSchema = z.object({
	status: z.enum(['active', 'archived', 'draft'])
});

export const copyTemplateSchema = z.object({
	templateId: z.string()
});

export const createTemplateSchema = z.object({});
