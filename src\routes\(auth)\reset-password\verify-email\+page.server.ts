import { verifyCodeSchema } from '$lib/schemas/auth';
import {
	getPasswordResetAttemptData,
	verifyPasswordResetOtp
} from '$lib/server/auth/password-reset';
import { ExpiringTokenBucket } from '$lib/server/auth/rate-limit';
import { fail, redirect } from '@sveltejs/kit';

import type { Actions, PageServerLoad } from './$types.js';

const otpVerificationBucket = new ExpiringTokenBucket<string>(5, 60 * 30);

export const load: PageServerLoad = async (event) => {
	const urlToken = event.url.searchParams.get('token');
	if (!urlToken) {
		return redirect(302, '/forgot-password?error=missing_token');
	}
	const attempt = await getPasswordResetAttemptData(urlToken);
	if (!attempt || !attempt.tokenRecord || !attempt.user) {
		return redirect(302, '/forgot-password?error=invalid_token');
	}
	return {
		token: urlToken,
		emailToVerify: attempt.tokenRecord.email
	};
};

export const actions: Actions = {
	default: async (event) => {
		const formData = await event.request.formData();
		const urlToken = formData.get('token') as string | null;

		const validation = verifyCodeSchema.safeParse(Object.fromEntries(formData));

		if (!validation.success) {
			return fail(400, {
				errors: validation.error.flatten().fieldErrors
			});
		}
		const { code } = validation.data;

		if (!urlToken) {
			return fail(400, { message: 'Invalid or missing session token' });
		}

		const attempt = await getPasswordResetAttemptData(urlToken);
		if (!attempt || !attempt.tokenRecord || !attempt.user) {
			return fail(401, { message: 'Invalid or expired session for OTP verification.' });
		}

		const hashedTokenId = attempt.tokenRecord.id;

		if (!otpVerificationBucket.check(hashedTokenId, 1)) {
			return fail(429, { message: 'Too many OTP attempts' });
		}
		otpVerificationBucket.consume(hashedTokenId, 1);

		const otpValid = await verifyPasswordResetOtp(hashedTokenId, code);
		if (!otpValid) {
			return fail(400, {
				message: 'Incorrect code'
			});
		}

		otpVerificationBucket.reset(hashedTokenId);

		// After OTP verification for password reset, proceed to the password reset form.
		// The presence of WebAuthn credentials does not change this step in the flow.
		// Any MFA requirements will be handled at the next actual login attempt.
		redirect(302, `/reset-password?token=${urlToken}`);
	}
};
