<script lang="ts">
	import { applyAction, enhance } from '$app/forms';
	import TemplateEditor from '$lib/components/templates/TemplateEditor.svelte';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import {
		clientUpdateTemplateContentSchema,
	} from '$lib/schemas/template';
	import { confirm } from '$lib/ui/confirm';
	import { toast } from '$lib/ui/toast';
	import IconDelete from '~icons/icon-park-outline/delete';
	import IconEye from '~icons/icon-park-outline/preview-open';
	import IconSave from '~icons/icon-park-outline/save';
	import IconSend from '~icons/icon-park-outline/send';
	import type { ActionData, PageData } from './$types';

	let { data, form: actionResult }: { data: PageData; form: ActionData } = $props();

	const form = createRuneForm(clientUpdateTemplateContentSchema, {
		name: data.template.name,
		description: data.template.description ?? '',
		template_definition: { fields: data.template.template_definition.fields ?? [] }
	});

	// Track the last seen server data to prevent overwriting user edits.
	let lastSeenData = $state(JSON.stringify(data.template));

	$effect(() => {
		const currentDataString = JSON.stringify(data.template);
		// Only update the form if the data from the server has actually changed.
		if (lastSeenData !== currentDataString) {
			form.values = {
				name: data.template.name,
				description: data.template.description ?? '',
				template_definition: { fields: data.template.template_definition.fields ?? [] }
			};
			lastSeenData = currentDataString;
		}
	});

	$effect(() => {
		if (!actionResult) return;

		if (actionResult.updateTemplate) {
			const updateTemplate = actionResult.updateTemplate;
			if ('errors' in updateTemplate && updateTemplate.errors) {
				form.setErrors(updateTemplate.errors);
				if ('root' in updateTemplate.errors && updateTemplate.errors.root) {
					toast.error(updateTemplate.errors.root[0]);
				} else {
					toast.error('Please correct the errors before saving.');
				}
			} else if ('success' in updateTemplate && updateTemplate.success) {
				toast.success('Template saved!');
			}
		}

		if (actionResult.updateStatus) {
			const updateStatus = actionResult.updateStatus;
			if ('errors' in updateStatus && updateStatus.errors) {
				if ('root' in updateStatus.errors && updateStatus.errors.root) {
					toast.error(updateStatus.errors.root[0]);
				} else {
					toast.error('Could not update status.');
				}
			} else if ('success' in updateStatus && updateStatus.success) {
				toast.success('message' in updateStatus ? updateStatus.message : 'Status updated!');
			}
		}

		if (actionResult.deleteTemplate) {
			const deleteTemplate = actionResult.deleteTemplate;
			if ('errors' in deleteTemplate && deleteTemplate.errors && 'root' in deleteTemplate.errors) {
				toast.error(deleteTemplate.errors.root?.[0]);
			}
		}
	});

	async function handleDelete() {
		const isConfirmed = await confirm({
			title: 'Delete Template',
			message: 'Are you sure you want to delete this template? This action cannot be undone.',
			confirmText: 'Delete'
		});

		if (isConfirmed) {
			const deleteForm = document.getElementById('delete-template-form');
			if (deleteForm instanceof HTMLFormElement) deleteForm.requestSubmit();
		}
	}
</script>

<div class="container mx-auto p-4 md:p-6">
	<div class="mb-6 flex items-center justify-between">
		<h1 class="text-3xl font-bold">Edit Template</h1>
		<div class="flex items-center gap-2">
			<a
				href="/app/templates/{data.template.id}/preview"
				class="btn btn-outline"
				target="_blank"
				rel="noopener noreferrer"
			>
				<IconEye />
				Preview
			</a>
			<form
				method="POST"
				action="?/updateStatus"
				use:enhance={() => {
					return async ({ result }) => {
						await applyAction(result);
					};
				}}
			>
				<input type="hidden" name="status" value="active" />
				<button class="btn btn-accent">
					<IconSend />
					Publish
				</button>
			</form>
			<button class="btn btn-error btn-outline" onclick={handleDelete}>
				<IconDelete />
				Delete
			</button>
		</div>
	</div>
	<form
		id="delete-template-form"
		method="POST"
		action="?/deleteTemplate"
		use:enhance
		class="hidden"
	></form>

	<RuneForm form={form}>
		{#snippet children(form)}
			<form
				method="POST"
				action="?/updateTemplate"
				use:enhance={({ formData }) => {
					formData.set(
						'template_definition',
						JSON.stringify({ fields: form.values.template_definition.fields })
					);
					form.setSubmitting(true);
					return async ({ update }) => {
						await update({ reset: false });
						form.setSubmitting(false);
					};
				}}
				onsubmit={form.handleSubmit()}
				class="space-y-6"
			>
				<TemplateEditor>
					{#snippet children()}
						<div class="card bg-base-100/80 shadow-xl">
							<div class="card-body">
								<h2 class="card-title text-2xl">Template Settings</h2>
								<Field name="name">
									{#snippet children(field)}
										<div class="form-control">
											<label for="template-name" class="label">
												<span class="label-text">Template Name</span>
											</label>
											<input
												id="template-name"
												name="name"
												type="text"
												class="input input-bordered w-full"
												value={field.value ?? ''}
												oninput={field.handleChange}
												onblur={field.handleBlur}
												required
											/>
											<Errors name="name" />
										</div>
									{/snippet}
								</Field>

								<Field name="description">
									{#snippet children(field)}
										<div class="form-control">
											<label for="template-description" class="label">
												<span class="label-text">Description</span>
											</label>
											<textarea
												id="template-description"
												name="description"
												class="textarea textarea-bordered h-24"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											></textarea>
											<Errors name="description" />
										</div>
									{/snippet}
								</Field>
								<Errors name="root" />
							</div>
						</div>
					{/snippet}
				</TemplateEditor>
				<div class="flex justify-end pt-4">
					<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
						{#if form.isSubmitting}
							<span class="loading loading-spinner"></span>
							Saving...
						{:else}
							<IconSave class="h-5 w-5" />
							Save Template
						{/if}
					</button>
				</div>
			</form>
		{/snippet}
	</RuneForm>
</div>
