<!--
  ALPHA FEATURE: Inventory Management - Item Display Card

  A card component to display a single inventory item in a grid.
  Includes a hover effect for visual feedback and status indicators.

  Features:
  - Visual status indicators (error/warning/success) based on quantity vs reorder threshold
  - Mobile-optimized touch targets and responsive design
  - Cost/price display with currency formatting
  - Integration with inventory item detail pages
  - Hover effects and modern card styling

  This component is part of the inventory management system that is currently in alpha status.
-->
<script lang="ts">
	import type { InventoryItem } from '$lib/server/db/schema';
	import { formatCurrency } from '$lib/utils/format';

	let { item }: { item: InventoryItem } = $props();



	function getQuantityStatus(quantity: number, reorderThreshold?: number | null) {
		if (quantity === 0) return 'status-error';
		if (reorderThreshold !== null && reorderThreshold !== undefined && quantity <= reorderThreshold) return 'status-warning';
		return 'status-success';
	}
</script>

<!-- Mobile-Optimized Inventory Card -->
<a
	href="/app/inventory/{item.id}"
	class="group card bg-base-100/80 border border-base-300/30 shadow-sm transition-all duration-200 hover:shadow-md hover:border-base-300/50 rounded-xl touch-manipulation"
>
	<!-- Mobile-Friendly Status Indicator -->
	<div class="absolute top-2 right-2 z-10">
		<div class="w-2 h-2 lg:w-1.5 lg:h-1.5 rounded-full {getQuantityStatus(item.quantity, item.reorder_threshold) === 'status-error' ? 'bg-error' : getQuantityStatus(item.quantity, item.reorder_threshold) === 'status-warning' ? 'bg-warning' : 'bg-success'}"></div>
	</div>

	<!-- Mobile-Optimized Image Section -->
	<figure class="aspect-square w-full relative overflow-hidden">
		{#if item.image_base64}
			<img
				src={item.image_base64}
				alt={item.name}
				class="h-full w-full object-cover rounded-t-xl"
				loading="lazy"
			/>
		{:else}
			<div class="bg-base-200/50 flex h-full w-full items-center justify-center rounded-t-xl">
				<div class="text-center">
					<div class="w-10 h-10 lg:w-12 lg:h-12 mx-auto mb-2 bg-base-content/10 rounded-full flex items-center justify-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="text-base-content/40 h-5 w-5 lg:h-6 lg:w-6"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="1.5"
							stroke-linecap="round"
							stroke-linejoin="round"
						>
							<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
							<polyline points="17 8 12 3 7 8" />
							<line x1="12" x2="12" y1="3" y2="15" />
						</svg>
					</div>
					<p class="text-xs text-base-content/50">No Image</p>
				</div>
			</div>
		{/if}
	</figure>

	<!-- Mobile-Optimized Card Body -->
	<div class="card-body p-3 lg:p-4">
		<!-- Title Section -->
		<div class="mb-3">
			<h2 class="text-sm lg:text-base font-semibold text-base-content/90 line-clamp-2 group-hover:text-primary transition-colors duration-200 leading-tight">
				{item.name}
			</h2>
			{#if item.description}
				<p class="text-xs lg:text-sm text-base-content/60 line-clamp-1 mt-1">{item.description}</p>
			{/if}
		</div>

		<!-- Mobile-Optimized Stats Section -->
		<div class="flex items-center justify-between mb-3">
			<div class="flex items-center gap-2">
				<div class="badge badge-ghost badge-sm lg:badge-md">
					<span class="font-semibold">{item.quantity}</span>
				</div>
				{#if item.barcode}
					<div class="w-1 h-1 bg-primary/40 rounded-full"></div>
				{/if}
			</div>
			<div class="text-right">
				<p class="font-medium text-sm lg:text-base text-base-content/90">{formatCurrency(item.price_in_cents)}</p>
				<p class="text-xs text-base-content/60">Price</p>
			</div>
		</div>

		<!-- Mobile-Friendly Reorder Alert -->
		{#if item.reorder_threshold !== null && item.reorder_threshold !== undefined && item.quantity <= item.reorder_threshold}
			<div class="bg-warning/10 border border-warning/20 rounded-lg p-2">
				<div class="flex items-center gap-2">
					<div class="w-1.5 h-1.5 bg-warning rounded-full flex-shrink-0"></div>
					<span class="text-xs text-warning font-medium">Low stock</span>
				</div>
			</div>
		{/if}
	</div>
</a>
