import { clientUpsertSchema } from '$lib/schemas/client';
import { db } from '$lib/server/db';
import { clients } from '$lib/server/db/schema';
import { processImage } from '$lib/server/utils/imageProcessing';
import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	const user = locals.user;
	if (!user) {
		redirect(302, '/login');
	}

	const userClients = await db.query.clients.findMany({
		where: eq(clients.user_id, user.id),
		orderBy: (clients, { desc }) => [desc(clients.created_at)]
	});

	return {
		clients: userClients
	};
};

export const actions: Actions = {
	createClient: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { errors: { root: ['Unauthorized'] } });
		}

		const formData = Object.fromEntries(await request.formData());
		const validation = clientUpsertSchema.safeParse(formData);

		if (!validation.success) {
			const errors = validation.error.flatten().fieldErrors;
			return fail(400, {
				data: formData,
				errors
			});
		}

		const values = validation.data;

		try {
			// Process image if provided
			let processedImageBase64: string | null = values.image_base64 || null;
			if (values.image_base64 && values.image_base64.trim() !== '') {
				try {
					processedImageBase64 = await processImage(values.image_base64, {
						maxWidth: 1600,
						maxHeight: 1200,
						quality: 80,
						format: 'jpeg'
					});
				} catch (imageError) {
					console.error('Image processing error:', imageError);
					return fail(400, {
						data: formData,
						errors: { image_base64: ['Failed to process image. Please try a different image.'] }
					});
				}
			} else if (values.image_base64 === '') {
				// No image provided
				processedImageBase64 = null;
			}

			const [newClient] = await db
				.insert(clients)
				.values({
					...values,
					image_base64: processedImageBase64,
					user_id: user.id
				})
				.returning({ id: clients.id });

			if (!newClient) {
				return fail(500, { data: formData, errors: { root: ['Failed to create client'] } });
			}

			return { success: true };
		} catch (error) {
			console.error('Error creating client:', error);
			return fail(500, {
				data: formData,
				errors: { root: ['Failed to create client. Please try again.'] }
			});
		}
	}
};
