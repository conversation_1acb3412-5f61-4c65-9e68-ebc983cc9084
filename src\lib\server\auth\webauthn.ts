import { db } from '$lib/server/db';
import { webauthn_challenges, webauthn_credentials } from '$lib/server/db/schema';
import { decodeBase64url, encodeBase64url } from '@oslojs/encoding';
import type {
	AuthenticationResponseJSON,
	AuthenticatorTransport,
	RegistrationResponseJSON,
	VerifiedAuthenticationResponse,
	VerifiedRegistrationResponse
} from '@simplewebauthn/server';
import {
	generateAuthenticationOptions,
	generateRegistrationOptions,
	verifyAuthenticationResponse,
	verifyRegistrationResponse
} from '@simplewebauthn/server';
import { error } from '@sveltejs/kit';
import { and, eq, gt } from 'drizzle-orm';
import { EXPECTED_ORIGIN, RP_ID, RP_NAME } from './webauthn_internals';

// Re-export for other modules that have trouble with the $lib alias
export { EXPECTED_ORIGIN, RP_ID, RP_NAME };

const CHALLENGE_EXPIRY_MS = 10 * 60 * 1000; // 10 minutes, for example

/**
 * Start WebAuthn registration process for a user
 */
export async function startRegistration(userId: string, userName: string, userDisplayName: string) {
	// Get existing credentials for the user to prevent duplicates
	const existingCredentials = await db.query.webauthn_credentials.findMany({
		where: eq(webauthn_credentials.user_id, userId)
	});

	// Remove any existing challenges for this user to prevent race conditions
	await db.delete(webauthn_challenges).where(eq(webauthn_challenges.user_id, userId));

	// Generate registration options
	const options = await generateRegistrationOptions({
		rpName: RP_NAME,
		rpID: RP_ID,
		userID: new TextEncoder().encode(userId),
		userName,
		userDisplayName,
		attestationType: 'none',
		excludeCredentials: existingCredentials.map((cred) => ({
			id: cred.credential_id,
			type: 'public-key',
			transports: cred.transports?.split(',') as AuthenticatorTransport[] | undefined
		})),
		authenticatorSelection: {
			residentKey: 'required',
			userVerification: 'preferred'
		}
	});

	// Store challenge for verification in the database
	await db.insert(webauthn_challenges).values({
		id: options.challenge,
		user_id: userId,
		expires_at: new Date(Date.now() + CHALLENGE_EXPIRY_MS)
	});

	return options;
}

/**
 * Verify registration response from client
 */
export async function verifyRegistration(
	userId: string,
	response: RegistrationResponseJSON,
	name: string
) {
	// Get stored challenge from DB
	const storedChallenge = await db.query.webauthn_challenges.findFirst({
		where: and(
			eq(webauthn_challenges.user_id, userId),
			gt(webauthn_challenges.expires_at, new Date())
		)
	});

	if (!storedChallenge) {
		throw error(400, 'Registration challenge not found, invalid for the user, or expired');
	}

	// Verify registration
	let verification: VerifiedRegistrationResponse;
	try {
		verification = await verifyRegistrationResponse({
			response,
			expectedChallenge: storedChallenge.id,
			expectedOrigin: EXPECTED_ORIGIN,
			expectedRPID: RP_ID,
			requireUserVerification: false
		});
	} catch (err) {
		console.error('Registration verification error:', err);
		// Delete the used/failed challenge
		await db.delete(webauthn_challenges).where(eq(webauthn_challenges.id, storedChallenge.id));
		throw error(400, 'Registration verification failed due to server error or invalid response');
	}

	// Delete the challenge after use
	await db.delete(webauthn_challenges).where(eq(webauthn_challenges.id, storedChallenge.id));

	if (!verification.verified || !verification.registrationInfo) {
		throw error(400, 'Registration verification failed by library or missing info');
	}

	const regInfo = verification.registrationInfo;
	if (!regInfo) {
		throw error(400, 'Registration verification successful, but registration info was missing.');
	}

	// Upsert the credential to the database
	await db
		.insert(webauthn_credentials)
		.values({
			user_id: userId,
			credential_id: regInfo.credential.id,
			public_key: encodeBase64url(regInfo.credential.publicKey),
			counter: regInfo.credential.counter,
			transports: response.response.transports?.join(','),
			device_type: regInfo.credentialDeviceType || response.authenticatorAttachment || null,
			created_at: new Date(),
			last_used_at: new Date(),
			backed_up: regInfo.credentialBackedUp,
			name: name
		})
		.onConflictDoUpdate({
			target: webauthn_credentials.credential_id,
			set: {
				public_key: encodeBase64url(regInfo.credential.publicKey),
				counter: regInfo.credential.counter,
				last_used_at: new Date(),
				name: name
			}
		});

	return true;
}

/**
 * Start WebAuthn authentication process
 */
export async function startAuthentication() {
	const options = await generateAuthenticationOptions({
		rpID: RP_ID,
		userVerification: 'preferred'
	});

	return options;
}

/**
 * Verify authentication response from client
 */
export async function verifyAuthentication(
	response: AuthenticationResponseJSON,
	expectedChallenge: string
) {
	if (!response.response.userHandle) {
		throw error(400, 'Authentication response missing userHandle');
	}
	const userId = new TextDecoder().decode(decodeBase64url(response.response.userHandle));

	const credential = await db.query.webauthn_credentials.findFirst({
		where: and(
			eq(webauthn_credentials.user_id, userId),
			eq(webauthn_credentials.credential_id, response.id)
		)
	});

	if (!credential) {
		throw error(400, 'Credential not found for this user.');
	}

	let verification: VerifiedAuthenticationResponse;
	try {
		verification = await verifyAuthenticationResponse({
			response,
			expectedChallenge,
			expectedOrigin: EXPECTED_ORIGIN,
			expectedRPID: RP_ID,
			credential: {
				id: credential.credential_id,
				publicKey: decodeBase64url(credential.public_key),
				counter: credential.counter,
				transports: credential.transports?.split(',') as AuthenticatorTransport[] | undefined
			},
			requireUserVerification: false
		});
	} catch (err) {
		console.error('Authentication verification error:', err);
		throw error(400, 'Authentication verification failed.');
	}

	if (!verification.verified || !verification.authenticationInfo) {
		throw error(400, 'Could not verify authentication response.');
	}

	const { newCounter } = verification.authenticationInfo;
	await db
		.update(webauthn_credentials)
		.set({ counter: newCounter, last_used_at: new Date() })
		.where(eq(webauthn_credentials.credential_id, response.id));

	return {
		verified: true,
		userId
	};
}
