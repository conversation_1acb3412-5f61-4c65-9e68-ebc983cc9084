import { z } from 'zod/v4';

/**
 * Shared validation primitives to eliminate duplication across schema files
 * These primitives can be composed into larger schemas
 */

// ============================================================================
// BASIC FIELD PRIMITIVES
// ============================================================================

/**
 * Email validation with consistent error message
 */
import { ERROR_MESSAGES } from '$lib/config';

export const emailField = z
	.string()
	.trim()
	.email({ message: ERROR_MESSAGES.INVALID_EMAIL })
	.max(255, 'Email address is too long');

/**
 * Password validation for new passwords
 */
export const passwordField = z
	.string()
	.min(8, { message: ERROR_MESSAGES.PASSWORD_TOO_SHORT })
	.max(255, 'Password is too long');

/**
 * Password validation for login (less strict)
 */
export const loginPasswordField = z
	.string()
	.min(1, { message: ERROR_MESSAGES.REQUIRED_FIELD });

/**
 * First name validation
 */
export const firstNameField = z
	.string()
	.trim()
	.min(1, { message: ERROR_MESSAGES.REQUIRED_FIELD })
	.max(100, 'First name is too long');

/**
 * Last name validation
 */
export const lastNameField = z
	.string()
	.trim()
	.min(1, { message: ERROR_MESSAGES.REQUIRED_FIELD })
	.max(100, 'Last name is too long');

/**
 * Optional last name validation
 */
export const optionalLastNameField = z
	.string()
	.trim()
	.max(100, 'Last name is too long')
	.optional();

/**
 * Phone number validation
 */
export const phoneNumberField = z
	.string()
	.trim()
	.max(20, 'Phone number is too long')
	.optional();

/**
 * Notes field validation
 */
export const notesField = z
	.string()
	.trim()
	.max(5000, 'Notes are too long')
	.optional();

/**
 * Description field validation
 */
export const descriptionField = z
	.string()
	.trim()
	.max(1000, 'Description is too long')
	.optional();

/**
 * Name field validation (for items, templates, etc.)
 */
export const nameField = z
	.string()
	.trim()
	.min(1, ERROR_MESSAGES.REQUIRED_FIELD)
	.max(255, 'Name is too long');

/**
 * UUID validation
 */
export const uuidField = z.string().uuid('Invalid ID format');

// ============================================================================
// SPECIALIZED FIELD PRIMITIVES
// ============================================================================

/**
 * Base64 image validation regex
 */
export const base64ImageRegex = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;

/**
 * Base64 image field validation
 */
export const imageBase64Field = z
	.string()
	.refine((val) => val === '' || base64ImageRegex.test(val), {
		message: 'Image must be a valid base64 encoded data URL'
	})
	.optional();

/**
 * Barcode field validation
 */
export const barcodeField = z
	.string()
	.trim()
	.max(50, 'Barcode is too long')
	.optional();

/**
 * Required barcode field validation
 * Used by ALPHA FEATURE: Inventory Management for barcode scanning workflows
 */
export const requiredBarcodeField = z
	.string()
	.trim()
	.min(1, ERROR_MESSAGES.REQUIRED_FIELD)
	.max(50, 'Barcode is too long');

/**
 * Quantity validation (non-negative integer)
 * Used by ALPHA FEATURES: Inventory Management and Shopping List for stock/quantity tracking
 */
export const quantityField = z
	.coerce
	.number()
	.int('Quantity must be a whole number')
	.min(0, 'Quantity cannot be negative');

/**
 * Price in cents validation (optional)
 * Used by ALPHA FEATURE: Inventory Management for selling price tracking
 */
export const optionalPriceField = z
	.coerce
	.number()
	.int('Price must be in cents (whole number)')
	.min(0, 'Price cannot be negative')
	.optional();

/**
 * Cost in cents validation (required, can be 0)
 * Used by ALPHA FEATURES: Inventory Management and Shopping List for purchase cost tracking
 */
export const costField = z
	.coerce
	.number()
	.int('Cost must be in cents (whole number)')
	.min(0, 'Cost cannot be negative');

/**
 * Reorder threshold validation
 */
export const reorderThresholdField = z
	.coerce
	.number()
	.int('Reorder threshold must be a whole number')
	.min(0, 'Reorder threshold cannot be negative')
	.optional();

/**
 * Supplier field validation
 */
export const supplierField = z
	.string()
	.trim()
	.max(255, 'Supplier name is too long')
	.optional();

// ============================================================================
// COMPOUND VALIDATION PATTERNS
// ============================================================================

/**
 * Password confirmation validation
 * Returns a refine function that can be used with z.object().refine()
 */
export function passwordConfirmationRefine(
	passwordField: string = 'password',
	confirmField: string = 'confirm_password'
) {
	return (data: Record<string, any>) => data[passwordField] === data[confirmField];
}

/**
 * Password confirmation error configuration
 */
export const passwordConfirmationError = {
	message: "Passwords don't match",
	path: ['confirm_password']
};

/**
 * Email availability validation (for use with async refinement)
 * This is a helper type - actual validation should be done server-side
 */
export type EmailAvailabilityValidator = (email: string) => Promise<boolean>;

/**
 * Password strength validation (for use with async refinement)
 * This is a helper type - actual validation should be done server-side
 */
export type PasswordStrengthValidator = (password: string) => Promise<boolean>;

// ============================================================================
// COMMON SCHEMA COMPOSITIONS
// ============================================================================

/**
 * Basic user identification fields
 */
export const userIdentificationSchema = z.object({
	first_name: firstNameField,
	email: emailField
});

/**
 * Full user profile fields
 */
export const userProfileSchema = z.object({
	first_name: firstNameField,
	last_name: optionalLastNameField,
	email: emailField,
	phone_number: phoneNumberField
});

/**
 * Basic login credentials
 */
export const loginCredentialsSchema = z.object({
	email: emailField,
	password: loginPasswordField
});

/**
 * Registration credentials with confirmation
 */
export const registrationCredentialsSchema = z
	.object({
		first_name: firstNameField,
		email: emailField,
		password: passwordField,
		confirm_password: z.string()
	})
	.refine(passwordConfirmationRefine(), passwordConfirmationError);

/**
 * Timestamped entity fields
 */
export const timestampedEntitySchema = z.object({
	created_at: z.date(),
	updated_at: z.date()
});

/**
 * User-owned entity fields
 */
export const userOwnedEntitySchema = z.object({
	user_id: uuidField
});

/**
 * Complete entity base (ID + timestamps + user ownership)
 */
export const baseEntitySchema = z.object({
	id: uuidField,
	user_id: uuidField,
	created_at: z.date(),
	updated_at: z.date()
});
