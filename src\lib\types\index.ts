/**
 * Unified type definitions for the Hairloom CRM application
 * Consolidates all TypeScript interfaces and types used across components
 */

// ============================================================================
// CORE DOMAIN TYPES
// ============================================================================

/**
 * Unified inventory item interface
 * Consolidates all inventory item representations across the app
 */
export interface InventoryItem {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  sku?: string | null;
  barcode: string | null;
  quantity: number;
  price_in_cents: number | null; // Selling price - what you charge customers
  cost_in_cents: number; // Purchase cost - what you pay to acquire the item
  reorder_threshold: number | null;
  supplier: string | null;
  image_base64: string | null;
  created_at: Date;
  updated_at: Date;
}

/**
 * Client interface
 */
export interface Client {
  id: string;
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  phone_number: string | null;
  notes: string | null;
  image_base64: string | null;
  created_at: Date;
  updated_at: Date;
}

/**
 * Shopping list item interface
 */
export interface ShoppingListItem {
  id: string;
  user_id: string;
  inventory_item_id: string;
  quantity: number;
  purchased: boolean;
  created_at: Date;
  updated_at: Date;
  // Joined fields from inventory_items
  item_name?: string;
  item_cost_in_cents?: number;
  item_barcode?: string;
}

/**
 * Template interface
 */
export interface Template {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  definition: TemplateDefinition;
  status: 'draft' | 'active' | 'archived';
  created_at: Date;
  updated_at: Date;
}

/**
 * Template definition structure
 */
export interface TemplateDefinition {
  sections: TemplateSection[];
  metadata?: {
    version?: string;
    author?: string;
    tags?: string[];
  };
}

export interface TemplateSection {
  id: string;
  title: string;
  fields: TemplateField[];
  order: number;
}

export interface TemplateField {
  id: string;
  type: 'text' | 'textarea' | 'number' | 'date' | 'checkbox' | 'select';
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: string[]; // For select fields
  order: number;
}

/**
 * User interface
 */
export interface User {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  email_verified: boolean;
  preferences: UserPreferences;
  created_at: Date;
  updated_at: Date;
}

/**
 * User preferences
 */
export interface UserPreferences {
  hiddenSystemTemplates: string[];
  theme?: 'light' | 'dark' | 'auto';
  sidebarCollapsed?: boolean;
  defaultCameraFacing?: 'user' | 'environment';
}

// ============================================================================
// FORM DATA TYPES
// ============================================================================

/**
 * Form data for creating/updating inventory items
 */
export interface InventoryItemFormData {
  name: string;
  description: string;
  barcode: string;
  quantity: number;
  price_in_cents: number | undefined;
  cost_in_cents: number;
  reorder_threshold: number | undefined;
  supplier: string;
  image_base64: string;
}

/**
 * Form data for creating/updating clients
 */
export interface ClientFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  notes: string;
  image_base64: string;
}

/**
 * Form data for authentication
 */
export interface LoginFormData {
  email: string;
  password: string;
}

export interface RegisterFormData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

/**
 * Standard API response format
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  details?: Record<string, unknown>;
  code?: string;
  timestamp?: string;
  message?: string;
}

/**
 * Paginated response format
 */
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * Search response format
 */
export interface SearchResponse<T> {
  items: T[];
  query: string;
  total: number;
  took: number; // Search time in milliseconds
}

// ============================================================================
// COMPONENT PROP TYPES
// ============================================================================

/**
 * Modal component props
 */
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
}

/**
 * Button component props
 */
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  loading?: boolean;
  class?: string;
  href?: string;
  onclick?: () => void;
}

/**
 * Card component props
 */
export interface CardProps {
  title?: string;
  subtitle?: string;
  class?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
}

/**
 * Image upload component props
 */
export interface ImageUploadProps {
  value?: string;
  onChange: (base64: string) => void;
  config?: ImageUploadConfig;
  disabled?: boolean;
}

export interface ImageUploadConfig {
  maxSizeKB: number;
  acceptedTypes: string[];
  showPreview: boolean;
  showCameraButton: boolean;
  showFileButton: boolean;
  placeholder: string;
  buttonText: string;
  cameraText: string;
  removeText: string;
  errorMessages: {
    fileSize: string;
    fileType: string;
    upload: string;
  };
  styling: {
    containerClass: string;
    buttonClass: string;
    previewClass: string;
  };
}

// ============================================================================
// SCANNER AND CAMERA TYPES
// ============================================================================

/**
 * Barcode scanner props
 */
export interface BarcodeScannerProps {
  onScan: (barcode: string) => void;
  onError: (error: string) => void;
  isActive: boolean;
  facingMode?: 'user' | 'environment';
}

/**
 * Camera error types
 */
export type CameraErrorType = 
  | 'NotAllowedError'
  | 'NotFoundError'
  | 'NotReadableError'
  | 'OverconstrainedError'
  | 'SecurityError'
  | 'AbortError'
  | 'NotSupportedError'
  | 'TypeError';

// ============================================================================
// QUICK RESTOCK TYPES
// ============================================================================

/**
 * Quick restock modal types
 */
export interface QuickRestockItem {
  id: string;
  name: string;
  description: string | null;
  barcode: string;
  current_quantity: number;
  reorder_threshold: number | null;
}

export interface QuickRestockResponse {
  success?: boolean;
  needsQuantity?: boolean;
  item?: QuickRestockItem | QuickRestockCompletedItem;
  error?: string;
}

export interface QuickRestockCompletedItem {
  id: string;
  name: string;
  description: string | null;
  barcode: string;
  previous_quantity: number;
  quantity_added_to_shopping_list: number;
  used_reorder_threshold: boolean;
}

// ============================================================================
// SESSION AND UPDATE MODE TYPES
// ============================================================================

/**
 * Update mode session item
 */
export interface UpdateModeSessionItem {
  action: 'updated' | 'added';
  item: InventoryItem;
  timestamp: Date;
}

// ============================================================================
// TOAST AND NOTIFICATION TYPES
// ============================================================================

/**
 * Toast message types
 */
export interface ToastMessage {
  id: number;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning' | 'loading';
  duration: number;
  timestamp: Date;
}

export interface ToastOptions {
  duration?: number;
}

// ============================================================================
// CONFIRMATION MODAL TYPES
// ============================================================================

/**
 * Confirmation modal configuration
 */
export interface ConfirmationConfig {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmClass?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Generic ID type
 */
export type ID = string;

/**
 * Currency amount in cents
 */
export type CurrencyAmount = number;

/**
 * Timestamp type
 */
export type Timestamp = Date | string;

/**
 * Optional fields helper
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Create type for database inserts (without id, timestamps)
 */
export type CreateType<T> = Omit<T, 'id' | 'created_at' | 'updated_at'>;

/**
 * Update type for database updates (partial, without id, timestamps)
 */
export type UpdateType<T> = Partial<Omit<T, 'id' | 'created_at' | 'updated_at'>>;

// ============================================================================
// ERROR TYPES
// ============================================================================

/**
 * Application error types
 */
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown> | string[] | string | null;
  timestamp: Date;
}

/**
 * Validation error
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// ============================================================================
// EXPORT CONVENIENCE TYPES
// ============================================================================

// Re-export commonly used types for convenience
export type { Snippet } from 'svelte';
export type { ZodType } from 'zod/v4';

