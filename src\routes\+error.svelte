<!--
Error page for the application.
A clean, helpful error page that displays error information and provides navigation options.
-->
<script lang="ts">
	import { dev } from '$app/environment';
	import { page } from '$app/state';
	import { REGEX_PATTERNS, ERROR_MESSAGES } from '$lib/config';
	import IconError from '~icons/icon-park-outline/error';

	function goBack() {
		history.back();
	}

	// Sanitize error messages in production
	function getSafeErrorMessage(errorMessage: string | undefined): string | undefined {
		if (dev) {
			return errorMessage; // Show full error in development
		}
		
		// In production, only show safe, generic error messages
		if (!errorMessage) return undefined;
		
		// Allow only specific safe error patterns
		for (const pattern of REGEX_PATTERNS.SAFE_ERROR_PATTERNS) {
			if (pattern.test(errorMessage)) {
				return errorMessage;
			}
		}

		// Return generic message for any other errors in production
		return ERROR_MESSAGES.GENERIC_ERROR;
	}

	const safeErrorMessage = $derived(getSafeErrorMessage(page.error?.message));
</script>

<div class="hero bg-base-200 min-h-screen" data-testid="error-page-container">
	<div class="hero-content text-center">
		<div
			class="bg-base-100 rounded-box max-w-lg space-y-6 p-8 shadow-xl md:p-12"
			data-testid="error-page-card"
		>
			<div class="flex flex-col items-center space-y-4">
				<div class="text-error text-7xl font-bold" data-testid="error-page-status-code">
					{page.status}
				</div>
				<h1
					class="text-base-content text-3xl font-semibold md:text-4xl"
					data-testid="error-page-title"
				>
					{#if page.status === 404}
						Page Not Found
					{:else if page.status === 403}
						Access Denied
					{:else if page.status === 500}
						Server Error
					{:else}
						Something Went Wrong
					{/if}
				</h1>
				<p class="text-base-content/80" data-testid="error-page-description">
					{#if page.status === 404}
						The page you're looking for doesn't exist or has been moved.
					{:else if page.status === 403}
						You don't have permission to access this resource.
					{:else if page.status === 500}
						We're experiencing some technical difficulties. Please try again later.
					{:else}
						An unexpected error occurred. Please try again or contact support if the problem
						persists.
					{/if}
				</p>
			</div>

			{#if safeErrorMessage}
				<div role="alert" class="alert alert-error flex items-center pr-2 shadow-lg">
					<IconError class="h-6 w-6 shrink-0" />
					<span class="flex-grow text-sm">{safeErrorMessage}</span>
				</div>
			{/if}

			<div class="flex flex-col gap-4 sm:flex-row">
				<button class="btn btn-primary" onclick={goBack} data-testid="error-page-back-button">
					Go Back
				</button>
				<a href="/" class="btn btn-outline" data-testid="error-page-home-link">Go Home</a>
			</div>
		</div>
	</div>
</div>
