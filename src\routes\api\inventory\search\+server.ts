import { db } from '$lib/server/db';
import { inventory_items } from '$lib/server/db/schema';
import {
	createErrorResponse,
	createJsonResponse,
	withAuthAndErrorHandling
} from '$lib/server/utils/api-response';
import { and, eq, ilike, or } from 'drizzle-orm';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = withAuthAndErrorHandling(async ({ url, locals }) => {
	// User is guaranteed to exist due to withAuthAndErrorHandling
	const user = locals.user!;
	const query = url.searchParams.get('q');
	const barcode = url.searchParams.get('barcode');

	if (!query && !barcode) {
		return createErrorResponse(
			'Query parameter "q" or "barcode" is required',
			'BAD_REQUEST',
			undefined,
			400
		);
	}

	let items;

		if (barcode) {
			// Search specifically by barcode
			items = await db
				.select({
					id: inventory_items.id,
					name: inventory_items.name,
					description: inventory_items.description,
					sku: inventory_items.sku,
					barcode: inventory_items.barcode,
					quantity: inventory_items.quantity,
					price_in_cents: inventory_items.price_in_cents,
					cost_in_cents: inventory_items.cost_in_cents,
					reorder_threshold: inventory_items.reorder_threshold,
					supplier: inventory_items.supplier,
					image_base64: inventory_items.image_base64
				})
				.from(inventory_items)
				.where(
					and(
						eq(inventory_items.user_id, user.id),
						eq(inventory_items.barcode, barcode)
					)
				)
				.limit(1);
		} else {
			// General search across multiple fields
			const searchTerm = `%${query}%`;
			items = await db
				.select({
					id: inventory_items.id,
					name: inventory_items.name,
					description: inventory_items.description,
					sku: inventory_items.sku,
					barcode: inventory_items.barcode,
					quantity: inventory_items.quantity,
					price_in_cents: inventory_items.price_in_cents,
					cost_in_cents: inventory_items.cost_in_cents,
					reorder_threshold: inventory_items.reorder_threshold,
					supplier: inventory_items.supplier,
					image_base64: inventory_items.image_base64
				})
				.from(inventory_items)
				.where(
					and(
						eq(inventory_items.user_id, user.id),
						or(
							ilike(inventory_items.name, searchTerm),
							ilike(inventory_items.description, searchTerm),
							ilike(inventory_items.sku, searchTerm),
							ilike(inventory_items.barcode, searchTerm),
							ilike(inventory_items.supplier, searchTerm)
						)
					)
				)
				.limit(10);
		}

		return createJsonResponse({ items });
});
