import { db } from '$lib/server/db';
import { webauthn_credentials } from '$lib/server/db/schema.js';
import { error, json } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';

export async function DELETE({ request, locals, params }) {
	if (!locals.user) {
		error(401, 'Unauthorized');
	}

	const credentialId = params.credentialId;

	if (!credentialId) {
		error(400, 'Invalid request: credentialId is missing.');
	}

	try {
		const [deleted] = await db
			.delete(webauthn_credentials)
			.where(
				and(
					eq(webauthn_credentials.id, credentialId),
					eq(webauthn_credentials.user_id, locals.user.id)
				)
			)
			.returning();

		if (!deleted) {
			error(404, 'Passkey not found or you do not have permission to delete it.');
		}

		return json({ success: true, message: 'Passkey deleted successfully.' });
	} catch (e) {
		console.error('Failed to delete passkey:', e);
		error(500, 'Failed to delete passkey due to a server error.');
	}
}
