import { db } from '$lib/server/db';
import {
    email_verification_tokens,
    users,
    type EmailVerificationToken,
    type User
} from '$lib/server/db/schema';
import { sendEmail } from '$lib/server/utils/email';
import { sha256 } from '@oslojs/crypto/sha2';
import { encodeHexLowerCase } from '@oslojs/encoding';
import { and, eq } from 'drizzle-orm';
import { ExpiringTokenBucket } from './rate-limit';
import { createTokenPair, TOKEN_DURATIONS } from './token-manager';
import { hashOtp } from './utils';


// Rate limiter instance (key type string for userId or email)
export const sendVerificationEmailBucket = new ExpiringTokenBucket<string>(3, 60 * 10);

export async function createEmailVerificationAttempt(
	userId: string,
	emailToVerify: string
): Promise<{ plaintextUrlToken: string; plaintextOtp: string; expiresAt: Date } | null> {
	try {
		await db
			.delete(email_verification_tokens)
			.where(
				and(
					eq(email_verification_tokens.user_id, userId),
					eq(email_verification_tokens.email, emailToVerify)
				)
			);
	} catch {
		// HL: Intentionally ignoring error if deletion fails (e.g., no prior tokens for this user/email).
		// This allows the creation of a new token to proceed regardless.
	}

	const tokenPair = await createTokenPair({
		durationMinutes: TOKEN_DURATIONS.EMAIL_VERIFICATION
	});

	try {
		await db.insert(email_verification_tokens).values({
			id: tokenPair.hashedUrlTokenId,
			user_id: userId,
			email: emailToVerify,
			hashed_otp_code: tokenPair.hashedOtpForDb,
			expires_at: tokenPair.expiresAt
		});
		return {
			plaintextUrlToken: tokenPair.plaintextUrlToken,
			plaintextOtp: tokenPair.plaintextOtp,
			expiresAt: tokenPair.expiresAt
		};
	} catch (error) {
		console.error('Failed to create email verification attempt:', error);
		return null;
	}
}

export async function getEmailVerificationAttemptData(
	plaintextUrlToken: string
): Promise<{ tokenRecord: EmailVerificationToken; user: User } | null> {
	const hashedUrlTokenId = encodeHexLowerCase(sha256(new TextEncoder().encode(plaintextUrlToken)));

	const result = await db
		.select({
			tokenRecord: email_verification_tokens,
			user: users
		})
		.from(email_verification_tokens)
		.innerJoin(users, eq(email_verification_tokens.user_id, users.id))
		.where(eq(email_verification_tokens.id, hashedUrlTokenId))
		.limit(1);

	if (result.length === 0 || !result[0]) {
		return null;
	}

	const { tokenRecord, user } = result[0];

	if (new Date() >= new Date(tokenRecord.expires_at)) {
		await db
			.delete(email_verification_tokens)
			.where(eq(email_verification_tokens.id, tokenRecord.id)); // Delete expired token
		return null;
	}
	return { tokenRecord, user };
}

export async function verifyEmailVerificationOtp(
	hashedUrlTokenIdForAttempt: string,
	plaintextOtp: string
): Promise<boolean> {
	const result = await db
		.select({ hashed_otp_code: email_verification_tokens.hashed_otp_code })
		.from(email_verification_tokens)
		.where(eq(email_verification_tokens.id, hashedUrlTokenIdForAttempt))
		.limit(1);

	if (result.length === 0 || !result[0] || !result[0].hashed_otp_code) {
		return false;
	}

	const newlyHashedOtp = await hashOtp(plaintextOtp);
	return newlyHashedOtp === result[0].hashed_otp_code;
}

export async function deleteUserEmailVerificationAttempts(
	userId: string,
	email?: string
): Promise<void> {
	const conditions = [eq(email_verification_tokens.user_id, userId)];
	if (email) {
		conditions.push(eq(email_verification_tokens.email, email));
	}
	await db.delete(email_verification_tokens).where(and(...conditions));
}

export async function invalidateEmailVerificationAttempt(hashedUrlTokenId: string): Promise<void> {
	await db
		.delete(email_verification_tokens)
		.where(eq(email_verification_tokens.id, hashedUrlTokenId));
}

export async function sendVerificationEmail(
	email: string,
	urlToken: string,
	otp: string,
	origin: string
): Promise<void> {
	const verificationLink = `${origin}/verify-email?token=${urlToken}`; // Or a path to a page that uses the OTP

	// TODO: Consider if the OTP should be part of the link or entered on a page loaded by the link.
	// For simplicity now, the link implies token verification, and OTP might be for a secondary step if needed.
	// The current signup flow redirects to /verify-email?token=... which suggests the token in URL is primary.

	const subject = 'Verify your email address for Hairloom';
	const text = `Hello,\n\nPlease verify your email address for Hairloom.\nVerification Link: ${verificationLink}\nYour One-Time Password (OTP) is: ${otp}\n\nIf you did not request this, please ignore this email.\n\nThanks,\nThe Hairloom Team`;
	const html = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6;">
      <h2>Verify Your Email Address for Hairloom</h2>
      <p>Hello,</p>
      <p>Thank you for signing up! Please verify your email address.</p>
      <p><strong>Verification Link:</strong> <a href="${verificationLink}">${verificationLink}</a></p>
      <p><strong>Your One-Time Password (OTP): ${otp}</strong></p>
      <p>Click the link to proceed. You may be asked for the OTP.</p>
      <p>If you did not create an account with Hairloom, you can safely ignore this email.</p>
      <hr>
      <p>Thanks,<br>The Hairloom Team</p>
    </div>
  `;

	console.log(
		`Sending verification email to ${email} with link: ${verificationLink} and OTP: ${otp}`
	);
	await sendEmail({ to: email, subject, text, html });
}

// Cookie-based functions and old EmailVerificationRequest interface are removed.
