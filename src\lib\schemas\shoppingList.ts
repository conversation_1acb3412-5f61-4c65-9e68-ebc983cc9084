// ALPHA FEATURE: Shopping List Validation Schemas
// These schemas support the shopping list system that is currently in alpha status.
// They provide validation for adding items to shopping list, updating quantities,
// and tracking purchase completion status.

import { z } from 'zod/v4';

export const addShoppingListItemSchema = z.object({
	inventory_item_id: z.uuid(),
	quantity: z.coerce.number().int().min(1).default(1)
});

export const updateShoppingListItemSchema = z.object({
	id: z.uuid(),
	quantity: z.coerce.number().int().min(1)
});

export const togglePurchasedSchema = z.object({
	id: z.uuid(),
	purchased: z.coerce.boolean()
});

export type AddShoppingListItem = typeof addShoppingListItemSchema._input;
export type UpdateShoppingListItem = typeof updateShoppingListItemSchema._input;
export type TogglePurchased = typeof togglePurchasedSchema._input;
