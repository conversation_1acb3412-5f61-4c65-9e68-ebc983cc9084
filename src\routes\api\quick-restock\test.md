# Quick Restock API Test Cases

## Test Scenarios

### 1. Item with Reorder Threshold
- **Setup**: Create inventory item with barcode "TEST001" and reorder_threshold = 5
- **Expected**: Zero inventory, add 5 to shopping list
- **API Call**: `POST /api/quick-restock` with `{"barcode": "TEST001"}`

### 2. Item without Reorder Threshold
- **Setup**: Create inventory item with barcode "TEST002" and no reorder_threshold
- **Expected**: Return `needsQuantity: true` with item details
- **API Call**: `POST /api/quick-restock` with `{"barcode": "TEST002"}`

### 3. Item with Custom Quantity
- **Setup**: Use item from scenario 2
- **Expected**: Zero inventory, add custom quantity to shopping list
- **API Call**: `POST /api/quick-restock` with `{"barcode": "TEST002", "quantity": 3}`

### 4. Item Not Found
- **Setup**: Use non-existent barcode
- **Expected**: 404 error with appropriate message
- **API Call**: `POST /api/quick-restock` with `{"barcode": "NONEXISTENT"}`

### 5. Item Already in Shopping List
- **Setup**: Item with reorder threshold already in shopping list
- **Expected**: Zero inventory, add to existing shopping list quantity
- **API Call**: `POST /api/quick-restock` with existing item barcode

### 6. Empty Barcode
- **Setup**: Send empty or whitespace barcode
- **Expected**: 400 error with validation message
- **API Call**: `POST /api/quick-restock` with `{"barcode": ""}`

## Manual Testing Steps

1. **Dashboard Access**: Navigate to `/app` and verify Quick Restock button is visible
2. **Modal Opening**: Click Quick Restock button and verify modal opens
3. **Scanner Interface**: Test barcode scanner functionality (if camera available)
4. **Manual Input**: Test manual barcode entry
5. **Error Handling**: Test with invalid barcodes
6. **Success Flow**: Test complete workflow with valid items
7. **Quantity Prompt**: Test items without reorder thresholds
8. **Shopping List Integration**: Verify items are added to shopping list correctly

## Expected UI Behavior

- Modal opens when Quick Restock button is clicked
- Scanner starts when "Start Barcode Scanner" is clicked
- Manual input works when "Enter Barcode Manually" is clicked
- Success messages appear via toast notifications
- Error messages appear via toast notifications
- Modal closes after successful operation
- Shopping list is updated with new items
