<!--
This Svelte component is for verifying an email address specifically in the password reset flow.
It expects a token in the URL (managed by +page.server.ts) which is implicitly passed along with the form submission.
It displays the email address to which the verification code was sent (data.emailToVerify).
Users enter the 8-digit code to verify their email.
It uses Svelte Runes for reactivity and $props() to access form data and page data.
The `runes-form` library is used for progressive enhancement of the form.
Error messages or success messages (form?.message) from the server are displayed.
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { verifyCodeSchema } from '$lib/schemas/auth';
	import { toast } from '$lib/ui/toast';
	import type { ActionData, PageData } from './$types.js';

	let { data, form: actionForm }: { data: PageData; form?: ActionData } = $props();

	const verifyResetForm = createRuneForm(verifyCodeSchema, { code: '' });

	$effect(() => {
		if (actionForm?.message) {
			toast.error(actionForm.message);
		}
		if (actionForm?.errors?.code) {
			toast.error(actionForm.errors.code[0]);
		}
	});
</script>

<div
	class="hero min-h-screen bg-cover bg-center"
	style="background-image: url('/theme/auth/Background.webp');"
	data-testid="reset-password-verify-email-page"
>
	<div class="hero-content flex-col">
		<div class="text-center">
			<h1 class="text-5xl font-bold text-base-content" data-testid="reset-password-verify-email-title">
				Verify Your Email to Reset Password
			</h1>
			{#if data.emailToVerify}
				<p
					class="py-4 text-base-content/80"
					data-testid="reset-password-verification-instruction-message"
				>
					We sent an 8-digit code to <strong data-testid="reset-password-email-to-verify"
						>{data.emailToVerify}</strong
					>. Please check your inbox.
				</p>
			{:else}
				<p class="py-4 text-base-content/80" data-testid="reset-password-loading-email-message">
					Loading email information...
				</p>
				<span
					class="loading loading-dots loading-lg"
					data-testid="reset-password-loading-email-spinner"
				></span>
			{/if}
		</div>
		{#if data.emailToVerify}
			<div
				class="card glass bg-base-100/50 w-full max-w-sm shrink-0 shadow-xl backdrop-blur-sm"
				data-testid="reset-password-verify-email-card"
			>
				<RuneForm form={verifyResetForm}>
					{#snippet children(form)}
						<form
							class="card-body"
							method="post"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ update }) => {
									await update();
									form.setSubmitting(false);
								};
							}}
							onsubmit={form.handleSubmit()}
							data-testid="reset-password-verify-email-form"
						>
							<input
								type="hidden"
								name="token"
								value={data.token}
								data-testid="reset-password-verify-email-token-input"
							/>
							<Field name="code">
								{#snippet children(field)}
									<div class="form-control">
										<label class="label" for="form-verify.code">
											<span class="label-text">Verification Code</span>
										</label>
										<label
											class="input input-bordered flex items-center gap-2"
											data-testid="reset-password-verify-email-code-group"
										>
											<!-- hashtag icon -->
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="16"
												height="16"
												fill="currentColor"
												viewBox="0 0 24 24"
												class="opacity-70"
												><path
													d="M10 15h4v-2h-4v2zm-2 4h8v-2h-8v2zm0-6h8v-2h-8v2zm0-6h8V5h-8v2z"
												/></svg
											>
											<input
												id="form-verify.code"
												name="code"
												class="grow"
												required
												placeholder="8-digit code"
												data-testid="reset-password-verification-code-input"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											/>
										</label>
										<Errors name="code" />
									</div>
								{/snippet}
							</Field>
							<div class="form-control mt-6">
								<button
									class="btn btn-primary"
									data-testid="reset-password-verify-submit-button"
									disabled={form.isSubmitting}
								>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
									{/if}
									Verify and Continue</button
								>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			</div>
		{/if}
	</div>
</div>
