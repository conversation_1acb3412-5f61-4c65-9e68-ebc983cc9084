import { clientNoteAddSchema, clientUpsertSchema } from '$lib/schemas/client';
import { db } from '$lib/server/db';
import { client_notes, clients, templates } from '$lib/server/db/schema';
import { processImage } from '$lib/server/utils/imageProcessing';
import { error, fail, isRedirect, redirect } from '@sveltejs/kit';
import { and, eq, isNull, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params }) => {
	const user = locals.user;
	if (!user) {
		redirect(302, '/login');
	}

	const clientId = params.clientId;
	if (clientId === 'new') {
		return {
			client: null,
			clientNotes: [],
			availableTemplates: await db.query.templates.findMany({
				where: and(
					or(
						isNull(templates.user_id), // System templates
						eq(templates.user_id, user.id) // User's own templates
					),
					eq(templates.status, 'active') // Only active templates
				),
				orderBy: (templates, { asc }) => [asc(templates.name)]
			})
		};
	}
	const client = await db.query.clients.findFirst({
		where: and(eq(clients.id, clientId), eq(clients.user_id, user.id))
	});

	if (!client) {
		error(404, 'Client not found');
	}

	const notes = await db.query.client_notes.findMany({
		where: eq(client_notes.client_id, clientId),
		orderBy: (notes, { desc }) => [desc(notes.created_at)],
		with: {
			template: {
				columns: {
					name: true,
					template_definition: true
				}
			}
		}
	});

	const availableTemplates = await db.query.templates.findMany({
		where: and(
			or(
				isNull(templates.user_id), // System templates
				eq(templates.user_id, user.id) // User's own templates
			),
			eq(templates.status, 'active') // Only active templates
		),
		orderBy: (templates, { asc }) => [asc(templates.name)]
	});

	return {
		client,
		clientNotes: notes,
		availableTemplates
	};
};

export const actions: Actions = {
	createClient: async ({ request, locals }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { createClient: { errors: { root: ['Unauthorized'] } } });
		}
		const formData = Object.fromEntries(await request.formData());
		const validation = clientUpsertSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				createClient: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}

		let newClient;
		try {
			const result = await db
				.insert(clients)
				.values({
					...validation.data,
					user_id: user.id
				})
				.returning({ id: clients.id });
			newClient = result[0];
		} catch (e) {
			console.error('Failed to create client:', e);
			return fail(500, {
				createClient: {
					data: formData,
					errors: { root: ['Failed to create client. Please try again.'] }
				}
			});
		}

		if (newClient) {
			redirect(302, `/app/clients/${newClient.id}`);
		}

		return fail(500, {
			createClient: { data: formData, errors: { root: ['Failed to create client.'] } }
		});
	},
	updateClient: async ({ request, locals, params }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { updateClient: { errors: { root: ['Unauthorized'] } } });
		}

		const clientId = params.clientId;
		const formData = Object.fromEntries(await request.formData());
		const validation = clientUpsertSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				updateClient: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}

		try {
			const values = validation.data;

			// Process image if provided, or handle image removal
			let processedImageBase64: string | null = values.image_base64 || null;
			if (values.image_base64 && values.image_base64.trim() !== '') {
				try {
					processedImageBase64 = await processImage(values.image_base64, {
						maxWidth: 1600,
						maxHeight: 1200,
						quality: 80,
						format: 'jpeg'
					});
				} catch (imageError) {
					console.error('Image processing error:', imageError);
					return fail(400, {
						updateClient: {
							data: formData,
							errors: { image_base64: ['Failed to process image. Please try a different image.'] }
						}
					});
				}
			} else if (values.image_base64 === '') {
				// User explicitly removed the image
				processedImageBase64 = null;
			}

			await db
				.update(clients)
				.set({
					...values,
					image_base64: processedImageBase64,
					updated_at: new Date()
				})
				.where(and(eq(clients.id, clientId), eq(clients.user_id, user.id)));

			return { updateClient: { success: true } };
		} catch (e) {
			if (isRedirect(e)) throw e;
			console.error('Failed to update client:', e);
			return fail(500, {
				updateClient: {
					data: formData,
					errors: { root: ['Failed to update client. Please try again.'] }
				}
			});
		}
	},

	deleteClient: async ({ locals, params }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { deleteClient: { errors: { root: ['Unauthorized'] } } });
		}
		try {
			const [deleted] = await db
				.delete(clients)
				.where(and(eq(clients.id, params.clientId), eq(clients.user_id, user.id)))
				.returning({ id: clients.id });

			if (!deleted) {
				return fail(404, {
					deleteClient: {
						errors: { root: ['Client not found or you do not have permission to delete it.'] }
					}
				});
			}
		} catch (e) {
			if (isRedirect(e)) throw e;
			console.error('Failed to delete client:', e);
			return fail(500, {
				deleteClient: { errors: { root: ['Failed to delete client. Please try again.'] } }
			});
		}

		redirect(302, '/app/clients');
	},

	addNote: async ({ request, locals, params }) => {
		const user = locals.user;
		if (!user) {
			return fail(401, { addNote: { errors: { root: ['Unauthorized'] } } });
		}
		const formData = Object.fromEntries(await request.formData());
		const validation = clientNoteAddSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				addNote: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}

		try {
			const { templateId, note_data } = validation.data;

			await db.insert(client_notes).values({
				client_id: params.clientId,
				user_id: user.id,
				template_id: templateId,
				note_data: note_data
			});
			return { addNote: { success: true } };
		} catch (e) {
			if (isRedirect(e)) throw e;
			console.error('Failed to add note:', e);
			return fail(500, {
				addNote: {
					data: formData,
					errors: { root: ['Failed to add note. Please try again.'] }
				}
			});
		}
	}
};
