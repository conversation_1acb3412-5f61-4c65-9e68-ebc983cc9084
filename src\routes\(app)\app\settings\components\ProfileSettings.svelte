<!--
	@component
	Profile Settings Module - Handles user profile information and email updates
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { updateEmailSchema, updateProfileSchema } from '$lib/schemas/settings';
	import { toast } from '$lib/ui/toast';
	import IconUser from '~icons/icon-park-outline/user';
	import type { ActionData, PageData } from '../$types';
	import SettingsModule from './SettingsModule.svelte';

	interface Props {
		user: PageData['user'];
		actionData?: ActionData;
	}

	let { user, actionData }: Props = $props();

	const profileForm = $derived(
		createRuneForm(updateProfileSchema, {
			first_name: user?.first_name ?? ''
		})
	);
	const emailForm = createRuneForm(updateEmailSchema, { email: '', password: '' });

	// Handle action results
	$effect(() => {
		if (!actionData) return;

		// Profile Form
		if ('profile' in actionData && actionData.profile) {
			const profile = actionData.profile;
			if ('errors' in profile && profile.errors) {
				profileForm.setErrors(profile.errors);
				if ('root' in profile.errors && profile.errors.root) {
					toast.error(profile.errors.root[0]);
				} else {
					toast.error('Please correct the errors in your profile.');
				}
			} else if ('success' in profile && profile.success) {
				toast.success('Profile updated successfully.');
			}
		}

		// Email Form
		if ('email' in actionData && actionData.email) {
			const email = actionData.email;
			if ('errors' in email && email.errors) {
				emailForm.setErrors(email.errors);
				if ('root' in email.errors && email.errors.root) {
					toast.error(email.errors.root[0]);
				} else {
					toast.error('Please correct the email errors.');
				}
			}
		}
	});
</script>

<div class="space-y-8">
	<!-- Profile Information -->
	<SettingsModule
		title="Profile Information"
		description="Update your personal details"
		icon={IconUser}
	>
		{#snippet children()}
			<RuneForm form={profileForm}>
				{#snippet children(form)}
					<form
						action="?/updateProfile"
						method="POST"
						class="space-y-6"
						use:enhance={() => {
							form.setSubmitting(true);
							return async ({ update }) => {
								await update();
								form.setSubmitting(false);
							};
						}}
						onsubmit={form.handleSubmit()}
					>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<Field name="first_name">
								{#snippet children(field)}
									<div class="form-control">
										<label for="first_name" class="label">
											<span class="label-text">First Name</span>
										</label>
										<input
											type="text"
											id="first_name"
											name="first_name"
											class="input input-bordered transition-all duration-200 focus:border-primary/50 focus:ring-2 focus:ring-primary/10"
											placeholder="Enter your first name"
											value={String(field.value ?? '')}
											oninput={field.handleChange}
											onblur={field.handleBlur}
										/>
										<Errors name="first_name" />
									</div>
								{/snippet}
							</Field>
							<div class="form-control">
								<label for="email_static" class="label">
									<span class="label-text font-medium">Email Address</span>
								</label>
								<input
									type="email"
									id="email_static"
									class="input input-bordered input-lg bg-base-200/50"
									value={user?.email}
									disabled
								/>
								<div class="label">
									<span class="label-text-alt text-base-content/60">Email changes require verification</span>
								</div>
							</div>
						</div>
						<Errors name="root" />
						<div class="flex justify-end">
							<button
								type="submit"
								class="btn btn-primary transition-all duration-200 hover:shadow-md"
								disabled={form.isSubmitting}
							>
								{#if form.isSubmitting}
									<span class="loading loading-spinner loading-sm"></span>
									Saving...
								{:else}
									Save Changes
								{/if}
							</button>
						</div>
					</form>
				{/snippet}
			</RuneForm>
		{/snippet}
	</SettingsModule>

	<!-- Email Address -->
	<SettingsModule
		title="Email Address"
		description="Update your email address"
		icon={IconUser}
	>
		{#snippet children()}
			<div class="alert alert-info mb-6">
				<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<span>Changing your email will require verification and re-authentication.</span>
			</div>
			<RuneForm form={emailForm}>
				{#snippet children(form)}
					<form
						action="?/updateEmail"
						method="POST"
						class="space-y-4"
						use:enhance={() => {
							form.setSubmitting(true);
							return async ({ update }) => {
								await update();
								form.setSubmitting(false);
							};
						}}
						onsubmit={form.handleSubmit()}
					>
						<div class="join join-vertical w-full">
							<Field name="email">
								{#snippet children(field)}
									<div class="join-item">
										<label class="floating-label">
											<input
												type="email"
												id="email"
												name="email"
												class="input input-bordered w-full"
												placeholder="<EMAIL>"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											/>
											<span>New Email Address</span>
										</label>
										<Errors name="email" />
									</div>
								{/snippet}
							</Field>
							<Field name="password">
								{#snippet children(field)}
									<div class="join-item">
										<label class="floating-label">
											<input
												type="password"
												id="password_for_email"
												name="password"
												class="input input-bordered w-full"
												placeholder="••••••••"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											/>
											<span>Current Password</span>
										</label>
										<Errors name="password" />
									</div>
								{/snippet}
							</Field>
						</div>
						<Errors name="root" />
						<div class="flex justify-end">
							<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
								{#if form.isSubmitting}
									<span class="loading loading-spinner"></span>
									Updating...
								{:else}
									Update Email
								{/if}
							</button>
						</div>
					</form>
				{/snippet}
			</RuneForm>
		{/snippet}
	</SettingsModule>
</div>
