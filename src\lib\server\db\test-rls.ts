/**
 * Test script to verify <PERSON><PERSON> is working correctly
 * Run this with: tsx src/lib/server/db/test-rls.ts
 */

import { db } from './index';
import { inventory_items, users } from './schema';
import { sql } from 'drizzle-orm';

async function testRLS() {
	console.log('🔒 Testing Row Level Security (RLS) implementation...\n');

	try {
		// First, let's check if RLS is enabled on the inventory_items table
		const rlsStatus = await db.execute(sql`
			SELECT schemaname, tablename, rowsecurity 
			FROM pg_tables 
			WHERE tablename = 'inventory_items'
		`);
		
		console.log('📋 RLS Status for inventory_items:', rlsStatus.rows[0]);

		// Get a test user
		const testUsers = await db.select().from(users).limit(2);
		
		if (testUsers.length < 1) {
			console.log('❌ No users found in database. Please seed the database first.');
			return;
		}

		const user1 = testUsers[0];
		const user2 = testUsers[1] || testUsers[0]; // Use same user if only one exists

		console.log(`\n👤 Testing with User 1: ${user1.email} (${user1.id})`);
		console.log(`👤 Testing with User 2: ${user2.email} (${user2.id})\n`);

		// Test 1: Set context for user1 and query inventory
		console.log('🧪 Test 1: Setting RLS context for User 1...');
		await db.execute(sql`SELECT set_config('app.current_user_id', ${user1.id}, true)`);
		
		const user1Items = await db.select().from(inventory_items);
		console.log(`✅ User 1 can see ${user1Items.length} inventory items`);

		// Test 2: Set context for user2 and query inventory
		if (user2.id !== user1.id) {
			console.log('\n🧪 Test 2: Setting RLS context for User 2...');
			await db.execute(sql`SELECT set_config('app.current_user_id', ${user2.id}, true)`);
			
			const user2Items = await db.select().from(inventory_items);
			console.log(`✅ User 2 can see ${user2Items.length} inventory items`);

			// Test 3: Verify users can't see each other's data
			if (user1Items.length > 0 && user2Items.length > 0) {
				const user1ItemIds = user1Items.map(item => item.id);
				const user2ItemIds = user2Items.map(item => item.id);
				const overlap = user1ItemIds.filter(id => user2ItemIds.includes(id));
				
				if (overlap.length === 0) {
					console.log('✅ RLS is working: Users cannot see each other\'s inventory items');
				} else {
					console.log('❌ RLS may not be working: Users can see overlapping items');
				}
			}
		}

		// Test 4: Try querying without setting user context
		console.log('\n🧪 Test 3: Querying without RLS context...');
		await db.execute(sql`SELECT set_config('app.current_user_id', '', true)`);
		
		const noContextItems = await db.select().from(inventory_items);
		console.log(`📊 Without context: ${noContextItems.length} items visible`);
		
		if (noContextItems.length === 0) {
			console.log('✅ RLS is working: No items visible without user context');
		} else {
			console.log('⚠️  Warning: Items are visible without user context');
		}

		console.log('\n🎉 RLS testing completed!');

	} catch (error) {
		console.error('❌ Error testing RLS:', error);
	}
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	testRLS().then(() => process.exit(0));
}

export { testRLS };
