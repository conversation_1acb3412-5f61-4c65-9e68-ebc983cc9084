export type ConfirmationConfig = {
	title: string;
	message: string;
	confirmText?: string;
	cancelText?: string;
	confirmClass?: string;
};

function createConfirmationStore() {
	let isOpen = $state(false);
	let config = $state<ConfirmationConfig | null>(null);
	let resolvePromise: (value: boolean | PromiseLike<boolean>) => void = () => {};

	function confirm(newConfig: ConfirmationConfig): Promise<boolean> {
		config = newConfig;
		isOpen = true;
		return new Promise<boolean>((resolve) => {
			resolvePromise = resolve;
		});
	}

	function close(confirmed: boolean) {
		isOpen = false;
		resolvePromise(confirmed);
		// Reset config after a short delay to allow for closing animations
		setTimeout(() => {
			config = null;
		}, 300);
	}

	return {
		get isOpen() {
			return isOpen;
		},
		get config() {
			return config;
		},
		confirm,
		close
	};
}

export const confirmationStore = createConfirmationStore();
