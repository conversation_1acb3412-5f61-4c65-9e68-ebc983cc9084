<!--
  - src/lib/components/actions/QuickActionBar.svelte
  -
  - Purpose:
  - Provides an accessible, keyboard-first "command palette" for the application,
  - similar to features in tools like VS Code or Slack (Ctrl+K). It allows users to
  - quickly search for and execute common actions.
  -
  - Accessibility Best Practices Implemented:
  - - Follows WAI-ARIA combobox pattern with `role="combobox"`, `listbox`, and `option`.
  - - Uses `aria-activedescendant` for robust keyboard navigation without losing input focus.
  - - Manages focus trapping: saves the previously focused element on open and restores it on close.
  - - `Escape` key closes the modal.
  - - Fully navigable via keyboard (Up/Down arrows, Enter).
  - - The modal overlay has a `role="dialog"` and `aria-modal="true"`.
  -
  - UI/UX Enhancements:
  - - Smooth `fly` transitions for modal and list items.
  - - Staggered list item animation for a polished feel.
  - - Modern, blurred glassmorphism effect for the UI.
  - - Clear visual distinction for focused/selected items.
  - - Informative empty state and footer with keyboard hints.
  -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { TIMING } from '$lib/config';
	import { isQuickActionBarOpen } from '$lib/stores/quickActionBar';
	import { quintOut } from 'svelte/easing';
	import { fly } from 'svelte/transition';
	import IconAdd from '~icons/icon-park-outline/add-one';
	import IconLogout from '~icons/icon-park-outline/logout';
	import IconTemplate from '~icons/icon-park-outline/page-template';
	import IconSearch from '~icons/icon-park-outline/search';
	import IconSettings from '~icons/icon-park-outline/setting-one';
	import IconClient from '~icons/icon-park-outline/user';

	type Action = {
		group: 'Navigation' | 'Create' | 'General' | 'Clients';
		label: string;
		href?: string;
		form?: {
			action: string;
			method: 'POST' | 'GET';
		};
		icon: any;
		keywords?: string;
		sublabel?: string;
	};

	type ClientResult = {
		id: string;
		first_name: string;
		last_name: string;
		email: string | null;
	};

	let searchTerm = $state('');
	let selectedIndex = $state(0);
	let inputElement = $state<HTMLInputElement | undefined>();
	let actionElements = $state<Array<HTMLLIElement | null>>([]);
	let lastActiveElement: HTMLElement | null = null;
	let clientResults = $state<ClientResult[]>([]);
	let debounceTimer: any;

	const toId = (label: string, group: string) => `qab-${group}-${label}`.replace(/\s+/g, '-');

	/**
	 * Executes the selected action.
	 * For POST actions, it creates and submits a hidden form. This is a robust
	 * way to trigger SvelteKit form actions from a global component without
	 * complex client-side fetch and state management.
	 */
	async function executeAction(action: Action) {
		close();
		if (action.href) {
			await goto(action.href);
		} else if (action.form) {
			const form = document.createElement('form');
			form.method = action.form.method;
			form.action = action.form.action;
			form.style.display = 'none';
			document.body.appendChild(form);
			form.submit();
			document.body.removeChild(form);
		}
	}

	const actions: Action[] = [
		// Navigation
		{
			group: 'Navigation',
			label: 'Clients',
			href: '/app/clients',
			icon: IconClient,
			keywords: 'customers users people'
		},

		{
			group: 'Navigation',
			label: 'Templates',
			href: '/app/templates',
			icon: IconTemplate,
			keywords: 'notes forms'
		},
		{
			group: 'Navigation',
			label: 'Settings',
			href: '/app/settings',
			icon: IconSettings,
			keywords: 'options configuration account'
		},
		// Create
		{
			group: 'Create',
			label: 'New Template',
			form: {
				action: '/app/templates?/createTemplate',
				method: 'POST'
			},
			icon: IconAdd,
			keywords: 'create form note'
		},

		// General
		{
			group: 'General',
			label: 'Logout',
			form: {
				action: '/logout',
				method: 'POST'
			},
			icon: IconLogout,
			keywords: 'sign out exit leave'
		}
	];

	const filteredStaticActions = $derived(
		actions.filter((action) => {
			const terms = `${action.label} ${action.keywords ?? ''}`.toLowerCase();
			return terms.includes(searchTerm.toLowerCase());
		})
	);

	const clientActions = $derived(
		clientResults.map((client) => ({
			group: 'Clients',
			label: `${client.first_name} ${client.last_name}`,
			href: `/app/clients/${client.id}`,
			icon: IconClient,
			keywords: client.email ?? '',
			sublabel: client.email ?? undefined
		})) as Action[]
	);

	const combinedActions = $derived.by(() => {
		if (!searchTerm) return filteredStaticActions;
		return [...clientActions, ...filteredStaticActions];
	});

	function close() {
		isQuickActionBarOpen.set(false);
	}

	function handleOverlayClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			close();
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			close();
			return;
		}

		if (combinedActions.length === 0) {
			return;
		}

		if (event.key === 'ArrowDown') {
			event.preventDefault();
			selectedIndex = (selectedIndex + 1) % combinedActions.length;
		} else if (event.key === 'ArrowUp') {
			event.preventDefault();
			selectedIndex = (selectedIndex - 1 + combinedActions.length) % combinedActions.length;
		} else if (event.key === 'Enter') {
			event.preventDefault();
			const selectedAction = combinedActions[selectedIndex];
			if (selectedAction) {
				void executeAction(selectedAction);
			}
		}
	}

	$effect(() => {
		// Reset state and manage focus when modal opens/closes
		if ($isQuickActionBarOpen) {
			lastActiveElement = document.activeElement as HTMLElement;
			searchTerm = '';
			selectedIndex = 0;
			actionElements = [];
			setTimeout(() => inputElement?.focus(), 50); // Delay focus until transition starts
		} else {
			lastActiveElement?.focus();
			lastActiveElement = null;
		}
	});

	$effect(() => {
		clearTimeout(debounceTimer);
		if (searchTerm.trim().length < 2) {
			clientResults = [];
			return;
		}

		debounceTimer = setTimeout(async () => {
			try {
				const response = await fetch(`/api/clients/search?q=${encodeURIComponent(searchTerm)}`);
				if (response.ok) {
					const data = await response.json();
					clientResults = data;
				} else {
					clientResults = [];
				}
			} catch {
				clientResults = [];
			}
		}, TIMING.SEARCH_DEBOUNCE);
	});

	$effect(() => {
		// Scroll the active item into view
		if (actionElements[selectedIndex]) {
			actionElements[selectedIndex]?.scrollIntoView({
				block: 'nearest'
			});
		}
	});

	const activeDescendant = $derived(
		combinedActions.length > 0 && selectedIndex < combinedActions.length
			? toId(combinedActions[selectedIndex].label, combinedActions[selectedIndex].group)
			: undefined
	);

	// Reset index if search term changes and selection is out of bounds
	$effect(() => {
		if (searchTerm && selectedIndex >= combinedActions.length) {
			selectedIndex = Math.max(0, combinedActions.length - 1);
		}
	});
</script>

{#if $isQuickActionBarOpen}
	<div
		class="modal modal-open sm:modal-middle modal-bottom backdrop-blur-md"
		role="dialog"
		aria-modal="true"
		aria-labelledby="qab-title"
		tabindex="-1"
		onclick={handleOverlayClick}
		onkeydown={handleKeydown}
	>
		<div
			class="modal-box bg-base-200/50 border-base-content/10 w-full max-w-xl border p-0 shadow-2xl backdrop-blur-xl"
			in:fly={{ duration: 300, y: 50, easing: quintOut }}
		>
			<div
				class="relative"
				role="combobox"
				aria-haspopup="listbox"
				aria-expanded="true"
				aria-owns="qab-listbox"
				aria-controls="qab-listbox"
			>
				<h2 id="qab-title" class="sr-only">Quick Actions</h2>
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
					<IconSearch class="text-base-content/60 h-6 w-6" />
				</div>
				<input
					bind:this={inputElement}
					type="text"
					placeholder="What do you need?"
					class="input input-ghost w-full pl-12 text-base focus:bg-transparent sm:text-lg"
					bind:value={searchTerm}
					aria-autocomplete="list"
					aria-controls="qab-listbox"
					aria-activedescendant={activeDescendant}
				/>
			</div>

			<div class="divider bg-base-content/10 m-0 h-px"></div>

			<div class="p-2">
				{#if combinedActions.length > 0}
					<ul
						id="qab-listbox"
						class="max-h-[60vh] space-y-1 overflow-y-auto p-1 md:max-h-[40vh]"
						role="listbox"
						aria-label="Actions"
					>
						{#each combinedActions as action, i (action.label + action.group)}
							{@const Icon = action.icon}
							{#if i === 0 || action.group !== combinedActions[i - 1].group}
								<li class="menu-title px-2 py-1 text-xs opacity-80" aria-hidden="true">
									<span>{action.group}</span>
								</li>
							{/if}
							<li
								bind:this={actionElements[i]}
								role="option"
								aria-selected={selectedIndex === i}
								id={toId(action.label, action.group)}
								class="flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors duration-150"
								class:bg-primary={selectedIndex === i}
								class:text-primary-content={selectedIndex === i}
								onmousemove={() => (selectedIndex = i)}
								onmousedown={(e) => {
									e.preventDefault(); // Prevent input from losing focus
									executeAction(action);
								}}
								tabindex="-1"
							>
								<Icon class="h-5 w-5" />
								<div class="flex flex-col">
									<span class="font-medium">{action.label}</span>
									{#if action.sublabel}
										<span
											class="text-xs {selectedIndex === i
												? 'text-primary-content/70'
												: 'text-base-content/70'}">{action.sublabel}</span
										>
									{/if}
								</div>
							</li>
						{/each}
					</ul>
				{:else}
					<div class="flex flex-col items-center justify-center gap-4 p-8 text-center">
						<IconSearch class="text-base-content/20 h-16 w-16" />
						<div class="flex flex-col">
							<p class="font-bold">No actions found</p>
							<p class="text-base-content/60 text-sm">
								Try searching for "clients", "new template", etc.
							</p>
						</div>
					</div>
				{/if}
			</div>

			<div class="hidden sm:block">
				<div class="divider bg-base-content/10 m-0 h-px"></div>
				<div class="bg-base-300/20 text-base-content/70 px-4 py-2 text-xs">
					<div class="flex justify-center gap-4">
						<span class="inline-flex items-center gap-2">
							<kbd class="kbd kbd-sm">↑</kbd>
							<kbd class="kbd kbd-sm">↓</kbd>
							to navigate
						</span>
						<span class="divider divider-horizontal bg-base-content/10 m-0 w-px"></span>
						<span class="inline-flex items-center gap-2">
							<kbd class="kbd kbd-sm">↵</kbd>
							to select
						</span>
						<span class="divider divider-horizontal bg-base-content/10 m-0 w-px"></span>
						<span class="inline-flex items-center gap-2">
							<kbd class="kbd kbd-sm">esc</kbd>
							to close
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
