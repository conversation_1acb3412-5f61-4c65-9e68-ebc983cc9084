import { db } from '$lib/server/db';
import { client_notes, clients, templates } from '$lib/server/db/schema';
import { subDays } from 'date-fns';
import { and, count, eq, gte, isNull, or } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

// console.log('TEST LOG: Server is running and logging works!');

export const load: PageServerLoad = ({ locals }) => {
	const { user } = locals;
	if (!user) {
		return;
	}

	const thirtyDaysAgo = subDays(new Date(), 30);

	const statsPromise = (async () => {
		const [
			clientCountResult,
			noteCountResult,
			newClientsLast30DaysResult,
			newNotesLast30DaysResult
		] = await Promise.all([
			db.select({ value: count() }).from(clients).where(eq(clients.user_id, user.id)),
			db.select({ value: count() }).from(client_notes).where(eq(client_notes.user_id, user.id)),
			db
				.select({ value: count() })
				.from(clients)
				.where(and(eq(clients.user_id, user.id), gte(clients.created_at, thirtyDaysAgo))),
			db
				.select({ value: count() })
				.from(client_notes)
				.where(and(eq(client_notes.user_id, user.id), gte(client_notes.created_at, thirtyDaysAgo)))
		]);

		return {
			totalClients: clientCountResult[0].value,
			totalNotes: noteCountResult[0].value,
			newClientsLast30Days: newClientsLast30DaysResult[0].value,
			newNotesLast30Days: newNotesLast30DaysResult[0].value
		};
	})();

	const recentClientsPromise = db.query.clients.findMany({
		where: eq(clients.user_id, user.id),
		orderBy: (clients, { desc }) => [desc(clients.updated_at)],
		limit: 5
	});

	const recentTemplatesPromise = db.query.templates.findMany({
		where: or(eq(templates.user_id, user.id), isNull(templates.user_id)),
		orderBy: (templates, { desc }) => [desc(templates.created_at)],
		limit: 5
	});

	return {
		stats: statsPromise,
		recentClients: recentClientsPromise,
		recentTemplates: recentTemplatesPromise
	};
};
