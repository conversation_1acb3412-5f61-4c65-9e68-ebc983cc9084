<script lang="ts">
	/**
	 * @component ClientsPage
	 * @description This page displays a list of all clients in a sortable and searchable table.
	 * It allows for adding new clients via a modal form. The table provides quick access
	 * to view individual client details.
	 */
	import { enhance } from '$app/forms';
	import ImageUpload from '$lib/components/ui/ImageUpload.svelte';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { clientUpsertSchema } from '$lib/schemas/client';
	import { toast } from '$lib/ui/toast';
	import IconAdd from '~icons/icon-park-outline/add-one';
	import IconArrowDown from '~icons/icon-park-outline/arrow-down';
	import IconArrowUp from '~icons/icon-park-outline/arrow-up';
	import IconSearch from '~icons/icon-park-outline/search';
	import IconUser from '~icons/icon-park-outline/user';
	import type { ActionData, PageData } from './$types';

	let { data, form: actionForm }: { data: PageData; form?: ActionData } = $props();

	type Client = PageData['clients'][number];

	let isModalOpen = $state(false);
	let searchTerm = $state('');
	let sortKey = $state<'first_name' | 'last_name' | 'email'>('last_name');
	let sortDirection = $state<'asc' | 'desc'>('asc');

	const addClientForm = createRuneForm(clientUpsertSchema, {
		first_name: '',
		last_name: '',
		email: '',
		phone_number: '',
		image_base64: ''
	});

	function handleImageChange(base64Image: string) {
		addClientForm.values.image_base64 = base64Image;
	}

	function getSortedAndFilteredClients(
		clients: Client[],
		term: string,
		key: 'first_name' | 'last_name' | 'email',
		direction: 'asc' | 'desc'
	): Client[] {
		let filteredClients = [...clients];

		// Filter
		if (term) {
			const lowerCaseSearch = term.toLowerCase();
			filteredClients = filteredClients.filter(
				(c) =>
					c.first_name?.toLowerCase().includes(lowerCaseSearch) ||
					c.last_name?.toLowerCase().includes(lowerCaseSearch) ||
					c.email?.toLowerCase().includes(lowerCaseSearch)
			);
		}

		// Sort
		const sortedClients = filteredClients.sort((a, b) => {
			const aValue = a[key] ?? '';
			const bValue = b[key] ?? '';
			if (aValue < bValue) return direction === 'asc' ? -1 : 1;
			if (aValue > bValue) return direction === 'asc' ? 1 : -1;
			return 0;
		});

		return sortedClients;
	}

	const sortedAndFilteredClients = $derived(
		getSortedAndFilteredClients(data.clients, searchTerm, sortKey, sortDirection)
	);

	function handleSort(key: typeof sortKey) {
		if (sortKey === key) {
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			sortKey = key;
			sortDirection = 'asc';
		}
	}

	$effect(() => {
		if (actionForm?.success) {
			toast.success('Client added successfully!');
			addClientForm.reset();
			isModalOpen = false;
		}
		if (actionForm?.errors) {
			addClientForm.setErrors(actionForm.errors);
			isModalOpen = true;
			if ('root' in actionForm.errors && actionForm.errors.root) {
				toast.error(actionForm.errors.root[0]);
			} else {
				const errorCount = Object.keys(actionForm.errors).length;
				if (errorCount > 0) {
					toast.error(`Please correct the ${errorCount} error(s) to continue.`);
				}
			}
		}
	});

	const sortableClass = 'cursor-pointer select-none hover:bg-base-content/10';
</script>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex flex-wrap items-center justify-between gap-4">
		<h1 class="text-3xl font-bold">Clients</h1>
		<div class="flex items-center gap-4">
			<div class="form-control">
				<label class="input input-bordered flex items-center gap-2">
					<IconSearch class="h-5 w-5 opacity-50" />
					<input type="text" class="grow" placeholder="Search" bind:value={searchTerm} />
				</label>
			</div>
			<button class="btn btn-primary" onclick={() => (isModalOpen = true)}>
				<IconAdd class="h-5 w-5" />
				Add Client
			</button>
		</div>
	</div>

	<!-- Clients Table -->
	<div class="card bg-base-200/70 shadow-lg">
		<div class="card-body p-0">
			<div class="overflow-x-auto">
				<table class="table-zebra table">
					<thead>
						<tr>
							<th onclick={() => handleSort('first_name')} class={sortableClass}>
								<div class="flex items-center gap-2">
									First Name
									{#if sortKey === 'first_name'}
										{#if sortDirection === 'asc'}
											<IconArrowUp class="h-4 w-4" />
										{:else}
											<IconArrowDown class="h-4 w-4" />
										{/if}
									{/if}
								</div>
							</th>
							<th onclick={() => handleSort('last_name')} class={sortableClass}>
								<div class="flex items-center gap-2">
									Last Name
									{#if sortKey === 'last_name'}
										{#if sortDirection === 'asc'}
											<IconArrowUp class="h-4 w-4" />
										{:else}
											<IconArrowDown class="h-4 w-4" />
										{/if}
									{/if}
								</div>
							</th>
							<th onclick={() => handleSort('email')} class={sortableClass}>
								<div class="flex items-center gap-2">
									Email
									{#if sortKey === 'email'}
										{#if sortDirection === 'asc'}
											<IconArrowUp class="h-4 w-4" />
										{:else}
											<IconArrowDown class="h-4 w-4" />
										{/if}
									{/if}
								</div>
							</th>
							<th>Phone Number</th>
							<th></th>
						</tr>
					</thead>
					<tbody>
						{#each sortedAndFilteredClients as client (client.id)}
							<tr class="hover">
								<td>
									<div class="flex items-center gap-3">
										<!-- Client Avatar -->
										{#if client.image_base64}
											<div class="avatar">
												<div class="w-10 h-10 rounded-full">
													<img
														src={client.image_base64}
														alt="{client.first_name} {client.last_name}"
														class="w-full h-full object-cover rounded-full"
													/>
												</div>
											</div>
										{:else}
											<div class="w-10 h-10 bg-primary/10 text-primary flex items-center justify-center rounded-full text-sm font-bold border-2 border-primary/20">
												{client.first_name?.[0]}{client.last_name?.[0]}
											</div>
										{/if}
										<div class="flex flex-col">
											<span class="font-medium">{client.first_name}</span>
											<span class="status status-success status-sm" title="Active Client"></span>
										</div>
									</div>
								</td>
								<td>{client.last_name}</td>
								<td>{client.email}</td>
								<td>{client.phone_number}</td>
								<td class="text-right">
									<a href="/app/clients/{client.id}" class="btn btn-ghost btn-sm">View</a>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
				{#if sortedAndFilteredClients.length === 0}
					<div class="flex flex-col items-center justify-center gap-4 p-8 text-center">
						<IconUser class="text-base-content/20 h-16 w-16" />
						<div class="flex flex-col">
							<p class="font-bold">No clients found</p>
							<p class="text-base-content/60 text-sm">
								{#if searchTerm}
									Try a different search term.
								{:else}
									Click "Add Client" to get started.
								{/if}
							</p>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>

	<!-- Add Client Modal -->
	{#if isModalOpen}
		<div class="modal modal-open" role="dialog">
			<div class="modal-box">
				<button
					class="btn btn-sm btn-circle btn-ghost absolute top-2 right-2"
					onclick={() => (isModalOpen = false)}>✕</button
				>
				<h3 class="text-2xl font-bold">Add New Client</h3>
				<p class="text-base-content/80 py-4">Enter the details for the new client below.</p>
				<RuneForm form={addClientForm}>
					{#snippet children(form)}
						<form
							method="POST"
							action="?/createClient"
							class="space-y-6 pt-4"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ result, update }) => {
									if (result.type !== 'redirect' && result.type !== 'error') {
										await update();
									}
									form.setSubmitting(false);
								};
							}}
							onsubmit={form.handleSubmit()}
						>
							<fieldset class="fieldset">
								<legend class="fieldset-legend">Personal Information</legend>
								<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
									<Field name="first_name">
										{#snippet children(field)}
											<div class="form-control">
												<label class="input input-bordered">
													<span class="label">First Name</span>
													<input
														id="first_name"
														name="first_name"
														type="text"
														value={String(field.value ?? '')}
														oninput={field.handleChange}
														onblur={field.handleBlur}
													/>
												</label>
												<Errors name="first_name" />
											</div>
										{/snippet}
									</Field>
									<Field name="last_name">
										{#snippet children(field)}
											<div class="form-control">
												<label class="input input-bordered">
													<span class="label">Last Name</span>
													<input
														id="last_name"
														name="last_name"
														type="text"
														value={String(field.value ?? '')}
														oninput={field.handleChange}
														onblur={field.handleBlur}
													/>
												</label>
												<Errors name="last_name" />
											</div>
										{/snippet}
									</Field>
								</div>
								<p class="label">Enter the client's full name as it should appear in your records.</p>
							</fieldset>

							<fieldset class="fieldset">
								<legend class="fieldset-legend">Contact Information</legend>
								<div class="space-y-4">
									<Field name="email">
										{#snippet children(field)}
											<div class="form-control">
												<label class="input input-bordered">
													<span class="label">Email</span>
													<input
														id="email"
														name="email"
														type="email"
														value={String(field.value ?? '')}
														oninput={field.handleChange}
														onblur={field.handleBlur}
													/>
												</label>
												<Errors name="email" />
											</div>
										{/snippet}
									</Field>

									<Field name="phone_number">
										{#snippet children(field)}
											<div class="form-control">
												<label class="input input-bordered">
													<span class="label">Phone Number</span>
													<input
														id="phone_number"
														name="phone_number"
														type="tel"
														value={String(field.value ?? '')}
														oninput={field.handleChange}
														onblur={field.handleBlur}
													/>
												</label>
												<Errors name="phone_number" />
											</div>
										{/snippet}
									</Field>
								</div>
								<p class="label">Provide at least one method of contact for the client.</p>
							</fieldset>

							<fieldset class="fieldset">
								<legend class="fieldset-legend">Client Photo</legend>
								<div class="bg-base-100/80 border border-base-300/30 rounded-xl p-6 shadow-sm">
									<ImageUpload
										value={addClientForm.values.image_base64}
										onImageChange={handleImageChange}
										maxWidth={1600}
										maxHeight={1200}
										quality={0.8}
										title="Add Client Photo"
										subtitle="Upload a file or take a photo"
									/>
								</div>
								<p class="label">Upload a photo to help identify the client.</p>
							</fieldset>

							<Errors name="root" />

							<!-- Hidden input for image data -->
							<input type="hidden" name="image_base64" bind:value={addClientForm.values.image_base64} />

							<div class="modal-action">
								<button type="button" class="btn" onclick={() => (isModalOpen = false)}>
									Cancel
								</button>
								<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
										Saving...
									{:else}
										Save Client
									{/if}
								</button>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			</div>
			<div
				class="modal-backdrop"
				onclick={() => (isModalOpen = false)}
				onkeydown={(e) => e.key === 'Escape' && (isModalOpen = false)}
				role="button"
				tabindex="0"
				aria-label="close modal"
			></div>
		</div>
	{/if}
</div>
