<!--
  ALPHA FEATURE: Inventory Management - Item Form Component
  Modern, playful form component for creating or editing inventory items.

  Features:
  - Beautiful DaisyUI v5 styling with custom microinteractions
  - Smooth animations and hover effects
  - Colorful accents and playful design elements
  - Responsive layout with improved UX
  - Enhanced accessibility and form validation
  - Integrated barcode scanning via BarcodeInput component
  - Image upload with preview and backend processing/resizing
  - Currency input handling for costs and prices (distinguishes purchase cost vs selling price)
  - Reorder threshold management for automatic shopping list suggestions
  - Supplier information tracking for restocking workflows

  Props:
    - form      : RunesForm instance (from createRuneForm)
    - action    : form action string (e.g. "?/create" or "?/update")
    - submitLabel: button text (default "Save")
    - onSuccess : callback to run after local client-side validation passes
    - isNewItem : boolean to determine if this is a new item form

  This component is part of the inventory management system that is currently in alpha status.
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import { currencyInput } from '$lib/actions/currencyInput';
	import BarcodeInput from '$lib/components/barcode/BarcodeInput.svelte';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import type { RunesForm } from '$lib/runes-form/index.svelte';
	import { toast } from '$lib/ui/toast';
	import type { ZodType } from 'zod/v4';
	import ImageUpload from '../ui/ImageUpload.svelte';

	type F = $$Generic<ZodType>;

	let {
		form,
		action = '',
		submitLabel = 'Save',
		onSuccess = () => {},
		isNewItem = false
	}: {
		form: RunesForm<F>;
		action?: string;
		submitLabel?: string;
		onSuccess?: (arg: any) => void;
		isNewItem?: boolean;
	} = $props();

	function handleCurrencyChange(event: Event, fieldName: string) {
		if (event instanceof CustomEvent) {
			(form.values as any)[fieldName] = event.detail.valueInCents;
		}
	}

	function handleImageChange(base64Image: string) {
		(form.values as any).image_base64 = base64Image;
	}

	async function handleBarcodeChange(event: Event | string) {
		let barcode: string;
		let isFromScanner = false;

		if (typeof event === 'string') {
			// Direct barcode value from scanner
			barcode = event.trim();
			isFromScanner = true;
			console.log('Barcode scanned:', barcode);
		} else {
			// Event from input field
			const input = event.target as HTMLInputElement;
			barcode = input.value.trim();
			console.log('Barcode entered manually:', barcode);
		}

		if (!barcode) return;

		// Skip processing if this is a manual input event triggered by the scanner
		// (the scanner already processed this barcode)
		if (!isFromScanner && (form.values as any).barcode === barcode) {
			console.log('Skipping duplicate barcode processing');
			return;
		}

		try {
			// Search for existing item with this barcode
			const response = await fetch(`/api/inventory/search?barcode=${encodeURIComponent(barcode)}`);

			if (response.ok) {
				const data = await response.json();

				if (data.items && data.items.length > 0) {
					const existingItem = data.items[0];

					if (isNewItem) {
						// If we're adding a new item and the barcode already exists,
						// navigate to the existing item's edit page
						toast.info(`Barcode already exists for "${existingItem.name}". Taking you to the existing item.`);
						await goto(`/app/inventory/${existingItem.id}`);
						return;
					} else {
						// If we're editing an existing item, auto-fill form with existing item data
						const values = form.values as any;
						if (!values.name) {
							values.name = existingItem.name;
						}
						if (!values.description) {
							values.description = existingItem.description || '';
						}
						if (!values.sku) {
							values.sku = existingItem.sku || '';
						}
						if (!values.price_in_cents) {
							values.price_in_cents = existingItem.price_in_cents;
						}
						if (!values.cost_in_cents) {
							values.cost_in_cents = existingItem.cost_in_cents;
						}
						if (!values.reorder_threshold) {
							values.reorder_threshold = existingItem.reorder_threshold;
						}
						if (!values.supplier) {
							values.supplier = existingItem.supplier || '';
						}
						if (!values.image_base64) {
							values.image_base64 = existingItem.image_base64 || '';
						}
					}
				} else if (isNewItem) {
					// Barcode doesn't exist, show success message for new item
					toast.success('Barcode scanned successfully! You can now enter the product details.');
				}
			}
		} catch (error) {
			console.error('Error looking up barcode:', error);
			toast.error('Failed to lookup barcode. Please try again or enter the information manually.');
		}
	}
</script>

<!-- Main Form Container -->
<div class="bg-base-100/80 backdrop-blur-sm border border-base-300/30 rounded-xl shadow-sm">
	<div class="p-8">
		<!-- Form Header -->
		<div class="mb-8">
			<div class="flex items-center gap-3 mb-2">
				<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
				<h2 class="text-2xl font-semibold text-base-content/90">
					{isNewItem ? 'Add New Item' : 'Edit Item'}
				</h2>
			</div>
			<p class="text-base-content/60 ml-5">
				{isNewItem ? 'Create a new inventory item with all the details' : 'Update your item details and keep your inventory accurate'}
			</p>
		</div>

		<RuneForm {form}>
			{#snippet children(_formContext)}
				<form
					{action}
					method="POST"
					class="space-y-8"
					use:enhance={() => {
						form.setSubmitting(true);
						return async ({ update, result }) => {
							await update({ reset: false });
							form.setSubmitting(false);

							if (result.type === 'success') {
								onSuccess(result);
							} else if (result.type === 'failure') {
								// Handle form validation errors
								const data = result.data as any;
								if (data?.create?.errors?.root) {
									toast.error(data.create.errors.root[0]);
								} else if (data?.update?.errors?.root) {
									toast.error(data.update.errors.root[0]);
								} else {
									toast.error('Failed to save item. Please check the form and try again.');
								}
							} else if (result.type === 'redirect') {
								onSuccess(result);
							}
						};
					}}
					onsubmit={form.handleSubmit()}
				>
					<!-- Mobile-Optimized Grid Layout -->
					<div class="grid grid-cols-1 gap-6 lg:gap-10 lg:grid-cols-3 transition-all duration-500">
						<!-- Image Upload Section - Mobile First -->
						<div class="lg:col-span-1 space-y-4 lg:space-y-6">
							<div class="flex items-center gap-3 mb-3 lg:mb-4">
								<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
								<div>
									<h3 class="text-base lg:text-lg font-semibold text-base-content/90">Product Image</h3>
									<p class="text-xs lg:text-sm text-base-content/60">Upload a photo of your item</p>
								</div>
							</div>
							<div class="bg-base-100/80 border border-base-300/30 rounded-xl p-4 lg:p-6 shadow-sm">
								<ImageUpload
									value={(form.values as any).image_base64}
									onImageChange={handleImageChange}
									maxWidth={1600}
									maxHeight={1200}
									quality={0.8}
									title="Add Product Image"
									subtitle="Upload a file or take a photo"
								/>
							</div>
						</div>

						<!-- Form Fields Section - Mobile Optimized -->
						<div class="lg:col-span-2 space-y-6 lg:space-y-8">
							<!-- Product Name Field -->
							<Field name="name">
								{#snippet children(field)}
									<div class="form-control">
										<label class="label" for="name-input">
											<span class="label-text font-medium text-base-content/90 text-sm lg:text-base">
												Product Name
												<span class="text-error/70 ml-1">*</span>
											</span>
										</label>
										<input
											id="name-input"
											type="text"
											name="name"
											class="input input-bordered input-lg lg:input-md transition-all duration-200 focus:border-primary/50 focus:ring-2 focus:ring-primary/10 text-base"
											placeholder="Enter a descriptive name for your product..."
											bind:value={(form.values as any).name}
											oninput={field.handleChange}
											onblur={field.handleBlur}
											required
										/>
										<Errors name="name" />
									</div>
								{/snippet}
							</Field>

							<!-- Product Description Field - Mobile Optimized -->
							<Field name="description">
								{#snippet children(field)}
									<div class="form-control group">
										<label class="label" for="description-input">
											<span class="label-text font-semibold text-base-content/90 flex items-center gap-2 text-sm lg:text-base">
												📝 Description
												<span class="badge badge-ghost badge-xs">Optional</span>
											</span>
										</label>
										<div class="relative">
											<textarea
												id="description-input"
												name="description"
												class="textarea textarea-bordered textarea-lg w-full h-24 lg:h-32 transition-all duration-300 focus:textarea-primary lg:focus:scale-[1.02] group-hover:border-primary/50 resize-none text-base"
												placeholder="Tell us more about this product... What makes it special?"
												bind:value={(form.values as any).description}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											></textarea>
											<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
										</div>
										<Errors name="description" />
									</div>
								{/snippet}
							</Field>

							<!-- Quantity Field - Mobile Optimized -->
							<Field name="quantity">
								{#snippet children(field)}
									<div class="form-control group">
										<label class="label" for="quantity-input">
											<span class="label-text font-semibold text-base-content/90 flex items-center gap-2 text-sm lg:text-base">
												📦 Current Quantity
												<span class="badge badge-error badge-xs">Required</span>
											</span>
										</label>
										<div class="relative">
											<input
												id="quantity-input"
												type="number"
												inputmode="numeric"
												name="quantity"
												class="input input-bordered input-lg lg:input-md w-full transition-all duration-300 focus:input-primary lg:focus:scale-[1.02] group-hover:border-primary/50 text-base"
												placeholder="How many do you have in stock?"
												min="0"
												step="1"
												bind:value={(form.values as any).quantity}
												oninput={field.handleChange}
												onblur={field.handleBlur}
												required
											/>
											<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
										</div>
										<Errors name="quantity" />
									</div>
								{/snippet}
							</Field>

							<!-- Barcode Field with Enhanced Styling -->
							<Field name="barcode">
								{#snippet children(field)}
									<div class="form-control group">
										<label class="label" for="barcode-input">
											<span class="label-text font-semibold text-base-content/90 flex items-center gap-2">
												📊 Barcode
												<span class="badge badge-info badge-xs">Scan or Type</span>
											</span>
										</label>
										<div class="relative">
											<div class="transition-all duration-300 group-hover:scale-[1.01]">
												<BarcodeInput
													id="barcode-input"
													name="barcode"
													bind:value={(form.values as any).barcode}
													onInput={field.handleChange}
													onBlur={(e) => {
														field.handleBlur();
														handleBarcodeChange(e);
													}}
													onScan={(barcode) => {
														(form.values as any).barcode = barcode;
														handleBarcodeChange(barcode);
													}}
													placeholder="Scan with camera or type barcode manually..."
												/>
											</div>
											<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-info/5 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
										</div>
										<Errors name="barcode" />
									</div>
								{/snippet}
							</Field>

							<!-- Pricing Section - Mobile Optimized -->
							<div class="grid grid-cols-1 gap-6 lg:gap-8 sm:grid-cols-2">
								<!-- Selling Price Field -->
								<Field name="price_in_cents">
									{#snippet children(field)}
										<div class="form-control group">
											<label class="label" for="price-input">
												<span class="label-text font-semibold text-base-content/90 flex items-center gap-2 text-sm lg:text-base">
													💰 Selling Price
													<span class="badge badge-success badge-xs">Optional</span>
												</span>
											</label>
											<div class="relative">
												<label class="input input-bordered input-lg lg:input-md flex items-center gap-3 transition-all duration-300 focus-within:input-success group-hover:border-success/50">
													<span class="text-success font-bold text-base lg:text-lg">$</span>
													<input
														id="price-input"
														type="text"
														inputmode="decimal"
														class="grow text-base lg:text-lg"
														placeholder="24.99"
														use:currencyInput={Number(field.value)}
														onchange={(e) => handleCurrencyChange(e, 'price_in_cents')}
														onblur={field.handleBlur}
														name="price_in_cents"
													/>
													<span class="text-success/70 text-xs lg:text-sm font-medium">Revenue</span>
												</label>
												<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-success/5 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
											</div>
											<Errors name="price_in_cents" />
										</div>
									{/snippet}
								</Field>

								<!-- Purchase Cost Field -->
								<Field name="cost_in_cents">
									{#snippet children(field)}
										<div class="form-control group">
											<label class="label" for="cost-input">
												<span class="label-text font-semibold text-base-content/90 flex items-center gap-2 text-sm lg:text-base">
													🏷️ Purchase Cost
													<span class="badge badge-error badge-xs">Required</span>
												</span>
											</label>
											<div class="relative">
												<label class="input input-bordered input-lg lg:input-md flex items-center gap-3 transition-all duration-300 focus-within:input-warning group-hover:border-warning/50">
													<span class="text-warning font-bold text-base lg:text-lg">$</span>
													<input
														id="cost-input"
														type="text"
														inputmode="decimal"
														class="grow text-base lg:text-lg"
														placeholder="12.50"
														use:currencyInput={Number(field.value)}
														onchange={(e) => handleCurrencyChange(e, 'cost_in_cents')}
														onblur={field.handleBlur}
														name="cost_in_cents"
														required
													/>
													<span class="text-warning/70 text-xs lg:text-sm font-medium">Expense</span>
												</label>
												<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-warning/5 to-error/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
											</div>
											<div class="label">
												<span class="label-text-alt text-xs text-base-content/60">💡 Enter 0 if this item has no cost</span>
											</div>
											<Errors name="cost_in_cents" />
										</div>
									{/snippet}
								</Field>
							</div>

							<!-- Inventory Management Section - Mobile Optimized -->
							<div class="grid grid-cols-1 gap-6 lg:gap-8 sm:grid-cols-2">
								<!-- Reorder Threshold Field -->
								<Field name="reorder_threshold">
									{#snippet children(field)}
										<div class="form-control group">
											<label class="label" for="reorder-threshold-input">
												<span class="label-text font-semibold text-base-content/90 flex items-center gap-2 text-sm lg:text-base">
													🔔 Reorder Alert
													<span class="badge badge-info badge-xs">Optional</span>
												</span>
											</label>
											<div class="relative">
												<input
													id="reorder-threshold-input"
													type="number"
													inputmode="numeric"
													min="0"
													step="1"
													name="reorder_threshold"
													class="input input-bordered input-lg lg:input-md w-full transition-all duration-300 focus:input-info lg:focus:scale-[1.02] group-hover:border-info/50 text-base"
													placeholder="Get notified when stock is low..."
													bind:value={(form.values as any).reorder_threshold}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-info/5 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
											</div>
											<div class="label">
												<span class="label-text-alt text-xs text-base-content/60">💡 Set a minimum quantity to get low stock alerts</span>
											</div>
											<Errors name="reorder_threshold" />
										</div>
									{/snippet}
								</Field>

								<!-- Supplier Field -->
								<Field name="supplier">
									{#snippet children(field)}
										<div class="form-control group">
											<label class="label" for="supplier-input">
												<span class="label-text font-semibold text-base-content/90 flex items-center gap-2 text-sm lg:text-base">
													🏪 Supplier
													<span class="badge badge-ghost badge-xs">Optional</span>
												</span>
											</label>
											<div class="relative">
												<input
													id="supplier-input"
													type="text"
													name="supplier"
													class="input input-bordered input-lg lg:input-md w-full transition-all duration-300 focus:input-accent lg:focus:scale-[1.02] group-hover:border-accent/50 text-base"
													placeholder="Where do you buy this from?"
													bind:value={(form.values as any).supplier}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-accent/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
											</div>
											<Errors name="supplier" />
										</div>
									{/snippet}
								</Field>
							</div>

						</div>
					</div>

					<!-- Hidden input for image data -->
					<input type="hidden" name="image_base64" bind:value={(form.values as any).image_base64} />

					<!-- Mobile-Optimized Form Actions -->
					<div class="mt-8 lg:mt-12 flex justify-center">
						<button
							type="submit"
							class="btn btn-primary btn-lg lg:btn-md w-full max-w-xs lg:w-auto transition-all duration-200 hover:shadow-md"
							disabled={form.isSubmitting}
						>
							{#if form.isSubmitting}
								<span class="loading loading-spinner loading-sm"></span>
								Saving...
							{:else}
								{submitLabel}
							{/if}
						</button>
					</div>
				</form>
			{/snippet}
		</RuneForm>
	</div>
</div>
