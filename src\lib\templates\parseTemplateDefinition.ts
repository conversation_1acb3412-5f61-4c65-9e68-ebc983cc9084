import { templateDefinitionSchema, type TemplateDefinition } from '$lib/schemas/template';

/**
 * Convert the raw `template_definition` JSONB value coming from the database
 * into a fully-typed `TemplateDefinition`. If validation fails the function
 * returns `undefined` rather than throwing – callers can decide what to do
 * (e.g. show a warning message).
 */
export function parseTemplateDefinition(raw: unknown): TemplateDefinition | undefined {
	const parsed = templateDefinitionSchema.safeParse(raw);
	return parsed.success ? parsed.data : undefined;
}
