<!--
  Inventory Update Mode Page
  Streamlined interface for scanning items and quickly updating quantities or adding new items
-->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { currencyInput } from '$lib/actions/currencyInput';
	import BarcodeScanner from '$lib/components/barcode/BarcodeScanner.svelte';
	import ImageUpload from '$lib/components/ui/ImageUpload.svelte';
	import { ERROR_MESSAGES, INFO_MESSAGES, SUCCESS_MESSAGES, TIMING } from '$lib/config';
	import { toast } from '$lib/ui/toast';
	import { searchByBarcode, type InventoryItem } from '$lib/utils/barcode';
	import IconArrowLeft from '~icons/icon-park-outline/arrow-left';
	import IconCheck from '~icons/icon-park-outline/check';
	import IconClose from '~icons/icon-park-outline/close';
	import IconPlus from '~icons/icon-park-outline/plus';
	import IconScan from '~icons/icon-park-outline/scan';
	import type { PageData } from './$types';

	interface SessionItem {
		action: 'updated' | 'added';
		item: InventoryItem;
		timestamp: Date;
	}

	interface NewItemForm {
		name: string;
		quantity: number;
		cost_in_cents: number;
		price_in_cents: number | undefined;
		barcode: string;
		image_base64: string;
	}

	let { data }: { data: PageData } = $props();

	// State management
	let currentMode = $state<'scanning' | 'updating' | 'adding'>('scanning');
	let currentItem = $state<InventoryItem | null>(null);
	let sessionItems = $state<SessionItem[]>([]);
	let isProcessing = $state(false);
	let manualBarcodeInput = $state('');
	let showManualInput = $state(false);
	let showAdvancedFields = $state(false);
	let nameInputElement = $state<HTMLInputElement>();
	let showHelp = $state(false);

	// Form state for quick updates
	let newQuantity = $state(0);
	let quantityInputElement = $state<HTMLInputElement>();
	let newItemForm = $state<NewItemForm>({
		name: '',
		quantity: 1,
		cost_in_cents: 0,
		price_in_cents: undefined,
		barcode: '',
		image_base64: ''
	});

	async function handleBarcodeScanned(barcode: string) {
		await processBarcode(barcode);
	}

	async function handleManualBarcodeSubmit() {
		if (!manualBarcodeInput.trim()) return;
		await processBarcode(manualBarcodeInput.trim());
		manualBarcodeInput = '';
		showManualInput = false;
	}

	async function processBarcode(barcode: string) {
		if (isProcessing) return;

		isProcessing = true;
		try {
			// Look up the item by barcode
			const existingItem = await searchByBarcode(barcode);

			if (existingItem) {
				// Item exists - show quantity update form
				currentItem = existingItem;
				newQuantity = existingItem.quantity;
				currentMode = 'updating';
				toast.success(`Found: ${existingItem.name}`);
			} else {
				// Item doesn't exist - show add item form
				newItemForm.barcode = barcode;
				newItemForm.name = '';
				newItemForm.quantity = 1;
				newItemForm.cost_in_cents = 0;
				newItemForm.price_in_cents = undefined;
				newItemForm.image_base64 = '';
				currentMode = 'adding';
				toast.info(INFO_MESSAGES.PROCESSING);
			}
		} catch (error) {
			console.error('Error looking up barcode:', error);
			toast.error(ERROR_MESSAGES.NETWORK_ERROR);
		} finally {
			isProcessing = false;
		}
	}

	async function updateQuantity() {
		if (!currentItem || isProcessing) return;

		isProcessing = true;
		try {
			const response = await fetch(`/api/inventory/${currentItem.id}`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					quantity: newQuantity
				})
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(errorData.details || errorData.error || 'Failed to update quantity');
			}

			// Add to session tracking
			sessionItems.push({
				action: 'updated',
				item: { ...currentItem, quantity: newQuantity },
				timestamp: new Date()
			});

			toast.success(SUCCESS_MESSAGES.QUANTITY_UPDATED);

			// Return to scanning mode
			currentMode = 'scanning';
			currentItem = null;
			newQuantity = 0;
		} catch (error) {
			console.error('Error updating quantity:', error);
			const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.GENERIC_ERROR;
			toast.error(errorMessage);
		} finally {
			isProcessing = false;
		}
	}

	async function addNewItem() {
		if (isProcessing) return;

		// Basic validation
		if (!newItemForm.name.trim()) {
			toast.error(ERROR_MESSAGES.REQUIRED_FIELD);
			return;
		}

		if (newItemForm.quantity < 0) {
			toast.error(ERROR_MESSAGES.INVALID_QUANTITY);
			return;
		}

		if (newItemForm.cost_in_cents < 0) {
			toast.error(ERROR_MESSAGES.INVALID_QUANTITY);
			return;
		}

		isProcessing = true;
		try {
			const response = await fetch('/api/inventory', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(newItemForm)
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(errorData.details || errorData.error || 'Failed to add item');
			}

			const result = await response.json();

			// Add to session tracking
			sessionItems.push({
				action: 'added',
				item: { ...newItemForm, id: result.id },
				timestamp: new Date()
			});

			toast.success(SUCCESS_MESSAGES.INVENTORY_ITEM_CREATED);

			// Return to scanning mode
			currentMode = 'scanning';
			resetNewItemForm();
		} catch (error) {
			console.error('Error adding item:', error);
			const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.GENERIC_ERROR;
			toast.error(errorMessage);
		} finally {
			isProcessing = false;
		}
	}

	function resetNewItemForm() {
		newItemForm = {
			name: '',
			quantity: 1,
			cost_in_cents: 0,
			price_in_cents: undefined,
			barcode: '',
			image_base64: ''
		};
	}

	function cancelCurrentAction() {
		currentMode = 'scanning';
		currentItem = null;
		newQuantity = 0;
		resetNewItemForm();
	}

	function exitUpdateMode() {
		goto('/app/inventory');
	}

	function adjustQuantity(delta: number) {
		newQuantity = Math.max(0, newQuantity + delta);
	}

	function setQuantity(value: number) {
		newQuantity = Math.max(0, value);
	}

	// Auto-focus inputs when switching modes
	$effect(() => {
		if (currentMode === 'updating' && quantityInputElement) {
			setTimeout(() => {
				quantityInputElement?.focus();
				quantityInputElement?.select();
			}, TIMING.SCANNER_PROCESSING_DELAY);
		} else if (currentMode === 'adding' && nameInputElement) {
			setTimeout(() => {
				nameInputElement?.focus();
			}, TIMING.SCANNER_PROCESSING_DELAY);
		}
	});

	// Global keyboard shortcuts
	function handleGlobalKeydown(event: KeyboardEvent) {
		// Escape key - cancel current action or exit
		if (event.key === 'Escape') {
			if (currentMode !== 'scanning') {
				cancelCurrentAction();
			} else {
				exitUpdateMode();
			}
			return;
		}

		// Only handle shortcuts when not typing in inputs
		if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
			return;
		}

		// Global shortcuts
		if (event.key === '?' && (event.shiftKey || event.key === '?')) {
			event.preventDefault();
			showHelp = !showHelp;
			return;
		}

		// Keyboard shortcuts for scanning mode
		if (currentMode === 'scanning') {
			if (event.key === 't' || event.key === 'T') {
				event.preventDefault();
				showManualInput = !showManualInput;
			}
		}

		// Keyboard shortcuts for updating mode
		if (currentMode === 'updating') {
			if (event.key === '+' || event.key === '=') {
				event.preventDefault();
				adjustQuantity(1);
			} else if (event.key === '-') {
				event.preventDefault();
				adjustQuantity(-1);
			} else if (event.key === '0') {
				event.preventDefault();
				setQuantity(0);
			} else if (event.key === '1') {
				event.preventDefault();
				setQuantity(1);
			} else if (event.key === '5') {
				event.preventDefault();
				setQuantity(5);
			}
		}
	}


</script>

<svelte:head>
	<title>Inventory Update Mode - Hairloom</title>
</svelte:head>

<svelte:window onkeydown={handleGlobalKeydown} />

<div class="container mx-auto p-4 max-w-4xl">
	<!-- Header -->
	<div class="mb-6">
		<!-- Mobile Header -->
		<div class="bg-base-100/80 backdrop-blur-sm border border-base-300/30 rounded-xl shadow-sm p-4 mb-4 lg:hidden">
			<div class="flex items-center justify-between">
				<button class="btn btn-ghost btn-sm" onclick={exitUpdateMode}>
					<IconArrowLeft class="h-4 w-4" />
					Back
				</button>
				<div class="text-center">
					<h1 class="text-lg font-semibold text-base-content/90">Update Mode</h1>
					<div class="badge badge-primary badge-sm">{sessionItems.length} items</div>
				</div>
				<button class="btn btn-error btn-sm" onclick={exitUpdateMode}>
					<IconClose class="h-4 w-4" />
				</button>
			</div>
		</div>

		<!-- Desktop Header -->
		<div class="hidden lg:block">
			<div class="bg-base-100/80 backdrop-blur-sm border border-base-300/30 rounded-xl shadow-sm p-6">
				<div class="flex items-center justify-between">
					<div class="flex items-center gap-4">
						<button class="btn btn-ghost btn-sm transition-colors duration-200 hover:bg-base-200/50" onclick={exitUpdateMode}>
							<IconArrowLeft class="h-4 w-4" />
							Back to Inventory
						</button>
						<div class="flex items-center gap-3">
							<div class="w-2 h-2 bg-secondary/40 rounded-full"></div>
							<div>
								<h1 class="text-2xl font-semibold text-base-content/90">Inventory Update Mode</h1>
								<p class="text-sm text-base-content/60 ml-5">Scan items to quickly update quantities or add new products</p>
							</div>
						</div>
					</div>
					<div class="flex items-center gap-4">
						<div class="bg-base-200/50 border border-base-300/30 rounded-lg p-3">
							<div class="text-xs text-base-content/60">Items Processed</div>
							<div class="text-lg font-semibold text-base-content/90">{sessionItems.length}</div>
						</div>
						<button
							class="btn btn-ghost btn-sm transition-colors duration-200 hover:bg-base-200/50"
							onclick={() => showHelp = !showHelp}
							title="Keyboard shortcuts (Press ?)"
						>
							?
						</button>
						<button class="btn btn-error btn-sm transition-all duration-200 hover:shadow-md" onclick={exitUpdateMode}>
							<IconClose class="h-4 w-4" />
							Exit
						</button>
					</div>
				</div>
			</div>
		</div>

	<!-- Main Content -->
	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
		<!-- Scanner Section -->
		<div class="lg:col-span-2">
			<div class="card bg-base-200 shadow-lg">
				<div class="card-body">
					<div class="flex items-center gap-3 mb-4">
						<IconScan class="h-6 w-6 text-primary" />
						<h2 class="card-title">Scan Barcode</h2>
						{#if isProcessing}
							<span class="loading loading-spinner loading-sm"></span>
						{/if}
					</div>

					{#if currentMode === 'scanning'}
						<div class="space-y-4">
							<!-- Mobile: Prominent Manual Input Button -->
							<div class="lg:hidden">
								<div class="flex gap-2 mb-4">
									<button
										class="btn btn-primary flex-1"
										onclick={() => showManualInput = !showManualInput}
									>
										{#if showManualInput}
											<IconScan class="h-4 w-4" />
											Scan Instead
										{:else}
											Type Barcode
										{/if}
									</button>
									<button
										class="btn btn-ghost"
										onclick={() => showHelp = !showHelp}
									>
										Help
									</button>
								</div>
							</div>

							<!-- Desktop: Traditional Layout -->
							<div class="hidden lg:flex items-center justify-between">
								<div>
									<p class="text-sm text-base-content/70">
										Scan a barcode to update quantity or add new item
									</p>
									<p class="text-xs text-base-content/50">
										Press <kbd class="kbd kbd-xs">T</kbd> to type barcode manually
									</p>
								</div>
								<button
									class="btn btn-sm btn-outline"
									onclick={() => showManualInput = !showManualInput}
								>
									{showManualInput ? 'Hide' : 'Type Barcode'}
								</button>
							</div>

							{#if showManualInput}
								<div class="card bg-base-100 border border-base-300">
									<div class="card-body p-4">
										<!-- Mobile: Stacked Layout -->
										<!-- Mobile: Touch-Optimized Layout -->
										<div class="lg:hidden space-y-4">
											<label class="label" for="mobile-barcode-input">
												<span class="label-text font-medium text-base">Enter Barcode</span>
											</label>
											<div class="join w-full">
												<input
													id="mobile-barcode-input"
													type="text"
													inputmode="text"
													class="input input-bordered input-lg join-item flex-1 text-base"
													placeholder="Type or paste barcode"
													bind:value={manualBarcodeInput}
													onkeydown={(e) => {
														if (e.key === 'Enter') {
															handleManualBarcodeSubmit();
														}
													}}
												/>
												<button
													class="btn btn-primary btn-lg join-item"
													onclick={handleManualBarcodeSubmit}
													disabled={!manualBarcodeInput.trim() || isProcessing}
												>
													{#if isProcessing}
														<span class="loading loading-spinner loading-sm"></span>
													{:else}
														<span class="hidden sm:inline">Go</span>
														<span class="sm:hidden">✓</span>
													{/if}
												</button>
											</div>
											<p class="text-xs text-base-content/60 text-center">
												Enter the numbers/letters from the barcode
											</p>
										</div>

										<!-- Desktop: Horizontal Layout -->
										<div class="hidden lg:flex gap-2">
											<input
												type="text"
												class="input input-bordered flex-1"
												placeholder="Enter barcode manually"
												bind:value={manualBarcodeInput}
												onkeydown={(e) => {
													if (e.key === 'Enter') {
														handleManualBarcodeSubmit();
													}
												}}
											/>
											<button
												class="btn btn-primary"
												onclick={handleManualBarcodeSubmit}
												disabled={!manualBarcodeInput.trim() || isProcessing}
											>
												{#if isProcessing}
													<span class="loading loading-spinner loading-sm"></span>
												{:else}
													Search
												{/if}
											</button>
										</div>
									</div>
								</div>
							{/if}

							<!-- Mobile: Optimized Scanner Interface -->
							<div class="lg:hidden">
								{#if !showManualInput}
									<div class="text-center mb-4">
										<div class="flex items-center justify-center gap-2 mb-2">
											<IconScan class="h-6 w-6 text-primary" />
											<p class="text-base font-medium">Scan Barcode</p>
										</div>
										<p class="text-sm text-base-content/60">Point camera at barcode to scan</p>
									</div>
									<!-- Mobile Scanner Container - Better proportions -->
									<div class="bg-black rounded-xl overflow-hidden shadow-lg" style="height: 240px;">
										<BarcodeScanner
											onScan={handleBarcodeScanned}
											onError={(error) => toast.error(error)}
											isActive={!showManualInput && currentMode === 'scanning'}
										/>
									</div>
									<div class="text-center mt-4 space-y-2">
										<p class="text-xs text-base-content/50">
											💡 Hold steady and ensure good lighting
										</p>
										<div class="flex justify-center">
											<button
												class="btn btn-ghost btn-sm text-primary"
												onclick={() => showManualInput = true}
											>
												Having trouble? Type barcode instead
											</button>
										</div>
									</div>
								{:else}
									<div class="text-center mb-4">
										<div class="flex items-center justify-center gap-2 mb-2">
											<span class="text-lg">⌨️</span>
											<p class="text-base font-medium">Type Barcode</p>
										</div>
										<p class="text-sm text-base-content/60">
											Enter the barcode numbers manually
										</p>
									</div>
								{/if}
							</div>

							<!-- Desktop: Full Scanner -->
							<div class="hidden lg:block aspect-video bg-black rounded-lg overflow-hidden">
								<BarcodeScanner
									onScan={handleBarcodeScanned}
									onError={(error) => toast.error(error)}
									isActive={!showManualInput && currentMode === 'scanning'}
								/>
							</div>
						</div>

					{:else if currentMode === 'updating'}
						<!-- Mobile-Optimized Update Interface -->
						<div class="space-y-4 lg:space-y-6">
							<!-- Item Info Card -->
							<div class="alert alert-info p-4">
								<IconCheck class="h-5 w-5 flex-shrink-0" />
								<div class="min-w-0 flex-1">
									<h3 class="font-bold text-sm lg:text-base truncate">{currentItem?.name}</h3>
									<div class="text-xs lg:text-sm mt-1 space-y-1">
										<div>Current: <span class="font-semibold">{currentItem?.quantity}</span></div>
										<div class="truncate">Barcode: <span class="font-mono text-xs">{currentItem?.barcode}</span></div>
									</div>
								</div>
							</div>

							<!-- Mobile: Touch-Friendly Quick Buttons -->
							<div class="lg:hidden">
								<p class="text-sm font-medium text-base-content/80 mb-3">Quick Set:</p>
								<div class="grid grid-cols-4 gap-2">
									<button
										class="btn btn-outline btn-lg aspect-square"
										onclick={() => setQuantity(0)}
									>
										<span class="text-lg font-bold">0</span>
									</button>
									<button
										class="btn btn-outline btn-lg aspect-square"
										onclick={() => setQuantity(1)}
									>
										<span class="text-lg font-bold">1</span>
									</button>
									<button
										class="btn btn-outline btn-lg aspect-square"
										onclick={() => setQuantity(5)}
									>
										<span class="text-lg font-bold">5</span>
									</button>
									<button
										class="btn btn-outline btn-lg aspect-square"
										onclick={() => setQuantity(10)}
									>
										<span class="text-lg font-bold">10</span>
									</button>
								</div>
							</div>

							<!-- Desktop: Compact Quick Buttons -->
							<div class="hidden lg:block">
								<div class="grid grid-cols-4 gap-2">
									<button
										class="btn btn-outline btn-sm"
										onclick={() => setQuantity(0)}
									>
										0
									</button>
									<button
										class="btn btn-outline btn-sm"
										onclick={() => setQuantity(1)}
									>
										1
									</button>
									<button
										class="btn btn-outline btn-sm"
										onclick={() => setQuantity(5)}
									>
										5
									</button>
									<button
										class="btn btn-outline btn-sm"
										onclick={() => setQuantity(10)}
									>
										10
									</button>
								</div>
							</div>

							<!-- Mobile-Optimized Quantity Input -->
							<div class="form-control">
								<label class="label" for="quantity-input">
									<span class="label-text font-semibold text-sm lg:text-base">New Quantity</span>
								</label>
								<div class="join">
									<button
										class="btn btn-outline btn-lg lg:btn-md join-item w-16 lg:w-auto"
										onclick={() => adjustQuantity(-1)}
										disabled={newQuantity <= 0}
									>
										<span class="text-xl lg:text-base font-bold">-</span>
									</button>
									<input
										bind:this={quantityInputElement}
										id="quantity-input"
										type="number"
										inputmode="numeric"
										min="0"
										class="input input-bordered input-lg lg:input-md join-item flex-1 text-center font-bold text-xl lg:text-lg"
										bind:value={newQuantity}
										onkeydown={(e) => {
											if (e.key === 'Enter') {
												updateQuantity();
											}
										}}
									/>
									<button
										class="btn btn-outline btn-lg lg:btn-md join-item w-16 lg:w-auto"
										onclick={() => adjustQuantity(1)}
									>
										<span class="text-xl lg:text-base font-bold">+</span>
									</button>
								</div>
							</div>

							<!-- Mobile: Touch-Friendly Adjustment Shortcuts -->
							<div class="lg:hidden">
								<p class="text-sm font-medium text-base-content/80 mb-3">Quick Adjust:</p>
								<div class="grid grid-cols-3 gap-2">
									<button
										class="btn btn-outline btn-lg"
										onclick={() => adjustQuantity(-5)}
										disabled={newQuantity < 5}
									>
										<span class="text-lg font-bold">-5</span>
									</button>
									<button
										class="btn btn-outline btn-lg"
										onclick={() => adjustQuantity(-1)}
										disabled={newQuantity <= 0}
									>
										<span class="text-lg font-bold">-1</span>
									</button>
									<button
										class="btn btn-outline btn-lg"
										onclick={() => adjustQuantity(1)}
									>
										<span class="text-lg font-bold">+1</span>
									</button>
								</div>
							</div>

							<!-- Desktop: Compact Shortcuts -->
							<div class="hidden lg:grid grid-cols-3 gap-2">
								<button
									class="btn btn-sm btn-outline"
									onclick={() => adjustQuantity(-5)}
									disabled={newQuantity < 5}
								>
									-5
								</button>
								<button
									class="btn btn-sm btn-outline"
									onclick={() => adjustQuantity(-1)}
									disabled={newQuantity <= 0}
								>
									-1
								</button>
								<button
									class="btn btn-sm btn-outline"
									onclick={() => adjustQuantity(1)}
								>
									+1
								</button>
							</div>

							<!-- Mobile-Optimized Action Buttons -->
							<div class="flex flex-col gap-3 lg:flex-row lg:gap-2">
								<button
									class="btn btn-ghost btn-lg lg:btn-md lg:flex-1 order-2 lg:order-1"
									onclick={cancelCurrentAction}
									disabled={isProcessing}
								>
									Cancel
								</button>
								<button
									class="btn btn-primary btn-lg lg:btn-md lg:flex-1 order-1 lg:order-2"
									onclick={updateQuantity}
									disabled={isProcessing || newQuantity < 0}
								>
									{#if isProcessing}
										<span class="loading loading-spinner loading-sm"></span>
									{:else}
										<IconCheck class="h-4 w-4" />
									{/if}
									Update to {newQuantity}
								</button>
							</div>
						</div>

					{:else if currentMode === 'adding'}
						<div class="space-y-4">
							<div class="alert alert-success">
								<IconPlus class="h-4 w-4" />
								<div>
									<h3 class="font-bold">Add New Item</h3>
									<div class="text-xs font-mono">Barcode: {newItemForm.barcode}</div>
								</div>
							</div>

							<!-- Essential Fields -->
							<div class="space-y-3">
								<div class="form-control">
									<label class="label" for="item-name">
										<span class="label-text font-semibold">What is this item? *</span>
									</label>
									<input
										bind:this={nameInputElement}
										id="item-name"
										type="text"
										class="input input-bordered input-lg"
										bind:value={newItemForm.name}
										placeholder="e.g., Shampoo, Hair Dryer, Scissors..."
										onkeydown={(e) => {
											if (e.key === 'Enter' && newItemForm.name.trim()) {
												// Move to next field or submit if ready
												document.getElementById('item-quantity')?.focus();
											}
										}}
									/>
								</div>

								<div class="grid grid-cols-2 gap-3">
									<div class="form-control">
										<label class="label" for="item-quantity">
											<span class="label-text font-semibold">How many? *</span>
										</label>
										<input
											id="item-quantity"
											type="number"
											min="1"
											class="input input-bordered input-lg text-center"
											bind:value={newItemForm.quantity}
											onkeydown={(e) => {
												if (e.key === 'Enter') {
													document.getElementById('item-cost')?.focus();
												}
											}}
										/>
									</div>

									<div class="form-control">
										<label class="label" for="item-cost">
											<span class="label-text font-semibold">Cost each? *</span>
											<span class="label-text-alt text-xs">Enter 0 if no cost</span>
										</label>
										<label class="input input-bordered input-lg flex items-center gap-2">
											$
											<input
												id="item-cost"
												type="text"
												inputmode="decimal"
												class="grow text-center"
												use:currencyInput={newItemForm.cost_in_cents}
												onchange={(e) => {
													if (e instanceof CustomEvent) {
														newItemForm.cost_in_cents = e.detail.valueInCents;
													}
												}}
												placeholder="0.00"
												onkeydown={(e) => {
													if (e.key === 'Enter' && newItemForm.name.trim()) {
														addNewItem();
													}
												}}
											/>
										</label>
									</div>
								</div>
							</div>

							<!-- Quick Add Button -->
							<div class="flex gap-2">
								<button class="btn btn-ghost flex-1" onclick={cancelCurrentAction} disabled={isProcessing}>
									Cancel
								</button>
								<button
									class="btn btn-primary flex-1 btn-lg"
									onclick={addNewItem}
									disabled={isProcessing || !newItemForm.name.trim()}
								>
									{#if isProcessing}
										<span class="loading loading-spinner loading-sm"></span>
									{:else}
										<IconPlus class="h-4 w-4" />
									{/if}
									Add & Continue
								</button>
							</div>

							<!-- Advanced Options (Collapsible) -->
							<div class="collapse collapse-arrow bg-base-200">
								<input type="checkbox" bind:checked={showAdvancedFields} />
								<div class="collapse-title text-sm font-medium">
									More Options (selling price, photo)
								</div>
								<div class="collapse-content space-y-3">
									<div class="form-control">
										<label class="label" for="item-price">
											<span class="label-text">Selling Price (Optional)</span>
										</label>
										<label class="input input-bordered flex items-center gap-2">
											$
											<input
												id="item-price"
												type="text"
												inputmode="decimal"
												class="grow"
												use:currencyInput={newItemForm.price_in_cents}
												onchange={(e) => {
													if (e instanceof CustomEvent) {
														newItemForm.price_in_cents = e.detail.valueInCents || undefined;
													}
												}}
												placeholder="0.00"
											/>
										</label>
									</div>

									<!-- Image Upload -->
									<div class="form-control">
										<ImageUpload
											value={newItemForm.image_base64}
											onImageChange={(base64) => newItemForm.image_base64 = base64}
											maxWidth={800}
											maxHeight={600}
											quality={0.8}
											title="Add Product Image"
											subtitle="Upload a file or take a photo"
										/>
									</div>
								</div>
							</div>
						</div>
					{/if}
				</div>
			</div>
		</div>

		<!-- Session Summary Sidebar -->
		<div class="lg:col-span-1">
			<div class="card bg-base-200 shadow-lg sticky top-4">
				<div class="card-body">
					<div class="flex items-center gap-2 mb-4">
						<IconCheck class="h-5 w-5 text-success" />
						<h3 class="card-title text-lg">Session Activity</h3>
					</div>

					{#if sessionItems.length === 0}
						<div class="text-center py-8 text-base-content/50">
							<IconScan class="h-12 w-12 mx-auto mb-2 opacity-50" />
							<p class="text-sm">No items processed yet</p>
							<p class="text-xs">Scan a barcode to get started</p>
						</div>
					{:else}
						<div class="space-y-3 max-h-96 overflow-y-auto">
							{#each sessionItems.slice().reverse() as sessionItem}
								<div class="flex items-start gap-3 p-3 rounded-lg bg-base-100 border border-base-300">
									<div class="flex-shrink-0 mt-0.5">
										{#if sessionItem.action === 'updated'}
											<div class="badge badge-success badge-sm">
												<IconCheck class="h-3 w-3" />
											</div>
										{:else}
											<div class="badge badge-primary badge-sm">
												<IconPlus class="h-3 w-3" />
											</div>
										{/if}
									</div>
									<div class="flex-1 min-w-0">
										<div class="font-medium text-sm truncate">
											{sessionItem.item.name}
										</div>
										<div class="text-xs text-base-content/60">
											{#if sessionItem.action === 'updated'}
												Quantity updated to {sessionItem.item.quantity}
											{:else}
												Added to inventory
											{/if}
										</div>
										<div class="text-xs text-base-content/40 mt-1">
											{sessionItem.timestamp.toLocaleTimeString()}
										</div>
									</div>
								</div>
							{/each}
						</div>

						<div class="mt-4 pt-4 border-t border-base-300">
							<div class="flex justify-between items-center text-sm">
								<span class="text-base-content/70">Total processed:</span>
								<span class="font-bold">{sessionItems.length}</span>
							</div>
							<div class="flex justify-between items-center text-sm mt-1">
								<span class="text-base-content/70">Updated:</span>
								<span class="text-success font-medium">
									{sessionItems.filter(item => item.action === 'updated').length}
								</span>
							</div>
							<div class="flex justify-between items-center text-sm mt-1">
								<span class="text-base-content/70">Added:</span>
								<span class="text-primary font-medium">
									{sessionItems.filter(item => item.action === 'added').length}
								</span>
							</div>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Help Modal -->
{#if showHelp}
	<div class="modal modal-open">
		<div class="modal-box max-w-2xl">
			<h3 class="font-bold text-lg mb-4">Keyboard Shortcuts & Tips</h3>

			<div class="space-y-6">
				<!-- Global Shortcuts -->
				<div>
					<h4 class="font-semibold text-base mb-2">Global Shortcuts</h4>
					<div class="space-y-1 text-sm">
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">Esc</kbd></span>
							<span>Cancel current action or exit</span>
						</div>
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">?</kbd></span>
							<span>Show/hide this help</span>
						</div>
					</div>
				</div>

				<!-- Scanning Mode -->
				<div>
					<h4 class="font-semibold text-base mb-2">Scanning Mode</h4>
					<div class="space-y-1 text-sm">
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">T</kbd></span>
							<span>Toggle manual barcode input</span>
						</div>
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">Enter</kbd></span>
							<span>Submit manual barcode</span>
						</div>
					</div>
				</div>

				<!-- Quantity Update Mode -->
				<div>
					<h4 class="font-semibold text-base mb-2">Quantity Update Mode</h4>
					<div class="space-y-1 text-sm">
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">+</kbd> / <kbd class="kbd kbd-sm">=</kbd></span>
							<span>Increase quantity by 1</span>
						</div>
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">-</kbd></span>
							<span>Decrease quantity by 1</span>
						</div>
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">0</kbd>, <kbd class="kbd kbd-sm">1</kbd>, <kbd class="kbd kbd-sm">5</kbd></span>
							<span>Set quantity to specific number</span>
						</div>
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">Enter</kbd></span>
							<span>Save quantity update</span>
						</div>
					</div>
				</div>

				<!-- Adding Mode -->
				<div>
					<h4 class="font-semibold text-base mb-2">Adding New Items</h4>
					<div class="space-y-1 text-sm">
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">Enter</kbd></span>
							<span>Move to next field or submit</span>
						</div>
						<div class="flex justify-between">
							<span><kbd class="kbd kbd-sm">Tab</kbd></span>
							<span>Navigate between fields</span>
						</div>
					</div>
				</div>

				<!-- Tips -->
				<div>
					<h4 class="font-semibold text-base mb-2">💡 Tips</h4>
					<ul class="text-sm space-y-1 list-disc list-inside">
						<li>Use the quick quantity buttons for common values</li>
						<li>Only name, quantity, and cost are required for new items</li>
						<li>Advanced options (price, photo) are in the collapsible section</li>
						<li>The session summary tracks all your changes</li>
						<li>Scanner automatically pauses when typing manually</li>
					</ul>
				</div>
			</div>

			<div class="modal-action">
				<button class="btn" onclick={() => showHelp = false}>Got it!</button>
			</div>
		</div>
		<button type="button" class="modal-backdrop" onclick={() => showHelp = false} aria-label="Close help"></button>
	</div>
{/if}

</div>
