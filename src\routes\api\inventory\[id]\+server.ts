import { db } from '$lib/server/db';
import { inventory_items } from '$lib/server/db/schema';
import { json } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';

export const PATCH: RequestHandler = async ({ params, request, locals }) => {
	if (!locals.user) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	const itemId = params.id;
	if (!itemId) {
		return json({ error: 'Item ID is required' }, { status: 400 });
	}

	try {
		const body = await request.json();
		const { quantity } = body;

		if (typeof quantity !== 'number' || quantity < 0) {
			return json({ error: 'Valid quantity is required' }, { status: 400 });
		}

		// Update the item quantity
		const result = await db
			.update(inventory_items)
			.set({
				quantity,
				updated_at: new Date()
			})
			.where(
				and(
					eq(inventory_items.id, itemId),
					eq(inventory_items.user_id, locals.user.id)
				)
			)
			.returning();

		if (result.length === 0) {
			return json({ error: 'Item not found' }, { status: 404 });
		}

		return json({ success: true, item: result[0] });
	} catch (error) {
		console.error('Error updating inventory item:', error);
		return json({ error: 'Failed to update item' }, { status: 500 });
	}
};
