import 'dotenv/config';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../schema';
import { seedSystemTemplates } from './system-templates';

const connectionString = process.env.POSTGRES_URL;

if (!connectionString) {
	throw new Error('POSTGRES_URL environment variable is not set');
}

const client = postgres(connectionString, { ssl: 'require', max: 1 });
const db = drizzle(client, { schema });

/**
 * Seeds the production database with essential system data, such as note templates.
 * This script is designed to be non-destructive and idempotent.
 */
async function main() {
	console.log('Seeding production database...');

	await seedSystemTemplates(db);

	console.log('Production database seeding complete.');
	process.exit(0);
}

main().catch((err) => {
	console.error('Error during production database seeding:', err);
	process.exit(1);
});
