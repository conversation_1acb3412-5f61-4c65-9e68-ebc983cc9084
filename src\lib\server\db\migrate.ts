import { createPool } from '@vercel/postgres';
import 'dotenv/config';
import { drizzle } from 'drizzle-orm/vercel-postgres';
import { migrate } from 'drizzle-orm/vercel-postgres/migrator';
import * as schema from './schema';

const databaseUrl = process.env.POSTGRES_URL;

if (!databaseUrl) {
	throw new Error('POSTGRES_URL is not set. Please check your environment variables.');
}

export const db = drizzle(createPool({ connectionString: databaseUrl }), { schema });

export async function migrateDatabase() {
	console.log('Running migrations...');
	await migrate(db, { migrationsFolder: 'drizzle' });
	console.log('Migrations complete!');
}

async function main() {
	try {
		await migrateDatabase();
		process.exit(0);
	} catch (err) {
		console.error(err);
		process.exit(1);
	}
}

if (import.meta.url.startsWith('file:')) {
	const moduleUrl = new URL(import.meta.url);
	const executionUrl = new URL(process.argv[1], 'file:');
	if (moduleUrl.pathname === executionUrl.pathname) {
		main();
	}
}
