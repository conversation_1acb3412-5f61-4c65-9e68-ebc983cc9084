import { db } from '$lib/server/db';
import { clients } from '$lib/server/db/schema';
import { error, json } from '@sveltejs/kit';
import { and, eq, ilike, or } from 'drizzle-orm';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url, locals }) => {
	const user = locals.user;
	if (!user) {
		error(401, 'Unauthorized');
	}

	const searchTerm = url.searchParams.get('q');

	if (!searchTerm || searchTerm.length < 2) {
		return json([]);
	}

	try {
		const foundClients = await db
			.select({
				id: clients.id,
				first_name: clients.first_name,
				last_name: clients.last_name,
				email: clients.email
			})
			.from(clients)
			.where(
				and(
					eq(clients.user_id, user.id),
					or(
						ilike(clients.first_name, `%${searchTerm}%`),
						ilike(clients.last_name, `%${searchTerm}%`),
						ilike(clients.email, `%${searchTerm}%`)
					)
				)
			)
			.limit(10);

		return json(foundClients);
	} catch (e) {
		console.error('Client search failed:', e);
		error(500, 'Failed to search for clients');
	}
};
