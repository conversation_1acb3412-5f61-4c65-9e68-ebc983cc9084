import { insertInventoryItemSchema } from '$lib/schemas/inventory';
import { db } from '$lib/server/db';
import { inventory_items } from '$lib/server/db/schema';
import {
	createErrorResponse,
	createJsonResponse,
	handleValidationError,
	withAuthAndErrorHandling
} from '$lib/server/utils/api-response';
import { processImage } from '$lib/server/utils/imageProcessing';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = withAuthAndErrorHandling(async ({ request, locals }) => {
	// User is guaranteed to exist due to withAuthAndErrorHandling
	const user = locals.user!;
	const body = await request.json();

	// Validate the request body
	const validation = insertInventoryItemSchema.safeParse(body);

	if (!validation.success) {
		const { response, status } = handleValidationError(validation.error);
		return json(response, { status });
	}

		const values = validation.data;

		// Process image if provided
		let processedImageBase64: string | null = values.image_base64 || null;
		if (values.image_base64 && values.image_base64.trim() !== '') {
			try {
				processedImageBase64 = await processImage(values.image_base64, {
					maxWidth: 1600,
					maxHeight: 1200,
					quality: 80,
					format: 'jpeg'
				});
			} catch (imageError) {
				console.error('Image processing error:', imageError);
				return createErrorResponse(
					'Failed to process image',
					'VALIDATION_ERROR',
					{ image_base64: ['Failed to process image. Please try a different image.'] },
					400
				);
			}
		} else if (values.image_base64 === '') {
			// No image provided
			processedImageBase64 = null;
		}

		// Insert the new inventory item
		const result = await db
			.insert(inventory_items)
			.values({
				...values,
				image_base64: processedImageBase64,
				user_id: user.id
			})
			.returning();

		return createJsonResponse({
			item: result[0],
			id: result[0].id
		}, 'Inventory item created successfully');
});
