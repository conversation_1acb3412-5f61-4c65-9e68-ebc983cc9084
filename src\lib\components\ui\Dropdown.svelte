<!--
  @component Dropdown
  @description A reusable, accessible dropdown component.

  This component provides a flexible dropdown container. It uses a slot-based architecture,
  allowing you to define a `trigger` (the element that opens the dropdown) and the
  `content` (the menu that appears).

  **Features:**
  - Manages its own open/close state using Svelte 5 runes.
  - Automatically closes when the user clicks outside of the dropdown content.
  - Closes when the 'Escape' key is pressed.
  - Uses svelte/transition for smooth animations.
-->
<script lang="ts">
	import type { Snippet } from 'svelte';
	import { quintOut } from 'svelte/easing';
	import { fly } from 'svelte/transition';

	let {
		children,
		trigger,
		align = 'right',
		width = 'w-56',
		contentClasses = 'p-2'
	}: {
		children: Snippet;
		trigger: Snippet;
		align?: 'left' | 'right';
		width?: string;
		contentClasses?: string;
	} = $props();

	let open = $state(false);
	let dropdownElement: HTMLElement | undefined = $state();

	const alignmentClasses = {
		left: 'origin-top-left left-0',
		right: 'origin-top-right right-0'
	};

	function closeOnEscape(event: KeyboardEvent) {
		if (open && event.key === 'Escape') {
			open = false;
		}
	}

	$effect(() => {
		if (!open || !dropdownElement) return;

		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
				open = false;
			}
		};

		document.addEventListener('mousedown', handleClickOutside);

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	});
</script>

<svelte:window on:keydown={closeOnEscape} />

<div class="relative" bind:this={dropdownElement}>
	<button
		type="button"
		onclick={() => {
			open = !open;
		}}
		aria-haspopup="true"
		aria-expanded={open}
	>
		{@render trigger()}
	</button>

	{#if open}
		<div
			transition:fly={{ duration: 200, y: 5, easing: quintOut }}
			class="absolute mt-2 {width} {alignmentClasses[
				align
			]} bg-base-200 border-base-content/10 z-50 rounded-md border shadow-lg"
			role="menu"
			tabindex="-1"
			onclick={() => {
				open = false;
			}}
			onkeydown={(e) => {
				if (e.key === 'Enter' || e.key === ' ') {
					open = false;
				}
			}}
		>
			<div class={contentClasses} role="presentation">
				{@render children()}
			</div>
		</div>
	{/if}
</div>
