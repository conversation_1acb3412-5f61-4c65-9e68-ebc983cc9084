<script lang="ts">
	/**
	 * @component ClientDetailPage
	 * @description This page displays the detailed view for a single client. It includes a form to
	 * update the client's information, a section to view and add notes, and an option to delete the client.
	 * All actions are progressively enhanced and provide feedback with appropriate confirmations and toasts.
	 */
	import { enhance } from '$app/forms';
	import DynamicFormRenderer from '$lib/components/templates/DynamicFormRenderer.svelte';
	import ImageUpload from '$lib/components/ui/ImageUpload.svelte';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { clientNoteAddSchema, clientUpsertSchema } from '$lib/schemas/client';
	import { confirm } from '$lib/ui/confirm';
	import { toast } from '$lib/ui/toast';
	import { z } from 'zod/v4';
	import IconArrowLeft from '~icons/icon-park-outline/arrow-left';
	import IconDelete from '~icons/icon-park-outline/delete';
	import IconEdit from '~icons/icon-park-outline/edit';
	import IconMail from '~icons/icon-park-outline/mail';
	import IconNotes from '~icons/icon-park-outline/notes';
	import IconPhone from '~icons/icon-park-outline/phone-telephone';
	import IconSave from '~icons/icon-park-outline/save';
	import IconUser from '~icons/icon-park-outline/user';
	import type { ActionData, PageData } from './$types';

	let { data, form: actionForm }: { data: PageData; form?: ActionData } = $props();
	let client = $derived(data.client);
	let clientNotes = $derived(data.clientNotes);
	let availableTemplates = $derived(data.availableTemplates);
	const plainTextTemplate = $derived(
		availableTemplates.find((t) => t.special_marker === 'plain_text_note')
	);

	// State for the new note form
	let selectedTemplateId = $state('');
	let templateFormData = $state<Record<string, any>>({});
	let isEditing = $state(false);

	$effect(() => {
		// When the plain text template becomes available, and if the user hasn't
		// selected another template, set the plain text template as the default.
		if (plainTextTemplate && selectedTemplateId === '') {
			selectedTemplateId = plainTextTemplate.id;
		}
	});

	const selectedTemplate = $derived(availableTemplates.find((t) => t.id === selectedTemplateId));

	$effect(() => {
		// When the selected template changes, reset the form data for it
		templateFormData = {};
	});

	const createClientForm = createRuneForm(clientUpsertSchema, {
		first_name: '',
		last_name: '',
		email: '',
		phone_number: '',
		image_base64: ''
	});

	const updateClientForm = createRuneForm(clientUpsertSchema, {
		first_name: '',
		last_name: '',
		email: '',
		phone_number: '',
		image_base64: ''
	});

	$effect(() => {
		if (client) {
			updateClientForm.values = {
				first_name: client.first_name ?? '',
				last_name: client.last_name ?? '',
				email: client.email ?? '',
				phone_number: client.phone_number ?? '',
				image_base64: client.image_base64 ?? ''
			};
		}
	});

	function handleCreateImageChange(base64Image: string) {
		createClientForm.values.image_base64 = base64Image;
	}

	function handleUpdateImageChange(base64Image: string) {
		updateClientForm.values.image_base64 = base64Image;
	}

	const addNoteForm = createRuneForm(clientNoteAddSchema, {
		templateId: '',
		note_data: {}
	});

	$effect(() => {
		addNoteForm.values.templateId = selectedTemplateId;
		addNoteForm.values.note_data = templateFormData;
	});

	const deleteClientForm = createRuneForm(z.object({}), {});

	let lastProcessedActionForm: ActionData | null | undefined = null;
	$effect(() => {
		if (!actionForm || actionForm === lastProcessedActionForm) return;
		lastProcessedActionForm = actionForm;

		// Create Client Action
		if ('createClient' in actionForm && actionForm.createClient) {
			const createClient = actionForm.createClient;
			if ('errors' in createClient && createClient.errors) {
				createClientForm.setErrors(createClient.errors);
				if ('root' in createClient.errors && createClient.errors.root) {
					toast.error(createClient.errors.root[0]);
				} else {
					toast.error('Please correct the errors to create the client.');
				}
			}
		}

		// Update Client Action
		if ('updateClient' in actionForm && actionForm.updateClient) {
			const updateClient = actionForm.updateClient;
			if ('errors' in updateClient && updateClient.errors) {
				updateClientForm?.setErrors(updateClient.errors);
				if ('root' in updateClient.errors && updateClient.errors.root) {
					toast.error(updateClient.errors.root[0]);
				} else {
					toast.error('Please correct the errors to update the client.');
				}
			} else if ('success' in updateClient && updateClient.success) {
				toast.success('Client updated successfully!');
				isEditing = false;
			}
		}

		// Add Note Action - Error Handling
		if ('addNote' in actionForm && actionForm.addNote) {
			const addNote = actionForm.addNote;
			if ('errors' in addNote && addNote.errors) {
				addNoteForm.setErrors(addNote.errors);
				if ('root' in addNote.errors && addNote.errors.root) {
					toast.error(addNote.errors.root[0]);
				} else {
					toast.error('Please correct the errors to add the note.');
				}
			}
		}

		// Delete Client Action
		if ('deleteClient' in actionForm && actionForm.deleteClient) {
			const deleteClient = actionForm.deleteClient;
			if ('errors' in deleteClient && deleteClient.errors && 'root' in deleteClient.errors) {
				toast.error(deleteClient.errors.root?.[0]);
			}
		}
	});

	// Separate effect to handle successful note addition to prevent loops
	let lastProcessedNoteSuccess: ActionData | null | undefined = null;
	$effect(() => {
		if (!actionForm || actionForm === lastProcessedNoteSuccess) return;
		const addNote = actionForm?.addNote;
		if (addNote && 'success' in addNote && addNote.success) {
			lastProcessedNoteSuccess = actionForm;
			toast.success('Note added successfully!');
			addNoteForm.reset();
			selectedTemplateId = plainTextTemplate?.id ?? ''; // Reset to plain text
			templateFormData = {};
		}
	});

	function formatNoteDate(date: Date | string) {
		return new Date(date).toLocaleString(undefined, {
			dateStyle: 'medium',
			timeStyle: 'short'
		});
	}
</script>

<div class="space-y-8">
	<a href="/app/clients" class="btn btn-ghost">
		<IconArrowLeft class="h-5 w-5" />
		Back to Clients
	</a>

	{#if client}
		<!-- Client Info section -->
		{#if isEditing}
			<div class="card bg-base-200/70 shadow-lg">
				<div class="card-body">
					<h2 class="card-title text-2xl">Update Client Information</h2>
					<RuneForm form={updateClientForm}>
						{#snippet children(form)}
							<form
								method="POST"
								action="?/updateClient"
								class="mt-4 space-y-4"
								use:enhance={() => {
									form.setSubmitting(true);
									return async ({ update }) => {
										await update({ reset: false });
										form.setSubmitting(false);
									};
								}}
								onsubmit={form.handleSubmit()}
							>
								<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
									<Field name="first_name">
										{#snippet children(field)}
											<div class="form-control">
												<label for="first_name" class="label"
													><span class="label-text">First Name</span></label
												>
												<input
													id="first_name"
													name="first_name"
													type="text"
													class="input input-bordered"
													value={String(field.value ?? '')}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<Errors name="first_name" />
											</div>
										{/snippet}
									</Field>
									<Field name="last_name">
										{#snippet children(field)}
											<div class="form-control">
												<label for="last_name" class="label"
													><span class="label-text">Last Name</span></label
												>
												<input
													id="last_name"
													name="last_name"
													type="text"
													class="input input-bordered"
													value={String(field.value ?? '')}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<Errors name="last_name" />
											</div>
										{/snippet}
									</Field>
								</div>
								<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
									<Field name="email">
										{#snippet children(field)}
											<div class="form-control">
												<label for="email" class="label"
													><span class="label-text">Email</span></label
												>
												<input
													id="email"
													name="email"
													type="email"
													class="input input-bordered"
													value={String(field.value ?? '')}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<Errors name="email" />
											</div>
										{/snippet}
									</Field>
									<Field name="phone_number">
										{#snippet children(field)}
											<div class="form-control">
												<label for="phone_number" class="label"
													><span class="label-text">Phone Number</span></label
												>
												<input
													id="phone_number"
													name="phone_number"
													type="tel"
													class="input input-bordered"
													value={String(field.value ?? '')}
													oninput={field.handleChange}
													onblur={field.handleBlur}
												/>
												<Errors name="phone_number" />
											</div>
										{/snippet}
									</Field>
								</div>

								<!-- Image Upload Section -->
								<div class="space-y-4">
									<div class="flex items-center gap-3">
										<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
										<div>
											<h3 class="text-lg font-semibold text-base-content/90">Client Photo</h3>
											<p class="text-sm text-base-content/60">Upload a photo of your client</p>
										</div>
									</div>
									<div class="bg-base-100/80 border border-base-300/30 rounded-xl p-6 shadow-sm">
										<ImageUpload
											value={updateClientForm.values.image_base64}
											onImageChange={handleUpdateImageChange}
											maxWidth={1600}
											maxHeight={1200}
											quality={0.8}
											title="Add Client Photo"
											subtitle="Upload a file or take a photo"
										/>
									</div>
								</div>

								<!-- Hidden input for image data -->
								<input type="hidden" name="image_base64" bind:value={updateClientForm.values.image_base64} />

								<div class="card-actions justify-end gap-2">
									<button
										type="button"
										class="btn btn-ghost"
										onclick={() => {
											isEditing = false;
										}}>Cancel</button
									>
									<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
										{#if form.isSubmitting}
											<span class="loading loading-spinner"></span>
											Saving...
										{:else}
											<IconSave class="h-5 w-5" />
											Save Changes
										{/if}
									</button>
								</div>
							</form>
						{/snippet}
					</RuneForm>
				</div>
			</div>
		{:else}
			<div class="card bg-base-200/70 shadow-lg">
				<div class="card-body">
					<div class="flex items-center justify-between">
						<h2 class="card-title text-2xl">Client Information</h2>
						<button class="btn btn-ghost" onclick={() => (isEditing = true)}>
							<IconEdit />
							Edit
						</button>
					</div>
					<div class="mt-4 flex flex-col gap-6 lg:flex-row">
						<!-- Client Image -->
						<div class="flex-shrink-0">
							{#if client.image_base64}
								<div class="avatar">
									<div class="w-32 h-32 rounded-xl shadow-md">
										<img
											src={client.image_base64}
											alt="{client.first_name} {client.last_name}"
											class="w-full h-full object-cover rounded-xl"
										/>
									</div>
								</div>
							{:else}
								<div class="w-32 h-32 bg-base-200/50 rounded-xl shadow-md flex items-center justify-center">
									<div class="text-center">
										<div class="w-12 h-12 mx-auto mb-2 bg-base-content/10 rounded-full flex items-center justify-center">
											<IconUser class="text-base-content/40 h-6 w-6" />
										</div>
										<p class="text-xs text-base-content/50">No Photo</p>
									</div>
								</div>
							{/if}
						</div>

						<!-- Client Details -->
						<div class="flex-1 grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
							<div class="flex items-center gap-2">
								<IconUser class="text-base-content/70 h-5 w-5" />
								<span class="font-semibold">Name:</span>
								<span>{client.first_name} {client.last_name}</span>
							</div>
							{#if client.email}
								<div class="flex items-center gap-2">
									<IconMail class="text-base-content/70 h-5 w-5" />
									<span class="font-semibold">Email:</span>
									<a href="mailto:{client.email}" class="link link-hover">{client.email}</a>
								</div>
							{/if}
							{#if client.phone_number}
								<div class="flex items-center gap-2">
									<IconPhone class="text-base-content/70 h-5 w-5" />
									<span class="font-semibold">Phone:</span>
									<span>{client.phone_number}</span>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>
		{/if}

		<div class="grid grid-cols-1 gap-8 lg:grid-cols-5">
			<!-- Client Notes -->
			<div class="space-y-4 lg:col-span-2">
				<div class="card bg-base-200/70 shadow-lg">
					<div class="card-body">
						<h2 class="card-title text-2xl">Add a Note</h2>
						<RuneForm form={addNoteForm}>
							{#snippet children(form)}
								<form
									method="POST"
									action="?/addNote"
									class="mt-2 space-y-4"
									use:enhance={() => {
										form.setSubmitting(true);
										return async ({ update }) => {
											await update({ reset: false });
											form.setSubmitting(false);
										};
									}}
									onsubmit={form.handleSubmit()}
								>
									<div class="form-control">
										<label for="template-select" class="label"
											><span class="label-text">Note Type</span></label
										>
										<select
											name="templateId"
											id="template-select"
											class="select select-bordered"
											bind:value={selectedTemplateId}
										>
											{#each availableTemplates as template (template.id)}
												<option value={template.id}>{template.name}</option>
											{/each}
										</select>
									</div>

									<input type="hidden" name="note_data" value={JSON.stringify(templateFormData)} />
									{#if selectedTemplate}
										<DynamicFormRenderer
											template={selectedTemplate}
											bind:formData={templateFormData}
										/>
									{/if}

									<Errors name="root" />

									<div class="card-actions justify-end">
										<button type="submit" class="btn btn-secondary" disabled={form.isSubmitting}>
											{#if form.isSubmitting}
												<span class="loading loading-spinner"></span>
												Adding...
											{:else}
												Add Note
											{/if}
										</button>
									</div>
								</form>
							{/snippet}
						</RuneForm>
					</div>
				</div>
			</div>

			<div class="space-y-4 lg:col-span-3">
				<h2 class="text-2xl font-bold">Client History</h2>
				{#if clientNotes.length > 0}
					<div class="space-y-4">
						{#each clientNotes as note (note.id)}
							<div class="card card-compact bg-base-100 shadow">
								<div class="card-body">
									<div class="flex items-center justify-between">
										<h3 class="card-title text-base">
											{note.template.name}
										</h3>
										<time class="text-base-content/60 text-xs">
											{formatNoteDate(note.created_at)}
										</time>
									</div>
									{#if note.note_data && note.template?.template_definition?.fields}
										{@const fieldLabels = new Map(
											note.template.template_definition.fields.map((f: any) => [f.name, f.label])
										)}
										<div class="text-base-content/90 prose prose-sm max-w-none pt-2">
											<ul>
												{#each Object.entries(note.note_data) as [key, value]}
													<li><strong>{fieldLabels.get(key) || key}:</strong> {String(value)}</li>
												{/each}
											</ul>
										</div>
									{:else if note.note_data}
										<!-- Fallback for data without full template info -->
										<div class="text-base-content/90 prose prose-sm max-w-none pt-2">
											<ul>
												{#each Object.entries(note.note_data) as [key, value]}
													<li><strong>{key}:</strong> {String(value)}</li>
												{/each}
											</ul>
										</div>
									{/if}
								</div>
							</div>
						{/each}
					</div>
				{:else}
					<div
						class="bg-base-200/70 flex flex-col items-center justify-center gap-4 rounded-lg p-8 text-center shadow-lg"
					>
						<IconNotes class="text-base-content/20 h-16 w-16" />
						<p class="font-bold">No notes yet</p>
						<p class="text-base-content/60 text-sm">Add a note above to start the history.</p>
					</div>
				{/if}
			</div>
		</div>

		<!-- Delete Client Section -->
		<div class="divider"></div>
		<div class="card border-error bg-base-100/80 border-2 shadow-xl">
			<div class="card-body">
				<h2 class="card-title text-error">Danger Zone</h2>
				<p>Deleting a client is a permanent action and cannot be undone.</p>
				<div class="card-actions justify-end">
					<RuneForm form={deleteClientForm}>
						{#snippet children(form)}
							<form
								method="POST"
								action="?/deleteClient"
								use:enhance={async ({ cancel }) => {
									const isConfirmed = await confirm({
										title: 'Are you sure?',
										message: `This will permanently delete ${client.first_name} ${client.last_name} and all associated data. This action cannot be undone.`,
										confirmText: 'Delete Client',
										cancelText: 'Cancel',
										confirmClass: 'btn-error'
									});
									if (!isConfirmed) {
										cancel();
									}
								}}
								onsubmit={form.handleSubmit()}
							>
								<button type="submit" class="btn btn-error">
									<IconDelete class="h-5 w-5" />
									Delete This Client
								</button>
							</form>
						{/snippet}
					</RuneForm>
				</div>
			</div>
		</div>
	{:else}
		<div class="card bg-base-200/70 shadow-lg">
			<div class="card-body">
				<h2 class="card-title text-2xl">Create New Client</h2>
				<RuneForm form={createClientForm}>
					{#snippet children(form)}
						<form
							method="POST"
							action="?/createClient"
							class="mt-4 space-y-4"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ update }) => {
									await update();
									form.setSubmitting(false);
								};
							}}
							onsubmit={form.handleSubmit()}
						>
							<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
								<Field name="first_name">
									{#snippet children(field)}
										<div class="form-control">
											<label for="first_name_create" class="label"
												><span class="label-text">First Name</span></label
											>
											<input
												id="first_name_create"
												name="first_name"
												type="text"
												class="input input-bordered"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											/>
											<Errors name="first_name" />
										</div>
									{/snippet}
								</Field>
								<Field name="last_name">
									{#snippet children(field)}
										<div class="form-control">
											<label for="last_name_create" class="label"
												><span class="label-text">Last Name</span></label
											>
											<input
												id="last_name_create"
												name="last_name"
												type="text"
												class="input input-bordered"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											/>
											<Errors name="last_name" />
										</div>
									{/snippet}
								</Field>
							</div>
							<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
								<Field name="email">
									{#snippet children(field)}
										<div class="form-control">
											<label for="email_create" class="label"
												><span class="label-text">Email</span></label
											>
											<input
												id="email_create"
												name="email"
												type="email"
												class="input input-bordered"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											/>
											<Errors name="email" />
										</div>
									{/snippet}
								</Field>
								<Field name="phone_number">
									{#snippet children(field)}
										<div class="form-control">
											<label for="phone_number_create" class="label"
												><span class="label-text">Phone Number</span></label
											>
											<input
												id="phone_number_create"
												name="phone_number"
												type="tel"
												class="input input-bordered"
												value={String(field.value ?? '')}
												oninput={field.handleChange}
												onblur={field.handleBlur}
											/>
											<Errors name="phone_number" />
										</div>
									{/snippet}
								</Field>
							</div>

							<!-- Image Upload Section -->
							<div class="space-y-4">
								<div class="flex items-center gap-3">
									<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
									<div>
										<h3 class="text-lg font-semibold text-base-content/90">Client Photo</h3>
										<p class="text-sm text-base-content/60">Upload a photo of your client</p>
									</div>
								</div>
								<div class="bg-base-100/80 border border-base-300/30 rounded-xl p-6 shadow-sm">
									<ImageUpload
										value={createClientForm.values.image_base64}
										onImageChange={handleCreateImageChange}
										maxWidth={1600}
										maxHeight={1200}
										quality={0.8}
										title="Add Client Photo"
										subtitle="Upload a file or take a photo"
									/>
								</div>
							</div>

							<!-- Hidden input for image data -->
							<input type="hidden" name="image_base64" bind:value={createClientForm.values.image_base64} />

							<div class="card-actions justify-end">
								<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
										Saving...
									{:else}
										<IconSave class="h-5 w-5" />
										Create Client
									{/if}
								</button>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			</div>
		</div>
	{/if}
</div>
