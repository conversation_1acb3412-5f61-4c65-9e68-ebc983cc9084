<!--
	@component
	Base component for settings modules. Provides a consistent interface and styling
	for different settings sections. Each settings module should extend this pattern.
-->
<script lang="ts">
	import type { Component, Snippet } from 'svelte';

	interface Props {
		title: string;
		description: string;
		icon: Component;
		children: Snippet;
	}

	let { title, description, icon: Icon, children }: Props = $props();
</script>

<!-- Settings Module -->
<div class="group border border-base-300/20 rounded-xl bg-gradient-to-br from-base-50/50 to-base-100/30 hover:border-base-300/40 transition-all duration-300">
	<div class="p-6">
		<!-- Module Header -->
		<div class="flex items-center gap-4 mb-6">
			<div class="w-10 h-10 bg-primary/5 border border-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/10 transition-colors duration-300">
				<Icon class="h-5 w-5 text-primary/70" />
			</div>
			<div>
				<h2 class="text-xl font-semibold text-base-content/90 mb-1">{title}</h2>
				<p class="text-base-content/60 text-sm">{description}</p>
			</div>
		</div>

		<!-- Module Content -->
		<div class="space-y-6">
			{@render children()}
		</div>
	</div>
</div>
