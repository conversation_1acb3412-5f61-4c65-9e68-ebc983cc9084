<!--
  - src/lib/components/actions/QuickActionFAB.svelte
  -
  - Purpose:
  - A Floating Action Button (FAB) for mobile devices that provides a quick way
  - to open the main command palette (QuickActionBar). It is only visible on
  - smaller screens to avoid cluttering the desktop interface.
  -->
<script lang="ts">
	import { toggleQuickActionBar } from '$lib/stores/quickActionBar';
	import IconSearch from '~icons/icon-park-outline/search';
</script>

<div class="fixed right-4 bottom-4 z-20 lg:hidden" data-testid="quick-action-fab">
	<button
		class="btn btn-primary btn-circle shadow-lg"
		onclick={toggleQuickActionBar}
		aria-label="Open command palette"
	>
		<IconSearch class="h-6 w-6" />
	</button>
</div>
