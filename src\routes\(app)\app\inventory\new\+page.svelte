<!--
  New Inventory Item Page
  Provides a form for users to add a new item to their inventory.
-->
<script lang="ts">
	import InventoryItemForm from '$lib/components/inventory/InventoryItemForm.svelte';
	import { SUCCESS_MESSAGES, ROUTES, TIMING } from '$lib/config';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { insertInventoryItemSchema } from '$lib/schemas/inventory';
	import { toast } from '$lib/ui/toast';

	const form = createRuneForm(insertInventoryItemSchema, {
		name: '',
		description: '',
		barcode: '',
		quantity: 0,
		price_in_cents: undefined,
		image_base64: '',
		cost_in_cents: 0,
		reorder_threshold: undefined,
		supplier: ''
	});

	function handleSuccess() {
		toast.success(SUCCESS_MESSAGES.INVENTORY_ITEM_CREATED);
		// Redirect after showing the toast
		setTimeout(() => {
			window.location.href = ROUTES.INVENTORY;
		}, TIMING.REDIRECT_DELAY);
	}
</script>

<!-- New Inventory Item Page -->
<div class="mx-auto w-full max-w-5xl space-y-6 pb-24">
	<!-- Page Header -->
	<div class="bg-base-100/80 backdrop-blur-sm border border-base-300/30 rounded-xl shadow-sm p-6">
		<div class="flex items-center gap-3 mb-2">
			<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
			<h1 class="text-2xl font-semibold text-base-content/90">Add New Inventory Item</h1>
		</div>
		<p class="text-base-content/60 ml-5">
			Fill out the details below to add a new item to your inventory
		</p>
	</div>

	<!-- Form -->
	<InventoryItemForm
		{form}
		action="?/create"
		submitLabel="Add Item"
		onSuccess={handleSuccess}
		isNewItem={true}
	/>
</div>
