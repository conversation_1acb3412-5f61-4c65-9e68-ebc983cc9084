import { toast } from '$lib/ui/toast';
import {
	env,
	pipeline,
	type PipelineType,
	type PretrainedOptions,
	type Text2TextGenerationPipeline,
	type TextGenerationConfig,
	type TextGenerationPipeline
} from '@xenova/transformers';
import { get, writable, type Writable } from 'svelte/store';

// Custom types to include properties not in the official definitions
interface ExtendedPretrainedOptions extends PretrainedOptions {
	device?: string;
	dtype?: string;
}

interface ExtendedTextGenerationConfig extends TextGenerationConfig {
	stream?: boolean;
}

interface ProgressCallbackData {
	status: string;
	name: string;
	file: string;
	progress: number;
	loaded: number;
	total: number;
}

// Skip local model check
env.allowLocalModels = false;

type WebLLMStatus = 'uninitialized' | 'loading' | 'ready' | 'error' | 'generating';

const MODEL_ID = 'Xenova/LaMini-Flan-T5-77M';
const TASK: PipelineType = 'text2text-generation';

const store: Writable<{
	status: WebLLMStatus;
	pipe: TextGenerationPipeline | Text2TextGenerationPipeline | null;
	progress: number;
	error: string | null;
}> = writable({
	status: 'uninitialized',
	pipe: null,
	progress: 0,
	error: null
});

async function initialize() {
	// Fallback to transformers.js
	store.update((s) => {
		s.status = 'loading';
		s.progress = 0;
		s.error = null;
		return s;
	});

	try {
		const options: ExtendedPretrainedOptions = {
			device: 'webgpu',
			dtype: 'q4f16',
			progress_callback: (p: ProgressCallbackData) => {
				// p is an object with the following properties:
				// { status: 'progress', name: '...', file: '...', progress: 82.2, loaded: 49242, total: 60000 }
				if (p.status === 'progress') {
					const progress = (p.loaded / p.total) * 100;
					store.update((s) => ({ ...s, progress }));
				}
			}
		};
		const generator = (await pipeline(TASK, MODEL_ID, options)) as
			| TextGenerationPipeline
			| Text2TextGenerationPipeline;

		store.update((s) => {
			s.status = 'ready';
			s.pipe = generator;
			s.progress = 100;
			return s;
		});

		toast.success('AI Ready');
	} catch (e) {
		const error = e instanceof Error ? e.message : String(e);
		console.error(e);
		store.update((s) => {
			s.status = 'error';
			s.error = error;
			return s;
		});
		toast.error(`Error loading AI Model: ${error}`);
	}
}

async function generate(prompt: string, opts: ExtendedTextGenerationConfig = {}) {
	let result = '';
	store.update((s) => {
		s.status = 'generating';
		return s;
	});

	try {
		const { pipe } = get(store);

		if (!pipe) {
			throw new Error('AI not initialized');
		}

		const generationOptions: ExtendedTextGenerationConfig = {
			...opts,
			max_new_tokens: 512,
			stream: true
		};
		const stream = await pipe(prompt, generationOptions);

		let fullResponse = '';
		for await (const chunk of stream as unknown as AsyncIterable<{ generated_text: string }>) {
			// The stream gives the full text so far, so we just need the last message.
			fullResponse = chunk.generated_text;
		}
		result = fullResponse;

		store.update((s) => {
			s.status = 'ready';
			return s;
		});

		return result;
	} catch (e) {
		const error = e instanceof Error ? e.message : String(e);
		console.error(e);
		store.update((s) => {
			s.status = 'error';
			s.error = error;
			return s;
		});
		toast.error(`Error during generation: ${error}`);
		return '';
	}
}

export const webllm = {
	subscribe: store.subscribe,
	initialize,
	generate
};
