// ALPHA FEATURE: Inventory Management - Main Page Server Load
// This server load function provided data for the main inventory page including
// fetching all inventory items for the authenticated user, ordered by name.
// Part of the inventory management system that is currently in alpha status.

import { db } from '$lib/server/db';
import { inventory_items } from '$lib/server/db/schema';
import { redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	if (!locals.user) throw redirect(302, '/login');

	const items = await db
		.select()
		.from(inventory_items)
		.where(eq(inventory_items.user_id, locals.user.id))
		.orderBy(inventory_items.name);

	return { items };
};
