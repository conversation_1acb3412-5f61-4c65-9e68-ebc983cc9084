<!--
ALPHA FEATURE: Inventory Management - Main Inventory Page

This page provided the main inventory management interface including:
- Grid view of all inventory items with status indicators
- Search and filtering capabilities
- Barcode scanning for quick item lookup
- Quick restock modal for streamlined restocking workflow
- Mobile-optimized responsive design
- Integration with inventory item detail pages

This page is part of the inventory management system that is currently in alpha status.
All functionality remains intact but is inaccessible via navigation.
-->

<script lang="ts">
	import BarcodeScanner from '$lib/components/barcode/BarcodeScanner.svelte';
	import InventoryCard from '$lib/components/inventory/InventoryCard.svelte';
	import QuickRestockModal from '$lib/components/inventory/QuickRestockModal.svelte';
	import { toast } from '$lib/ui/toast';
	import IconPlus from '~icons/icon-park-outline/plus';
	import IconScan from '~icons/icon-park-outline/scan';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	let searchTerm = $state('');
	let filteredItems = $state(data.items);
	let showBarcodeScanner = $state(false);
	let showQuickRestockModal = $state(false);

	$effect(() => {
		let filtered = data.items;

		// Apply search filter
		if (searchTerm) {
			const lowerCaseSearch = searchTerm.toLowerCase();
			filtered = filtered.filter(
				(item) =>
					item.name.toLowerCase().includes(lowerCaseSearch) ||
					item.description?.toLowerCase().includes(lowerCaseSearch) ||
					item.sku?.toLowerCase().includes(lowerCaseSearch) ||
					item.barcode?.toLowerCase().includes(lowerCaseSearch)
			);
		}

		filteredItems = filtered;
	});

	function handleBarcodeScanned(barcode: string) {
		searchTerm = barcode;
		showBarcodeScanner = false;
	}

	function openBarcodeScanner() {
		showBarcodeScanner = true;
	}

	function closeBarcodeScanner() {
		showBarcodeScanner = false;
	}

	function handleScannerError(error: string) {
		console.error('Barcode scanner error:', error);
		toast.error('Barcode scanning failed. Please try again.');
		showBarcodeScanner = false;
	}
</script>

<!-- Inventory Management Page -->
<div class="mx-auto w-full max-w-7xl space-y-6 pb-24">
	<!-- Page Header -->
	<div class="bg-base-100/80 backdrop-blur-sm border border-base-300/30 rounded-xl shadow-sm p-6">
		<div class="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between">
			<!-- Title Section -->
			<div class="flex items-center gap-3">
				<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
				<div>
					<h1 class="text-2xl font-semibold text-base-content/90">Inventory Management</h1>
					<p class="text-base-content/60 mt-1">Manage your products and stock levels</p>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex flex-wrap items-center gap-3">
				<!-- Update Mode Button -->
				<a
					href="/app/inventory/update-mode"
					class="btn btn-secondary transition-all duration-200 hover:shadow-md"
				>
					<IconScan class="h-4 w-4" />
					<span>Update Mode</span>
				</a>

				<!-- Quick Restock Button -->
				<button
					type="button"
					class="btn btn-accent transition-all duration-200 hover:shadow-md"
					onclick={() => showQuickRestockModal = true}
				>
					<IconScan class="h-4 w-4" />
					<span>Quick Restock</span>
				</button>
			</div>
		</div>

		<!-- Search Section -->
		<div class="mt-6 flex flex-col gap-4 sm:flex-row sm:items-center">
			<div class="flex-1 max-w-md">
				<div class="relative">
					<input
						type="search"
						placeholder="Search by name, description, or barcode..."
						class="input input-bordered w-full pr-12 transition-all duration-200 focus:border-primary/50 focus:ring-2 focus:ring-primary/10"
						bind:value={searchTerm}
					/>
					<button
						type="button"
						class="btn btn-ghost btn-sm absolute right-2 top-1/2 -translate-y-1/2 transition-colors duration-200 hover:bg-primary/5"
						onclick={openBarcodeScanner}
						aria-label="Scan barcode to search"
					>
						<IconScan class="h-4 w-4 text-primary" />
					</button>
				</div>
			</div>

			<!-- Add New Item Button -->
			<a
				href="/app/inventory/new"
				class="btn btn-primary transition-all duration-200 hover:shadow-md"
			>
				<IconPlus class="h-4 w-4" />
				<span>Add New Item</span>
			</a>
		</div>
	</div>

	<!-- Results Summary -->
	<div class="flex items-center justify-between p-4 bg-base-100/80 border border-base-300/30 rounded-xl shadow-sm">
		<div class="flex items-center gap-3">
			<div class="w-1.5 h-1.5 bg-primary rounded-full"></div>
			<div>
				<p class="font-medium text-base-content/90">
					{filteredItems.length} {filteredItems.length === 1 ? 'item' : 'items'} found
				</p>
				<p class="text-sm text-base-content/60">
					{searchTerm ? `Filtered from ${data.items.length} total items` : `Total inventory items`}
				</p>
			</div>
		</div>
		{#if searchTerm}
			<button
				class="btn btn-ghost btn-sm transition-colors duration-200 hover:bg-base-200/50"
				onclick={() => { searchTerm = ''; }}
			>
				Clear Filter
			</button>
		{/if}
	</div>

	{#if filteredItems.length > 0}
		<!-- Mobile-Optimized Item Grid -->
		<div class="grid grid-cols-2 gap-3 sm:grid-cols-2 sm:gap-4 lg:grid-cols-3 xl:grid-cols-4">
			{#each filteredItems as item (item.id)}
				<InventoryCard {item} />
			{/each}
		</div>
	{:else}
		<!-- Empty State -->
		<div class="card bg-base-100/80 border border-base-300/30 shadow-sm rounded-xl">
			<div class="card-body items-center text-center p-8">
				<div class="w-2 h-2 bg-primary/40 rounded-full mb-4"></div>

				<h2 class="text-xl font-semibold text-base-content/90 mb-2">
					{#if searchTerm}
						No items match your search
					{:else}
						Your inventory awaits
					{/if}
				</h2>

				{#if searchTerm}
					<p class="text-base-content/70 mb-4 max-w-md">
						We couldn't find any items matching "<strong>{searchTerm}</strong>".
						Try adjusting your search term or browse all items.
					</p>
					<div class="flex gap-3">
						<button
							class="btn btn-ghost transition-colors duration-200 hover:bg-base-200/50"
							onclick={() => { searchTerm = ''; }}
						>
							Clear Search
						</button>
						<a href="/app/inventory/new" class="btn btn-primary transition-all duration-200 hover:shadow-md">
							<IconPlus class="h-4 w-4" />
							Add New Item
						</a>
					</div>
				{:else}
					<p class="text-base-content/70 mb-4 max-w-md">
						Ready to start managing your inventory? Add your first item to get started.
					</p>
					<a href="/app/inventory/new" class="btn btn-primary transition-all duration-200 hover:shadow-md">
						<IconPlus class="h-4 w-4" />
						Add Your First Item
					</a>
				{/if}
			</div>
		</div>
	{/if}
</div>

<!-- Barcode Scanner Modal -->
{#if showBarcodeScanner}
	<BarcodeScanner
		onScan={handleBarcodeScanned}
		onClose={closeBarcodeScanner}
		onError={handleScannerError}
		isActive={true}
	/>
{/if}

<!-- Quick Restock Modal -->
<QuickRestockModal isOpen={showQuickRestockModal} onClose={() => showQuickRestockModal = false} />
