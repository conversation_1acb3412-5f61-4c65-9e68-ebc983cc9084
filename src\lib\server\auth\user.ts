import { db } from '$lib/server/db';
import { users, type User } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { hashPassword } from './password';

export function verifyFirstNameInput(firstName: string): boolean {
	// Enhanced validation for first name
	const trimmedFirstName = firstName.trim();
	
	// Check for basic requirements
	if (trimmedFirstName.length === 0 || trimmedFirstName.length > 100) {
		return false;
	}
	
	// Check for suspicious patterns (potential XSS/injection attempts)
	const suspiciousPatterns = [
		/<[^>]*>/g, // HTML tags
		/javascript:/i, // JavaScript protocol
		/data:/i, // Data URLs
		/vbscript:/i, // VBScript
		/on\w+\s*=/i // Event handlers
	];
	
	for (const pattern of suspiciousPatterns) {
		if (pattern.test(trimmedFirstName)) {
			return false;
		}
	}
	
	return true;
}

export function sanitizeFirstName(firstName: string): string {
	// Basic sanitization - trim and limit length
	return firstName.trim().slice(0, 100);
}

export async function createUser(
	email: string,
	firstName: string,
	password: string | null = null
): Promise<User> {
	// Validate and sanitize inputs
	if (!verifyFirstNameInput(firstName)) {
		throw new Error('Invalid first name');
	}
	
	const sanitizedFirstName = sanitizeFirstName(firstName);
	const hashedPassword = password ? await hashPassword(password) : null;

	const [user] = await db
		.insert(users)
		.values({
			email: email.toLowerCase().trim(), // Normalize email
			first_name: sanitizedFirstName,
			hashed_password: hashedPassword,
			email_verified: false
		})
		.returning();

	return user;
}

export async function updateUserPassword(userId: string, password: string): Promise<void> {
	const newPasswordHash = await hashPassword(password);
	await db
		.update(users)
		.set({ hashed_password: newPasswordHash, updated_at: new Date() })
		.where(eq(users.id, userId));
}

export async function updateUserEmail(userId: string, email: string): Promise<void> {
	const normalizedEmail = email.toLowerCase().trim();
	await db.update(users).set({ email: normalizedEmail }).where(eq(users.id, userId));
}

export async function getUserPasswordHash(userId: string): Promise<string | null> {
	const user = await db.query.users.findFirst({
		columns: { hashed_password: true },
		where: eq(users.id, userId)
	});
	return user?.hashed_password || null;
}

export async function getUserFromEmail(email: string): Promise<User | null> {
	// Normalize email for consistent lookups
	const normalizedEmail = email.toLowerCase().trim();
	
	const result = await db.select().from(users).where(eq(users.email, normalizedEmail)).limit(1);

	if (result.length === 0 || !result[0]) {
		return null;
	}
	return result[0];
}

export async function setUserEmailAsVerified(userId: string): Promise<void> {
	await db
		.update(users)
		.set({ email_verified: true, updated_at: new Date() })
		.where(eq(users.id, userId));
}

export async function checkUserAuthMethods(email: string) {
	try {
		const normalizedEmail = email.toLowerCase().trim();
		
		const foundUser = await db.query.users.findFirst({
			where: eq(users.email, normalizedEmail),
			with: {
				user_oauth_accounts: {
					columns: {
						provider: true
					}
				},
				webauthn_credentials: {
					columns: {
						id: true
					}
				}
			}
		});

		if (!foundUser) {
			return { userExists: false, methods: [] };
		}

		const methods = [];
		if (foundUser.hashed_password) {
			methods.push({ type: 'password' });
		}
		if (foundUser.webauthn_credentials.length > 0) {
			methods.push({ type: 'passkey' });
		}
		for (const account of foundUser.user_oauth_accounts) {
			methods.push({ type: 'oauth', provider: account.provider });
		}

		return {
			userExists: true,
			methods
		};
	} catch (error) {
		console.error('Error during auth lookup:', error);
		throw new Error('Internal server error during auth lookup');
	}
}
