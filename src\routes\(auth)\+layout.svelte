<!--
This layout is for unauthenticated users (e.g., login, signup pages).
It provides content for the 'navbar_end' snippet defined in the root layout (src/routes/+layout.svelte).
It does not provide a 'sidebar' snippet, so the sidebar will be empty or hidden for these routes.
-->
<script lang="ts">
	import ConfirmationModal from '$lib/components/ConfirmationModal.svelte';
	import WeatherEffects from '$lib/components/WeatherEffects.svelte';
	import type { Snippet } from 'svelte';

	let { children }: { children: Snippet } = $props();
</script>

{@render children()}

<ConfirmationModal />
<WeatherEffects />
