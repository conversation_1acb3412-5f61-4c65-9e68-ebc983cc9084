import type { FormField, TemplateDefinition } from '$lib/schemas/template';

export const sampleTemplate: {
	name: string;
	description: string;
	template_definition: TemplateDefinition;
} = {
	name: 'Sample Client Follow-up',
	description: 'A template for tracking follow-up communication with clients.',
	template_definition: {
		fields: [
			{
				id: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
				name: 'followUpDate',
				label: 'Follow-up Date',
				type: 'date',
				required: true
			},
			{
				id: 'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
				name: 'communicationMethod',
				label: 'Communication Method',
				type: 'select',
				required: false,
				options: [
					{ label: 'Email', value: 'email' },
					{ label: 'Phone Call', value: 'phone' },
					{ label: 'SMS/Text', value: 'sms' }
				]
			},
			{
				id: 'c3d4e5f6-a7b8-9012-3456-7890abcdef12',
				name: 'followUpNotes',
				label: 'Notes',
				type: 'textarea',
				required: true,
				placeholder: 'e.g., Sent a check-in email about their new color...'
			}
		] as FormField[]
	}
};

export const sampleClient = {
	firstName: 'Alex',
	lastName: 'Sample',
	email: '<EMAIL>',
	phone: '555-0199',
	notes: 'A sample client for testing purposes.'
};
