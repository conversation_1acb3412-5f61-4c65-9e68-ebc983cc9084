-- Add indexes for better shopping list and inventory performance

-- Index for shopping list items by user and purchased status
CREATE INDEX IF NOT EXISTS "shopping_list_items_user_purchased_idx" ON "shopping_list_items" ("user_id", "purchased");

-- Index for shopping list items by user and inventory item
CREATE INDEX IF NOT EXISTS "shopping_list_items_user_inventory_idx" ON "shopping_list_items" ("user_id", "inventory_item_id");

-- Index for inventory items by user and reorder threshold (for reorder suggestions)
CREATE INDEX IF NOT EXISTS "inventory_items_user_reorder_idx" ON "inventory_items" ("user_id", "reorder_threshold") WHERE "reorder_threshold" IS NOT NULL;

-- Index for inventory items by user and quantity (for low stock queries)
CREATE INDEX IF NOT EXISTS "inventory_items_user_quantity_idx" ON "inventory_items" ("user_id", "quantity");

-- Composite index for shopping list items ordering
CREATE INDEX IF NOT EXISTS "shopping_list_items_user_order_idx" ON "shopping_list_items" ("user_id", "purchased", "created_at");
