/**
 * Utility functions for barcode operations
 */

export interface InventoryItem {
	id: string;
	name: string;
	description?: string;
	sku?: string;
	barcode?: string;
	quantity: number;
	price_in_cents?: number;
	cost_in_cents: number;
	reorder_threshold?: number;
	supplier?: string;
	image_base64?: string;
}

/**
 * Search for an inventory item by barcode
 */
export async function searchByBarcode(barcode: string): Promise<InventoryItem | null> {
	if (!barcode.trim()) {
		return null;
	}

	try {
		const response = await fetch(`/api/inventory/search?barcode=${encodeURIComponent(barcode)}`);
		
		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const data = await response.json();
		
		if (data.error) {
			throw new Error(data.error);
		}

		return data.items && data.items.length > 0 ? data.items[0] : null;
	} catch (error) {
		console.error('Error searching by barcode:', error);
		return null;
	}
}

/**
 * Search for inventory items by general query
 */
export async function searchInventoryItems(query: string): Promise<InventoryItem[]> {
	if (!query.trim()) {
		return [];
	}

	try {
		const response = await fetch(`/api/inventory/search?q=${encodeURIComponent(query)}`);
		
		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const data = await response.json();
		
		if (data.error) {
			throw new Error(data.error);
		}

		return data.items || [];
	} catch (error) {
		console.error('Error searching inventory items:', error);
		return [];
	}
}

/**
 * Check if a barcode already exists in the inventory
 */
export async function checkBarcodeExists(barcode: string): Promise<boolean> {
	const item = await searchByBarcode(barcode);
	return item !== null;
}

/**
 * Validate barcode format (basic validation)
 */
export function validateBarcode(barcode: string): boolean {
	if (!barcode || typeof barcode !== 'string') {
		return false;
	}

	// Remove whitespace
	const cleanBarcode = barcode.trim();
	
	// Check if it's not empty and contains only alphanumeric characters and common barcode symbols
	if (cleanBarcode.length === 0) {
		return false;
	}

	// Basic pattern for common barcode formats (UPC, EAN, Code128, etc.)
	// This is a simple validation - in production you might want more specific validation
	const barcodePattern = /^[0-9A-Za-z\-_]+$/;
	
	return barcodePattern.test(cleanBarcode) && cleanBarcode.length >= 4 && cleanBarcode.length <= 50;
}

/**
 * Format barcode for display
 */
export function formatBarcode(barcode: string): string {
	if (!barcode) return '';
	return barcode.trim().toUpperCase();
}
