import { env } from '$env/dynamic/private';

export const RP_NAME = 'Hairloom';

if (!env.WEBAUTHN_RP_ID || !env.WEBAUTHN_ORIGIN) {
	// If you are not using WebAuthn, you can ignore this error.
	console.warn(
		'WebAuthn environment variables (WEBAUTHN_RP_ID, WEBAUTHN_ORIGIN) are not set. Passkey login will not be available.'
	);
}

export const RP_ID = env.WEBAUTHN_RP_ID || 'thehairloom.app';

export const ORIGIN = env.WEBAUTHN_ORIGIN
	? env.WEBAUTHN_ORIGIN.split(',').map((o) => o.trim())
	: ['https://preview.thehairloom.app', 'https://thehairloom.app', 'https://www.thehairloom.app'];

export const EXPECTED_ORIGIN = ORIGIN;
