import type { FormField, TemplateDefinition } from '$lib/schemas/template';
import { get, writable } from 'svelte/store';

export const createTemplateEditorStore = (initialDefinition: TemplateDefinition) => {
	const store = writable(initialDefinition);
	const { subscribe, set, update } = store;

	const findField = (id: string): FormField | undefined => {
		const definition = get(store);
		return definition.fields.find((f) => f.id === id);
	};

	return {
		subscribe,
		set,
		update,
		findField
	};
};
