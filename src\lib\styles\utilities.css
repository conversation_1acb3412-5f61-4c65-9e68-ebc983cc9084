/**
 * Custom CSS Utilities for Hairloom CRM
 * Provides custom styling that cannot be achieved with utility classes alone
 * Following the refined, coy, and playful aesthetic guidelines
 *
 * Note: This file avoids @apply directives for Tailwind CSS v4 compatibility.
 * Most styling should use utility classes directly in HTML instead of these custom classes.
 * Only include custom CSS here that cannot be achieved with standard utility classes.
 */

/* ===== CUSTOM ANIMATIONS AND EFFECTS ===== */

/* Note: .btn-playful is defined in animations.css to avoid duplication */

/* ===== CARD UTILITIES ===== */

/* Card with gradient border accent - Custom effect that cannot be achieved with utility classes */
.card-gradient-accent {
  position: relative;
  background: linear-gradient(var(--color-base-100), var(--color-base-100)) padding-box,
              linear-gradient(135deg, var(--color-primary), var(--color-secondary)) border-box;
  border: 2px solid transparent;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

/* ===== LOADING ANIMATIONS ===== */

/* Pulse animation for loading states */
.pulse-loading {
  animation: pulse-loading 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-loading {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Shimmer effect for skeleton loading */
.shimmer {
  background: linear-gradient(90deg,
    var(--color-base-200) 25%,
    var(--color-base-300) 50%,
    var(--color-base-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ===== ACCESSIBILITY UTILITIES ===== */

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible styling for better accessibility */
.focus-visible-enhanced:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary);
  border-radius: 0.25rem;
}

/* ===== HOVER EFFECTS ===== */

/* Glow effect on hover */
.hover-glow {
  transition: box-shadow 200ms ease;
}

.hover-glow:hover {
  box-shadow: 0 10px 25px -5px rgba(var(--color-primary) / 0.2);
}

/* ===== HIGH CONTRAST MODE SUPPORT ===== */

@media (prefers-contrast: high) {
  .card-gradient-accent {
    border: 2px solid var(--color-base-content);
    background: var(--color-base-100);
  }

  .btn-playful::before {
    background-color: var(--color-base-content);
    opacity: 0.1;
  }
}

/* ===== REDUCED MOTION SUPPORT ===== */

@media (prefers-reduced-motion: reduce) {
  .btn-playful,
  .btn-playful::before,
  .pulse-loading,
  .shimmer,
  .hover-glow {
    animation: none;
    transition: none;
  }
}
