import { db } from '$lib/server/db';
import { migrateDatabase } from '$lib/server/db/migrate';
import { resetDatabase } from '$lib/server/db/reset';
import { seedDatabase } from '$lib/server/db/seed/seed';
import { seedSystemTemplates } from '$lib/server/db/seed/system-templates';
import { error, json, type RequestHandler } from '@sveltejs/kit';

export const POST: RequestHandler = async ({ locals }) => {
	// IMPORTANT: This is a destructive operation and should be protected.
	// We're checking for an authenticated user, but you might want to add
	// role-based access control to ensure only admins can run this.
	if (!locals.user) {
		throw error(401, 'Unauthorized');
	}

	// Additional check for non-production environments
	const vercelEnv = process.env.VERCEL_ENV;
	const nodeEnv = process.env.NODE_ENV;

	if (vercelEnv === 'production' || nodeEnv === 'production') {
		console.error('ERROR: Attempted to run reseed endpoint in production. Aborting.');
		throw error(403, 'This action is not allowed in production.');
	}

	try {
		console.log('--- STARTING DATABASE RESEED VIA API ---');

		// 1. Reset the database (drops all tables)
		await resetDatabase();

		// 2. Run migrations (re-creates tables)
		await migrateDatabase();

		// 3. Seed system templates
		await seedSystemTemplates(db);

		// 4. Seed development data
		await seedDatabase();

		console.log('--- DATABASE RESEED VIA API COMPLETE ---');

		return json({ success: true, message: 'Database reseeded successfully.' });
	} catch (e: unknown) {
		console.error('Failed to reseed database via API:', e);
		const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
		throw error(500, `Database reseeding failed: ${errorMessage}`);
	}
};
