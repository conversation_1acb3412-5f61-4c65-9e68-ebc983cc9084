<!--
	@component
	Refined button component that follows the coy/playful/refined aesthetic
-->
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		variant?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';
		size?: 'xs' | 'sm' | 'md' | 'lg';
		type?: 'button' | 'submit' | 'reset';
		disabled?: boolean;
		loading?: boolean;
		class?: string;
		href?: string;
		children: Snippet;
		onclick?: () => void;
	}

	let { 
		variant = 'primary', 
		size = 'md', 
		type = 'button', 
		disabled = false, 
		loading = false, 
		class: className = '',
		href,
		children,
		onclick,
		...restProps 
	}: Props = $props();

	const baseClasses = 'btn transition-all duration-200 hover:shadow-md';
	const variantClasses = {
		primary: 'btn-primary',
		secondary: 'btn-secondary', 
		accent: 'btn-accent',
		success: 'btn-success',
		warning: 'btn-warning',
		error: 'btn-error',
		ghost: 'btn-ghost',
		outline: 'btn-outline'
	};
	const sizeClasses = {
		xs: 'btn-xs',
		sm: 'btn-sm',
		md: '',
		lg: 'btn-lg'
	};

	const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
</script>

{#if href}
	<a 
		{href} 
		class={classes}
		class:btn-disabled={disabled}
		{...restProps}
	>
		{#if loading}
			<span class="loading loading-spinner loading-sm"></span>
		{/if}
		{@render children()}
	</a>
{:else}
	<button 
		{type}
		class={classes}
		{disabled}
		{onclick}
		{...restProps}
	>
		{#if loading}
			<span class="loading loading-spinner loading-sm"></span>
		{/if}
		{@render children()}
	</button>
{/if}
