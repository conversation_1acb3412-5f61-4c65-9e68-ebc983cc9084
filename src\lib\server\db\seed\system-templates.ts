import type { TemplateDefinition } from '$lib/schemas/template';
import type { Template } from '$lib/server/db/schema';
import * as schema from '$lib/server/db/schema';
import { templateSelectSchema } from '$lib/server/db/zodSchemas';
import { eq, type SQL } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import type { VercelPgDatabase } from 'drizzle-orm/vercel-postgres';
import type { z } from 'zod/v4';

type Db = PostgresJsDatabase<typeof schema> | VercelPgDatabase<typeof schema>;
type SelectTemplate = z.infer<typeof templateSelectSchema>;

interface SystemTemplate {
	name: string;
	description: string;
	template_definition: TemplateDefinition;
	special_marker: string;
	status?: 'draft' | 'active' | 'archived';
}

const systemTemplates: SystemTemplate[] = [
	{
		name: 'Plain Text Note',
		description: 'A simple, general-purpose note with a single text area for quick client updates.',
		status: 'active',
		special_marker: 'plain_text_note',
		template_definition: {
			fields: [
				{
					id: 'd0e1f2a3-b4c5-6789-0123-456789abcdef',
					name: 'content',
					label: 'Note',
					type: 'textarea',
					required: true,
					placeholder: 'Enter your note here...'
				}
			]
		}
	},
	{
		name: 'New Client Consultation',
		description: 'A comprehensive form to capture all necessary details for a new client visit.',
		status: 'active',
		special_marker: 'new_client_consultation',
		template_definition: {
			fields: [
				{
					id: 'e1f2a3b4-c5d6-7890-1234-567890abcdef0',
					name: 'hairHistory',
					label: 'Hair History',
					type: 'textarea',
					required: true,
					placeholder: 'e.g., previous color treatments, chemical services, major haircuts'
				},
				{
					id: 'f2a3b4c5-d6e7-8901-2345-67890abcdef01',
					name: 'hairGoals',
					label: 'Hair Goals',
					type: 'textarea',
					required: true,
					placeholder: 'e.g., looking for lower maintenance, want to go blonde, desire more volume'
				},
				{
					id: 'a3b4c5d6-e7f8-9012-3456-7890abcdef012',
					name: 'allergiesSensitivities',
					label: 'Allergies/Sensitivities',
					type: 'textarea',
					required: false,
					placeholder: 'e.g., sensitive scalp, allergy to PPD, fragrance sensitivity'
				},
				{
					id: 'b4c5d6e7-f8a9-0123-4567-890abcdef0123',
					name: 'lifestyle',
					label: 'Lifestyle',
					type: 'text',
					required: false,
					placeholder: 'e.g., active, professional, low-maintenance'
				},
				{
					id: 'c5d6e7f8-a9b0-1234-5678-90abcdef01234',
					name: 'hairTexture',
					label: 'Hair Texture',
					type: 'select',
					required: false,
					options: [
						{ label: 'Fine', value: 'fine' },
						{ label: 'Medium', value: 'medium' },
						{ label: 'Coarse', value: 'coarse' }
					]
				},
				{
					id: 'd6e7f8a9-b0c1-2345-6789-0abcdef012345',
					name: 'hairDensity',
					label: 'Hair Density',
					type: 'select',
					required: false,
					options: [
						{ label: 'Thin', value: 'thin' },
						{ label: 'Medium', value: 'medium' },
						{ label: 'Thick', value: 'thick' }
					]
				}
			]
		}
	},
	{
		name: 'Color Formula',
		description: "Record a client's specific hair color formula and application details.",
		status: 'active',
		special_marker: 'color_formula',
		template_definition: {
			fields: [
				{
					id: 'e7f8a9b0-c1d2-3456-7890-1234567890ab',
					name: 'colorLineUsed',
					label: 'Color Line Used',
					type: 'text',
					required: true,
					placeholder: 'e.g., Wella Koleston Perfect, Redken Shades EQ'
				},
				{
					id: 'f8a9b0c1-d2e3-4567-8901-234567890abc',
					name: 'baseColorFormula',
					label: 'Base Color Formula',
					type: 'text',
					required: true
				},
				{
					id: 'a9b0c1d2-e3f4-5678-9012-34567890abcd',
					name: 'highlightsLowlightsFormula',
					label: 'Highlights/Lowlights Formula',
					type: 'text',
					required: false
				},
				{
					id: 'b0c1d2e3-f4a5-6789-0123-4567890abcde',
					name: 'tonerGlossFormula',
					label: 'Toner/Gloss Formula',
					type: 'text',
					required: false
				},
				{
					id: 'c1d2e3f4-a5b6-7890-1234-567890abcdef',
					name: 'developerVolume',
					label: 'Developer Volume',
					type: 'select',
					required: true,
					options: [
						{ label: '5 vol', value: '5' },
						{ label: '10 vol', value: '10' },
						{ label: '15 vol', value: '15' },
						{ label: '20 vol', value: '20' },
						{ label: '25 vol', value: '25' },
						{ label: '30 vol', value: '30' },
						{ label: '40 vol', value: '40' }
					]
				},
				{
					id: 'd2e3f4a5-b6c7-8901-2345-67890abcdef0',
					name: 'processingTime',
					label: 'Processing Time (mins)',
					type: 'number',
					required: true
				},
				{
					id: 'e3f4a5b6-c7d8-9012-3456-7890abcdef01',
					name: 'applicationNotes',
					label: 'Application Notes',
					type: 'textarea',
					required: false,
					placeholder: 'e.g., Root smudge technique, babylights at the crown...'
				}
			]
		}
	},
	{
		name: 'Haircut Details',
		description: "Log the specific details of a client's haircut and styling.",
		status: 'active',
		special_marker: 'haircut_details',
		template_definition: {
			fields: [
				{
					id: 'f4a5b6c7-d8e9-0123-4567-890abcdef012',
					name: 'lengthRemoved',
					label: 'Length Removed',
					type: 'text',
					required: false,
					placeholder: 'e.g., 2 inches, a trim'
				},
				{
					id: 'a5b6c7d8-e9f0-1234-5678-90abcdef0123',
					name: 'techniqueUsed',
					label: 'Technique Used',
					type: 'text',
					required: true,
					placeholder: 'e.g., layers, graduation, texturizing, blunt cut'
				},
				{
					id: 'b6c7d8e9-f0a1-2345-6789-0abcdef01234',
					name: 'toolsUsed',
					label: 'Tools Used',
					type: 'text',
					required: false,
					placeholder: 'e.g., shears, thinning shears, razor'
				},
				{
					id: 'c7d8e9f0-a1b2-3456-7890-1234567890ab',
					name: 'stylingNotes',
					label: 'Styling Notes',
					type: 'textarea',
					required: false,
					placeholder: 'e.g., round brush blowout, diffused for natural curls...'
				}
			]
		}
	},
	{
		name: 'Chemical Service',
		description: 'Record details of a chemical service like a perm, relaxer, or keratin treatment.',
		status: 'active',
		special_marker: 'chemical_service',
		template_definition: {
			fields: [
				{
					id: 'd8e9f0a1-b2c3-4567-8901-234567890abc',
					name: 'serviceType',
					label: 'Service Type',
					type: 'select',
					required: true,
					options: [
						{ label: 'Perm', value: 'perm' },
						{ label: 'Relaxer', value: 'relaxer' },
						{ label: 'Keratin Treatment', value: 'keratin' },
						{ label: 'Other', value: 'other' }
					]
				},
				{
					id: 'e9f0a1b2-c3d4-5678-9012-34567890abcd',
					name: 'productUsed',
					label: 'Product/Brand Used',
					type: 'text',
					required: true
				},
				{
					id: 'f0a1b2c3-d4e5-6789-0123-4567890abcde',
					name: 'rodSizeDetails',
					label: 'Rod Size/Details (for perms)',
					type: 'text',
					required: false
				},
				{
					id: 'a1b2c3d4-e5f6-7890-1234-567890abcdea',
					name: 'processingTime',
					label: 'Processing Time (mins)',
					type: 'number',
					required: true
				},
				{
					id: 'b2c3d4e5-f6a7-8901-2345-67890abcdef0b',
					name: 'serviceNotes',
					label: 'Notes',
					type: 'textarea',
					required: false,
					placeholder: 'e.g., Client has resistant hair, used a pre-treatment...'
				}
			]
		}
	},
	{
		name: 'Client Feedback',
		description:
			'Log feedback received from the client after their appointment to track satisfaction.',
		status: 'active',
		special_marker: 'client_feedback',
		template_definition: {
			fields: [
				{
					id: 'd9e0f1a2-b3c4-5678-9012-34567890abcd',
					name: 'feedbackDate',
					label: 'Date of Feedback',
					type: 'date',
					required: true
				},
				{
					id: 'e0f1a2b3-c4d5-6789-0123-4567890abcde',
					name: 'feedbackSummary',
					label: 'Feedback Summary',
					type: 'textarea',
					required: true,
					placeholder: 'e.g., Loves the color, mentioned hair feels healthier...'
				},
				{
					id: 'f1a2b3c4-d5e6-7890-1234-567890abcdef',
					name: 'satisfactionLevel',
					label: 'Satisfaction Level',
					type: 'select',
					required: false,
					options: [
						{ label: 'Very Satisfied', value: '5' },
						{ label: 'Satisfied', value: '4' },
						{ label: 'Neutral', value: '3' },
						{ label: 'Dissatisfied', value: '2' },
						{ label: 'Very Dissatisfied', value: '1' }
					]
				}
			]
		}
	}
];

export async function seedSystemTemplates(db: Db) {
	console.log('Seeding system templates...');

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const queryInterface = db.query.templates as any;

	const existingTemplates: SelectTemplate[] = await queryInterface.findMany({
		where: (table: { special_marker: SQL<unknown> }, { isNotNull }: typeof import('drizzle-orm')) =>
			isNotNull(table.special_marker)
	});

	for (const template of systemTemplates) {
		const existing = existingTemplates.find((t) => t.special_marker === template.special_marker);

		if (existing) {
			console.log(`Updating system template: ${template.name}`);
			await db
				.update(schema.templates)
				.set({
					name: template.name,
					description: template.description,
					template_definition: template.template_definition,
					status: template.status || 'draft',
					updated_at: new Date()
				})
				.where(eq(schema.templates.id, existing.id));
		} else {
			console.log(`Creating system template: ${template.name}`);
			await db.insert(schema.templates).values({
				...template,
				created_by: 'system',
				status: template.status || 'draft'
			} as unknown as Template);
		}
	}

	console.log('System templates seeding complete.');
}
