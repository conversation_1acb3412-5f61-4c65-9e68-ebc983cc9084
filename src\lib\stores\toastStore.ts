import { getToastDuration } from '$lib/config';
import { writable } from 'svelte/store';

export type ToastMessage = {
	id: number;
	message: string;
	type: 'success' | 'error' | 'info' | 'warning' | 'loading';
	duration: number;
	timestamp: Date;
};

function createToastStore() {
	const activeToasts = writable<ToastMessage[]>([]);
	const history = writable<ToastMessage[]>([]);
	let nextId = 0;

	function add(message: string, type: ToastMessage['type'] = 'info', duration?: number): number {
		const id = nextId++;
		const actualDuration = duration ?? getToastDuration(type);
		const newToast: ToastMessage = { id, message, type, duration: actualDuration, timestamp: new Date() };

		activeToasts.update((toasts) => [...toasts, newToast]);
		history.update((h) => [newToast, ...h]);

		if (actualDuration > 0) {
			setTimeout(() => remove(id), actualDuration);
		}
		return id;
	}

	function remove(id: number) {
		activeToasts.update((toasts) => toasts.filter((t) => t.id !== id));
	}

	function removeFromHistory(id: number) {
		history.update((h) => h.filter((t) => t.id !== id));
		// Also remove from active toasts in case it's still visible
		remove(id);
	}

	function clearHistory() {
		history.set([]);
	}

	function update(id: number, newToastData: Partial<Omit<ToastMessage, 'id'>>) {
		const fullUpdateData = { ...newToastData, timestamp: new Date() };
		activeToasts.update((toasts) =>
			toasts.map((t) => (t.id === id ? { ...t, ...fullUpdateData } : t))
		);
		history.update((h) => h.map((t) => (t.id === id ? { ...t, ...fullUpdateData } : t)));
	}

	return {
		subscribe: activeToasts.subscribe,
		history: {
			subscribe: history.subscribe
		},
		add,
		remove,
		removeFromHistory,
		clearHistory,
		update,
		success: (message: string, duration?: number) => add(message, 'success', duration),
		error: (message: string, duration?: number) => add(message, 'error', duration),
		info: (message: string, duration?: number) => add(message, 'info', duration),
		warning: (message: string, duration?: number) => add(message, 'warning', duration),
		loading: (message: string) => add(message, 'loading', 0) // duration 0 means it won't auto-dismiss
	};
}

export const toastStore = createToastStore();
