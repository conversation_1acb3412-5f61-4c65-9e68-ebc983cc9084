<!--
  - src/lib/components/templates/DynamicFormRenderer.svelte
  -
  - Purpose:
  - Renders a functional HTML form based on a given template definition. This component
  - translates the JSON structure of a template into actual, interactive form fields.
  -
  - Key Features:
  - - Accepts a `template` object and a bindable `formData` object.
  - - Iterates through the fields defined in `template.template_definition`.
  - - Dynamically creates the correct input element for each field type (text, select, checkbox, etc.).
  - - Binds each form element to a property on the `formData` object.
  - - Applies DaisyUI classes for consistent styling.
  -
  - Integration:
  - This is a highly reusable component. It's used by:
  - - `src/routes/(app)/app/templates/[id]/preview/+page.svelte` to show a live preview of a template.
  - - `src/lib/components/clients/AddClientNoteForm.svelte` to render a form when a user selects a template for a note.
-->
<script lang="ts">
	import type { FormTemplate } from '$lib/server/db/zodSchemas';

	let {
		template,
		formData = $bindable()
	}: {
		template: FormTemplate;
		formData: Record<string, any>;
	} = $props();
</script>

<div class="space-y-8">
	{#each template.template_definition.fields as field (field.id)}
		<div class="w-full">
			{#if field.type === 'textarea'}
				<div class="form-control group">
					<label class="label" for={field.id}>
						<span class="label-text font-semibold text-base-content/90 flex items-center gap-2">
							📝 {field.label}
							{#if field.required}
								<span class="badge badge-error badge-xs">Required</span>
							{:else}
								<span class="badge badge-ghost badge-xs">Optional</span>
							{/if}
						</span>
					</label>
					<div class="relative">
						<textarea
							id={field.id}
							name={field.name}
							class="textarea textarea-bordered textarea-lg w-full h-32 transition-all duration-300 focus:textarea-primary focus:scale-[1.02] group-hover:border-primary/50 resize-none"
							placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}...`}
							required={field.required}
							bind:value={formData[field.name]}
						></textarea>
						<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
					</div>
				</div>
			{:else if field.type === 'select'}
				<div class="form-control group">
					<label class="label" for={field.id}>
						<span class="label-text font-semibold text-base-content/90 flex items-center gap-2">
							🎯 {field.label}
							{#if field.required}
								<span class="badge badge-error badge-xs">Required</span>
							{:else}
								<span class="badge badge-ghost badge-xs">Optional</span>
							{/if}
						</span>
					</label>
					<div class="relative">
						<select
							id={field.id}
							name={field.name}
							class="select select-bordered select-lg w-full transition-all duration-300 focus:select-primary focus:scale-[1.02] group-hover:border-primary/50"
							required={field.required}
							bind:value={formData[field.name]}
						>
							{#if field.placeholder}
								<option disabled selected value="">{field.placeholder}</option>
							{:else}
								<option value="" disabled selected>Choose {field.label.toLowerCase()}...</option>
							{/if}
							{#each field.options || [] as option (option.value)}
								<option value={option.value}>{option.label}</option>
							{/each}
						</select>
						<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
					</div>
				</div>
			{:else if field.type === 'checkbox'}
				<div class="form-control group">
					<label for={field.id} class="label cursor-pointer justify-start gap-4 transition-all duration-300 hover:bg-base-200/50 rounded-lg p-3">
						<input
							id={field.id}
							name={field.name}
							type="checkbox"
							class="checkbox checkbox-primary transition-all duration-300 hover:scale-110"
							required={field.required}
							bind:checked={formData[field.name]}
						/>
						<span class="label-text font-semibold text-base-content/90 flex items-center gap-2">
							✅ {field.label}
							{#if field.required}
								<span class="badge badge-error badge-xs">Required</span>
							{/if}
						</span>
					</label>
				</div>
			{:else if field.type === 'radio'}
				<div class="form-control group">
					<div class="label">
						<span class="label-text font-semibold text-base-content/90 flex items-center gap-2">
							🎯 {field.label}
							{#if field.required}
								<span class="badge badge-error badge-xs">Required</span>
							{:else}
								<span class="badge badge-ghost badge-xs">Optional</span>
							{/if}
						</span>
					</div>
					<div class="flex flex-wrap gap-4 p-4 bg-base-200/30 rounded-lg border border-base-300/20 transition-all duration-300 group-hover:bg-base-200/50">
						{#each field.options || [] as option (option.value)}
							<label class="flex cursor-pointer items-center gap-3 p-3 rounded-lg transition-all duration-300 hover:bg-primary/10 hover:scale-105">
								<input
									type="radio"
									name={field.name}
									value={option.value}
									class="radio radio-primary transition-all duration-300"
									required={field.required}
									bind:group={formData[field.name]}
								/>
								<span class="label-text font-medium">{option.label}</span>
							</label>
						{/each}
					</div>
				</div>
			{:else}
				<div class="form-control group">
					<label class="label" for={field.id}>
						<span class="label-text font-semibold text-base-content/90 flex items-center gap-2">
							📝 {field.label}
							{#if field.required}
								<span class="badge badge-error badge-xs">Required</span>
							{:else}
								<span class="badge badge-ghost badge-xs">Optional</span>
							{/if}
						</span>
					</label>
					<div class="relative">
						<input
							id={field.id}
							name={field.name}
							type={field.type}
							class="input input-bordered input-lg w-full transition-all duration-300 focus:input-primary focus:scale-[1.02] group-hover:border-primary/50"
							placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}...`}
							required={field.required}
							bind:value={formData[field.name]}
						/>
						<div class="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
					</div>
				</div>
			{/if}
		</div>
	{/each}
</div>
