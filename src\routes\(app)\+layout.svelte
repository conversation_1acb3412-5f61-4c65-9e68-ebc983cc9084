<!--
- src/routes/(app)/+layout.svelte
-
- Purpose:
- This is the primary layout for the authenticated sections of the application, using a
- DaisyUI drawer component for navigation. It wraps all pages within the `/(app)` group.
-
- Key Features:
- - Sets up the main visual structure with a responsive sidebar and a top navbar.
- - Provides consistent navigation links in the sidebar.
- - The header contains user information, notifications, a theme controller, and a user menu.
- - Initializes global components like `ToastNotifications`, `ConfirmationModal`, and the new `QuickActionBar`.
- - Wires up the `Ctrl+K` shortcut to toggle the `QuickActionBar` component.
-
- Structure:
- - A DaisyUI `drawer` component manages the responsive sidebar.
- - A `navbar` component serves as the top header.
- - Global components are placed at the end of the main content container.
- - The `{@render children()}` snippet renders the content of individual pages.
-->
<script lang="ts">
	import { page } from '$app/state';
	import QuickActionBar from '$lib/components/actions/QuickActionBar.svelte';
	import QuickActionFAB from '$lib/components/actions/QuickActionFAB.svelte';
	import ConfirmationModal from '$lib/components/ConfirmationModal.svelte';
	import NotificationHistory from '$lib/components/NotificationHistory.svelte';
	import ThemeController from '$lib/components/ThemeController.svelte';
	import Dropdown from '$lib/components/ui/Dropdown.svelte';
	import { toggleQuickActionBar } from '$lib/stores/quickActionBar';
	import type { Snippet } from 'svelte';
	import IconDashboard from '~icons/icon-park-outline/dashboard-one';
	import IconForm from '~icons/icon-park-outline/form-one';
	import IconLogout from '~icons/icon-park-outline/logout';
	import IconPeople from '~icons/icon-park-outline/people';
	import IconPlus from '~icons/icon-park-outline/plus-cross';
	import IconBell from '~icons/icon-park-outline/remind';
	import IconSetting from '~icons/icon-park-outline/setting-two';
	import type { LayoutData } from './$types';

	let { data, children }: { data: LayoutData; children: Snippet } = $props();
	let user = $derived(data.user);
	let unreadCount = $state(0);
	let showNotifications = $state(false);
	let notificationContainer: HTMLElement | undefined = $state();

	function handleKeydown(event: KeyboardEvent) {
		if (event.ctrlKey && event.key === 'k') {
			event.preventDefault();
			toggleQuickActionBar();
		}
		if (showNotifications && event.key === 'Escape') {
			event.preventDefault();
			showNotifications = false;
		}
	}

	$effect(() => {
		if (!showNotifications || !notificationContainer) return;

		const handleClickOutside = (event: MouseEvent) => {
			if (notificationContainer && !notificationContainer.contains(event.target as Node)) {
				showNotifications = false;
			}
		};
		document.addEventListener('mousedown', handleClickOutside);

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	});

	function closeDrawer(): void {
		const drawerCheckbox = document.getElementById('my-drawer-app') as HTMLInputElement | null;
		if (drawerCheckbox) {
			drawerCheckbox.checked = false;
		}
	}
</script>

<svelte:body on:keydown={handleKeydown} />

<!-- Main Layout -->
<div class="min-h-screen bg-base-100 relative">
	<!-- Purposeful background system -->
	<div class="absolute inset-0 overflow-hidden pointer-events-none">
		<!-- Dynamic gradient mesh that responds to content -->
		<div class="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-secondary/2 animate-gradient-shift"></div>

		<!-- Floating connection lines that suggest data flow -->
		<svg class="absolute inset-0 w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
			<defs>
				<linearGradient id="connectionGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
					<stop offset="0%" style="stop-color:oklch(65% 0.25 280);stop-opacity:0.1" />
					<stop offset="50%" style="stop-color:oklch(70% 0.22 320);stop-opacity:0.2" />
					<stop offset="100%" style="stop-color:oklch(75% 0.18 60);stop-opacity:0.1" />
				</linearGradient>
				<linearGradient id="connectionGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
					<stop offset="0%" style="stop-color:oklch(70% 0.20 200);stop-opacity:0.1" />
					<stop offset="50%" style="stop-color:oklch(65% 0.25 240);stop-opacity:0.15" />
					<stop offset="100%" style="stop-color:oklch(75% 0.18 120);stop-opacity:0.1" />
				</linearGradient>
				<linearGradient id="connectionGradient3" x1="50%" y1="0%" x2="50%" y2="100%">
					<stop offset="0%" style="stop-color:oklch(68% 0.22 340);stop-opacity:0.08" />
					<stop offset="50%" style="stop-color:oklch(72% 0.20 20);stop-opacity:0.18" />
					<stop offset="100%" style="stop-color:oklch(70% 0.16 80);stop-opacity:0.08" />
				</linearGradient>
			</defs>

			<!-- Animated connection paths with variety -->
			<!-- Horizontal flowing path -->
			<path d="M50,180 Q350,120 650,220 Q950,160 1150,200"
				  stroke="url(#connectionGradient1)"
				  stroke-width="2"
				  fill="none"
				  class="animate-draw-path-1"
				  style="stroke-dasharray: 200 1000; stroke-dashoffset: 1200;" />

			<!-- Diagonal ascending path -->
			<path d="M180,650 Q480,450 780,520 Q1080,390 1200,300"
				  stroke="url(#connectionGradient2)"
				  stroke-width="1.5"
				  fill="none"
				  class="animate-draw-path-2"
				  style="stroke-dasharray: 180 800; stroke-dashoffset: 980;" />

			<!-- Curved vertical path -->
			<path d="M800,50 Q700,250 750,450 Q820,650 900,750"
				  stroke="url(#connectionGradient3)"
				  stroke-width="1.8"
				  fill="none"
				  class="animate-draw-path-3"
				  style="stroke-dasharray: 150 750; stroke-dashoffset: 900;" />

			<!-- Short connecting segments -->
			<path d="M300,350 Q450,280 600,340"
				  stroke="url(#connectionGradient1)"
				  stroke-width="1.2"
				  fill="none"
				  class="animate-draw-path-4"
				  style="stroke-dasharray: 100 300; stroke-dashoffset: 400;" />

			<path d="M150,480 Q250,420 350,460 Q450,500 550,440"
				  stroke="url(#connectionGradient2)"
				  stroke-width="1.4"
				  fill="none"
				  class="animate-draw-path-5"
				  style="stroke-dasharray: 120 380; stroke-dashoffset: 500;" />

			<!-- Gentle arc -->
			<path d="M950,600 Q1050,550 1100,650"
				  stroke="url(#connectionGradient3)"
				  stroke-width="1.6"
				  fill="none"
				  class="animate-draw-path-6"
				  style="stroke-dasharray: 80 220; stroke-dashoffset: 300;" />
		</svg>

		<!-- Interactive node points that pulse with activity -->
		<!-- Nodes positioned along the connection paths -->
		<div class="absolute w-3 h-3 bg-primary rounded-full animate-pulse-data shadow-lg shadow-primary/30" style="top: 22.5%; left: 8.3%;"></div>
		<div class="absolute w-2.5 h-2.5 bg-secondary rounded-full animate-pulse-data shadow-md shadow-secondary/30" style="top: 15%; left: 54.2%; animation-delay: 1.2s;"></div>
		<div class="absolute w-4 h-4 bg-accent rounded-full animate-pulse-data shadow-lg shadow-accent/30" style="top: 27.5%; left: 96%; animation-delay: 2.4s;"></div>

		<!-- Nodes for diagonal path -->
		<div class="absolute w-2 h-2 bg-info rounded-full animate-pulse-data shadow-md shadow-info/30" style="top: 81.3%; left: 15%; animation-delay: 0.8s;"></div>
		<div class="absolute w-3.5 h-3.5 bg-warning rounded-full animate-pulse-data shadow-lg shadow-warning/30" style="top: 65%; left: 65%; animation-delay: 3.2s;"></div>
		<div class="absolute w-2.5 h-2.5 bg-success rounded-full animate-pulse-data shadow-md shadow-success/30" style="top: 37.5%; left: 100%; animation-delay: 1.6s;"></div>

		<!-- Nodes for vertical path -->
		<div class="absolute w-2.5 h-2.5 bg-primary rounded-full animate-pulse-data shadow-md shadow-primary/30" style="top: 6.3%; left: 66.7%; animation-delay: 4s;"></div>
		<div class="absolute w-3 h-3 bg-secondary rounded-full animate-pulse-data shadow-lg shadow-secondary/30" style="top: 56.3%; left: 62.5%; animation-delay: 2.8s;"></div>
		<div class="absolute w-2 h-2 bg-accent rounded-full animate-pulse-data shadow-sm shadow-accent/30" style="top: 93.8%; left: 75%; animation-delay: 0.4s;"></div>

		<!-- Additional scattered nodes for depth -->
		<div class="absolute w-1.5 h-1.5 bg-info rounded-full animate-pulse-data shadow-sm shadow-info/30" style="top: 43.8%; left: 37.5%; animation-delay: 5.2s;"></div>
		<div class="absolute w-2 h-2 bg-warning rounded-full animate-pulse-data shadow-md shadow-warning/30" style="top: 60%; left: 29.2%; animation-delay: 3.6s;"></div>
		<div class="absolute w-2.5 h-2.5 bg-success rounded-full animate-pulse-data shadow-md shadow-success/30" style="top: 75%; left: 91.7%; animation-delay: 1.4s;"></div>

		<!-- Subtle grid pattern that suggests organization -->
		<div class="absolute inset-0 opacity-[0.02]"
			 style="background-image: linear-gradient(oklch(65% 0.25 280) 1px, transparent 1px), linear-gradient(90deg, oklch(65% 0.25 280) 1px, transparent 1px); background-size: 60px 60px;"></div>
	</div>

	<div class="drawer lg:drawer-open relative z-10">
		<input id="my-drawer-app" type="checkbox" class="drawer-toggle" />
		<label for="my-drawer-app" class="sr-only">Toggle Drawer</label>
		<div class="drawer-content flex flex-col">
			<!-- Mobile-Optimized Navbar -->
			<div
				class="navbar bg-base-100/90 border-b border-base-300/20 relative z-10 shadow-sm backdrop-blur-sm min-h-16"
				data-testid="app-navbar"
			>
				<div class="navbar-start">
					<label
						for="my-drawer-app"
						class="btn btn-square btn-ghost lg:hidden transition-colors duration-200 hover:bg-primary/5 touch-manipulation"
						aria-label="open sidebar"
						data-testid="app-navbar-drawer-toggle"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							class="inline-block h-6 w-6 lg:h-5 lg:w-5 stroke-current"
							><path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M4 6h16M4 12h16M4 18h16"
							></path></svg
						>
					</label>
					<a
						class="flex items-center gap-2 btn btn-ghost text-base lg:text-lg font-semibold text-base-content/90 hover:text-base-content transition-colors duration-200 touch-manipulation"
						href="/app"
						data-testid="app-navbar-dashboard-link"
					>
						<div class="w-1.5 h-1.5 bg-primary/60 rounded-full"></div>
						<span class="hidden sm:inline">Hairloom</span>
						<span class="sm:hidden">HL</span>
						<div class="badge badge-warning badge-xs font-semibold ml-1">BETA</div>
					</a>
				</div>
				<div class="navbar-center">
					<!-- Subtle decorative elements -->
					<div class="hidden md:flex items-center gap-4">
						<div class="w-1 h-1 bg-primary/30 rounded-full"></div>
						<div class="w-1.5 h-1.5 bg-secondary/25 rounded-full"></div>
						<div class="w-1 h-1 bg-accent/20 rounded-full"></div>
					</div>
				</div>
				<!-- Mobile-Optimized Navbar End -->
				<div class="navbar-end gap-1 lg:gap-2">
					<!-- Theme Controller - Hidden on small screens -->
					<div class="hidden sm:block transition-colors duration-200">
						<ThemeController />
					</div>

					<!-- Notification Dropdown -->
					<div class="relative" bind:this={notificationContainer}>
						<button
							class="btn btn-ghost btn-circle transition-colors duration-200 hover:bg-base-200/50 touch-manipulation"
							onclick={() => (showNotifications = !showNotifications)}
							aria-haspopup="true"
							aria-expanded={showNotifications}
						>
							<div class="indicator">
								<IconBell class="h-5 w-5 text-base-content/70" />
								{#if unreadCount > 0}
									<span class="badge badge-primary badge-xs lg:badge-sm indicator-item">{unreadCount}</span>
								{/if}
							</div>
						</button>
						{#if showNotifications}
							<div class="absolute top-full right-0 z-50 mt-2">
								<NotificationHistory />
							</div>
						{/if}
					</div>

					<!-- Profile Dropdown -->
					<Dropdown align="right" width="w-64">
						{#snippet trigger()}
							<div role="button" class="btn btn-ghost btn-circle avatar transition-colors duration-200 hover:bg-base-200/50 touch-manipulation">
								<div class="w-8 lg:w-9 rounded-full border border-base-300/20">
									<img
										alt="User Avatar"
										src={data.user.profile_photo_url ?? '/avatar.png'}
										class="w-full h-full rounded-full object-cover"
									/>
								</div>
							</div>
						{/snippet}
						{#snippet children()}
							<div class="bg-base-100 border border-base-300/30 shadow-lg rounded-xl overflow-hidden">
								<!-- Profile Header -->
								<div class="bg-base-50/50 p-4 border-b border-base-300/20">
									<div class="flex items-center gap-3">
										<div class="w-10 h-10 rounded-full border border-base-300/20">
											<img
												alt="User Avatar"
												src={data.user.profile_photo_url ?? '/avatar.png'}
												class="w-full h-full rounded-full object-cover"
											/>
										</div>
										<div>
											<h3 class="font-semibold text-base-content/90">Hello, {user.first_name}</h3>
											<p class="text-sm text-base-content/60">{data.user.email}</p>
										</div>
									</div>
								</div>

								<!-- Menu Items -->
								<div class="p-2">
									<a
										href="/app/clients/new"
										class="flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 hover:bg-primary/5 group"
									>
										<div class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
											<IconPlus class="h-4 w-4 text-primary/70" />
										</div>
										<div>
											<p class="font-medium text-base-content/90">New Client</p>
											<p class="text-xs text-base-content/60">Add a new client</p>
										</div>
									</a>

									<a
										href="/app/settings"
										class="flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 hover:bg-base-200/50 group"
									>
										<div class="w-8 h-8 bg-base-200/50 rounded-lg flex items-center justify-center">
											<IconSetting class="h-4 w-4 text-base-content/70" />
										</div>
										<div>
											<p class="font-medium text-base-content/90">Settings</p>
											<p class="text-xs text-base-content/60">Account preferences</p>
										</div>
									</a>

									<div class="border-t border-base-300/20 mt-2 pt-2">
										<form method="POST" action="/logout" class="w-full">
											<button
												type="submit"
												class="flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 hover:bg-error/5 group w-full text-left"
											>
												<div class="w-8 h-8 bg-error/10 rounded-lg flex items-center justify-center">
													<IconLogout class="h-4 w-4 text-error/70" />
												</div>
												<div>
													<p class="font-medium text-error/90">Logout</p>
													<p class="text-xs text-base-content/60">Sign out</p>
												</div>
											</button>
										</form>
									</div>
								</div>
							</div>
						{/snippet}
					</Dropdown>
				</div>
			</div>

			<!-- Page Content Area -->
			<main class="flex-1 overflow-y-auto p-6" data-testid="app-main-content">
				<div class="max-w-7xl mx-auto">
					{@render children()}
				</div>
			</main>

			<!-- Global components -->
			<ConfirmationModal />
			<QuickActionBar />
			<QuickActionFAB />
		</div>
		<!-- Sidebar -->
		<div
			class="drawer-side bg-base-100/90 border-r border-base-300/20 z-20 shadow-sm backdrop-blur-sm h-screen"
			style="--sidebar-width: 20rem; width: var(--sidebar-width); min-width: 4rem; max-width: 28rem;"
			data-testid="app-drawer-side"
		>
			<label for="my-drawer-app" aria-label="close sidebar" class="drawer-overlay"></label>

			<!-- Sidebar Content -->
			<div class="h-full w-full flex flex-col">
				<!-- Sidebar Header -->
				<div class="p-5 border-b border-base-300/20">
					<div class="flex items-center gap-3">
						<div class="w-8 h-8 bg-primary/10 border border-primary/20 rounded-lg flex items-center justify-center">
							<span class="text-primary/80 font-semibold text-sm">H</span>
						</div>
						<div>
							<h3 class="font-semibold text-base-content/90">Navigation</h3>
							<p class="text-xs text-base-content/60">Manage your business</p>
						</div>
					</div>
				</div>

				<!-- Mobile-Optimized Navigation Menu -->
				<ul class="menu text-base-content flex-1 p-3 lg:p-4 gap-1 lg:gap-2" data-testid="app-sidebar-menu" style="width: 100%;">
					<li>
						<a
							href="/app"
							class="group transition-colors duration-200 touch-manipulation py-3 lg:py-2 {page.url.pathname === '/app' ? 'bg-primary/10 text-primary border-l-2 border-primary/50' : 'hover:bg-primary/5'}"
							onclick={closeDrawer}
							data-testid="app-sidebar-dashboard-link"
						>
							<IconDashboard class="h-6 w-6 lg:h-5 lg:w-5" />
							<span class="font-medium text-base lg:text-sm">Dashboard</span>
							{#if page.url.pathname === '/app'}
								<div class="ml-auto w-2 h-2 lg:w-1.5 lg:h-1.5 bg-primary rounded-full"></div>
							{/if}
						</a>
					</li>
					<li>
						<a
							href="/app/clients"
							class="group transition-colors duration-200 touch-manipulation py-3 lg:py-2 {page.url.pathname.startsWith('/app/clients') ? 'bg-secondary/10 text-secondary border-l-2 border-secondary/50' : 'hover:bg-secondary/5'}"
							onclick={closeDrawer}
							data-testid="app-sidebar-clients-link"
						>
							<IconPeople class="h-6 w-6 lg:h-5 lg:w-5" />
							<span class="font-medium text-base lg:text-sm">Clients</span>
							{#if page.url.pathname.startsWith('/app/clients')}
								<div class="ml-auto w-2 h-2 lg:w-1.5 lg:h-1.5 bg-secondary rounded-full"></div>
							{/if}
						</a>
					</li>
					<li>
						<a
							href="/app/templates"
							class="group transition-colors duration-200 touch-manipulation py-3 lg:py-2 {page.url.pathname.startsWith('/app/templates') ? 'bg-accent/10 text-accent border-l-2 border-accent/50' : 'hover:bg-accent/5'}"
							onclick={closeDrawer}
							data-testid="app-sidebar-templates-link"
						>
							<IconForm class="h-6 w-6 lg:h-5 lg:w-5" />
							<span class="font-medium text-base lg:text-sm">Note Templates</span>
							{#if page.url.pathname.startsWith('/app/templates')}
								<div class="ml-auto w-2 h-2 lg:w-1.5 lg:h-1.5 bg-accent rounded-full"></div>
							{/if}
						</a>
					</li>

					<li>
						<a
							href="/app/settings"
							class="group transition-colors duration-200 touch-manipulation py-3 lg:py-2 {page.url.pathname.startsWith('/app/settings') ? 'bg-base-200/50 text-base-content border-l-2 border-base-content/30' : 'hover:bg-base-200/30'}"
							onclick={closeDrawer}
							data-testid="app-sidebar-settings-link"
						>
							<IconSetting class="h-6 w-6 lg:h-5 lg:w-5" />
							<span class="font-medium text-base lg:text-sm">Settings</span>
							{#if page.url.pathname.startsWith('/app/settings')}
								<div class="ml-auto w-2 h-2 lg:w-1.5 lg:h-1.5 bg-base-content/60 rounded-full"></div>
							{/if}
						</a>
					</li>
				</ul>

				<!-- Sidebar Footer -->
				<div class="p-4 border-t border-base-300/20">
					<div class="text-center">
						<p class="text-xs text-base-content/40">Made with care</p>
					</div>
				</div>
			</div>
			<!-- Sidebar resize handle (desktop only) -->
			        <div
          class="hidden lg:block absolute top-0 right-0 h-full w-2 cursor-ew-resize z-30"
          role="button"
          tabindex="0"
          aria-label="Resize sidebar"
          onmousedown={(e) => {
					e.preventDefault();
					const target = e.target as HTMLElement;
					if (!target) return;
					const sidebar = target.parentElement;
					if (!sidebar) return;
					const startX = e.clientX;
					const startWidth = sidebar.offsetWidth;
					function onMouseMove(ev: MouseEvent) {
						const newWidth = Math.min(448, Math.max(64, startWidth + (ev.clientX - startX)));
						sidebar?.style.setProperty('--sidebar-width', newWidth + 'px');
					}
					function onMouseUp() {
						document.removeEventListener('mousemove', onMouseMove);
						document.removeEventListener('mouseup', onMouseUp);
					}
					document.addEventListener('mousemove', onMouseMove);
					document.addEventListener('mouseup', onMouseUp);
				}}
			></div>
		</div>
	</div>
</div>
