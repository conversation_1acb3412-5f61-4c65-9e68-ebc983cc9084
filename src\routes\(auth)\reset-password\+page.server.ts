import { resetPasswordSchema as baseResetPasswordSchema } from '$lib/schemas/auth';
import { invalidateUserSessions } from '$lib/server/auth';
import { verifyPasswordStrength } from '$lib/server/auth/password';
import {
	getPasswordResetAttemptData,
	invalidateUserPasswordResetAttempts
} from '$lib/server/auth/password-reset';
import { updateUserPassword } from '$lib/server/auth/user';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types.js';

export const load: PageServerLoad = async (event) => {
	const token = event.url.searchParams.get('token');
	if (!token) {
		error(400, 'Missing password reset token.');
	}

	const attemptData = await getPasswordResetAttemptData(token);
	if (!attemptData) {
		error(400, 'Invalid or expired password reset link.');
	}

	return {
		token
	};
};

const resetPasswordSchema = baseResetPasswordSchema.refine(
	async (data) => await verifyPasswordStrength(data.password),
	{
		message: 'New password is not strong enough.',
		path: ['password']
	}
);

export const actions: Actions = {
	default: async (event) => {
		const { request } = event;
		const formData = Object.fromEntries(await request.formData());
		const token = formData.token as string;

		if (!token) {
			return fail(400, { data: formData, errors: { root: ['Invalid or missing token.'] } });
		}

		const validation = await resetPasswordSchema.safeParseAsync(formData);

		if (!validation.success) {
			return fail(400, {
				data: formData,
				errors: validation.error.flatten().fieldErrors
			});
		}
		const { password } = validation.data;

		const attemptData = await getPasswordResetAttemptData(token);
		if (!attemptData) {
			return fail(400, {
				data: formData,
				errors: { root: ['Invalid or expired password reset link.'] }
			});
		}
		const { user } = attemptData;

		await updateUserPassword(user.id, password);
		await invalidateUserPasswordResetAttempts(user.id);
		await invalidateUserSessions(user.id);

		// Create a new session for the user
		// Not setting a flash message here, will redirect to login where they can sign in.
		redirect(302, '/login');
	}
};
