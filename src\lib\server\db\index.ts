import { env } from '$env/dynamic/private';
import { createPool } from '@vercel/postgres';
import { drizzle } from 'drizzle-orm/vercel-postgres';
import * as schema from './schema';

const databaseUrl = env.POSTGRES_URL;

if (!databaseUrl) {
	const vercelEnv = env.VERCEL_ENV || 'local';
	throw new Error(
		`POSTGRES_URL is not set for the ${vercelEnv} environment. Please check your environment variables.`
	);
}

export const db = drizzle(createPool({ connectionString: databaseUrl }), { schema });
