<script lang="ts">
	import { fieldTypeSchema, templateDefinitionSchema } from '$lib/schemas/template';
	import { webllm } from '$lib/stores/webllm.svelte';
	import { toast } from '$lib/ui/toast';
	import IconSparkle from '~icons/icon-park-outline/magic-hat';

	let aiPrompt = $state('');
	let generatedSchema = $state<object | null>(null);

	async function generateSchema() {
		if ($webllm.status === 'uninitialized') {
			await webllm.initialize();
		}
		if ($webllm.status !== 'ready') {
			toast.info('AI is still loading, please wait a moment and try again.');
			return;
		}

		generatedSchema = null;
		const fieldTypes = fieldTypeSchema.options.join(', ');

		const systemPrompt = `You are an API that returns ONLY raw JSON, never any other text or explanation. Your entire response must be a single JSON object. Do not use markdown, do not use code fences.
Based on the user's request, create a JSON object that strictly follows this format: \`{ "fields": [{ "label": string, "name": string, "type": string, "placeholder": string?, "required": boolean?, "options": [{ "label": string, "value": string }]? }] }\`.
- The "name" must be a camelCase version of the "label".
- The "type" must be one of: ${fieldTypes}.
- Only include "options" for "select" and "radio" types.
- Ensure option values are simple strings, often a lowercase version of the label.
- Do not include "id" or "defaultValue" properties.`;

		const fullPrompt = `${systemPrompt}\n\nUser request: "${aiPrompt}"`;

		let generatedJson = await webllm.generate(fullPrompt);

		const jsonMatch = generatedJson.match(/```json\n([\s\S]*?)\n```/);
		if (jsonMatch && jsonMatch[1]) {
			generatedJson = jsonMatch[1];
		}
		generatedJson = generatedJson.trim();

		try {
			const parsed = JSON.parse(generatedJson);

			const validation = templateDefinitionSchema.safeParse(parsed);

			if (validation.success) {
				generatedSchema = validation.data;
				toast.success('AI generated a valid schema!');
				aiPrompt = '';
			} else {
				console.error('Zod validation error:', validation.error);
				toast.error(
					`AI response had an unexpected structure: ${validation.error.issues.map((e) => e.message).join(', ')}`
				);
				generatedSchema = { error: 'Validation failed', details: validation.error.format() };
			}
		} catch (e) {
			console.error('Failed to parse AI response:', e, 'Raw response:', generatedJson);
			toast.error('The AI returned invalid JSON. Please try again.');
			generatedSchema = { error: 'JSON parsing failed', response: generatedJson };
		}
	}
</script>

<div class="container mx-auto p-4 md:p-8 space-y-6">
	<h1 class="text-3xl font-bold">WebLLM Schema Generation Test</h1>

	<!-- AI Generation Card -->
	<div class="card bg-base-100 shadow-xl border">
		<div class="card-body">
			<div class="flex items-center justify-between">
				<h2 class="card-title flex items-center gap-2">
					<IconSparkle class="h-6 w-6" />
					Generate Fields with AI
				</h2>
				{#if $webllm.status === 'uninitialized'}
					<button type="button" class="btn btn-sm btn-outline" onclick={webllm.initialize}>
						Initialize AI
					</button>
				{/if}
			</div>
			<p class="text-sm text-base-content/70">
				Describe the fields you want in your template, and the AI will generate them for you. For
				example: "A form to collect a client's name, email, and a multi-line text area for their
				project requirements."
			</p>

			<div class="form-control mt-2">
				<textarea
					bind:value={aiPrompt}
					class="textarea textarea-bordered"
					placeholder="Describe your template..."
					rows="3"
					disabled={$webllm.status === 'loading' || $webllm.status === 'generating'}
				></textarea>
			</div>

			<div class="card-actions justify-end mt-2">
				{#if $webllm.status === 'loading'}
					<div class="flex items-center gap-2">
						<span class="text-sm">Loading AI model...</span>
						<progress
							class="progress progress-primary w-24"
							value={$webllm.progress}
							max="100"
						></progress>
					</div>
				{:else if $webllm.status === 'generating'}
					<button class="btn btn-primary" disabled>
						<span class="loading loading-spinner"></span>
						Generating...
					</button>
				{:else}
					<button
						type="button"
						class="btn btn-primary"
						onclick={generateSchema}
						disabled={!aiPrompt.trim() || $webllm.status !== 'ready'}
					>
						Generate
					</button>
				{/if}
			</div>
			{#if $webllm.status === 'error'}
				<div class="alert alert-error text-sm">
					<p>Error: {$webllm.error}</p>
				</div>
			{/if}
		</div>
	</div>

	<!-- Generated Schema Output -->
	{#if generatedSchema}
		<div class="card bg-base-200 shadow-inner">
			<div class="card-body">
				<h3 class="card-title">Generated Output</h3>
				<pre
					class="bg-base-300 p-4 rounded-lg overflow-x-auto text-sm"
				><code class="language-json">{JSON.stringify(generatedSchema, null, 2)}</code></pre>
			</div>
		</div>
	{/if}
</div> 