@import 'tailwindcss';
@import './lib/styles/animations.css';

/* CSS Design System Constants */
:root {
	/* Radius values - consolidated magic numbers */
	--radius-large: 2rem;
	--radius-medium: 0.75rem;
	--radius-small: 0.25rem;

	/* Size values - consolidated magic numbers */
	--size-border: 1px;
	--size-touch-target: 2.75rem; /* 44px minimum touch target */

	/* Animation values - consolidated magic numbers */
	--animation-scale-hover: 1.05;
	--animation-scale-initial: 0.95;
	--animation-rotate-wiggle: 3deg;
	--animation-translate-slide: 20px;
	--animation-shadow-glow: 5px;
}

@plugin "daisyui" {
	themes:
		hairloomlight --default,
		hairloomdark --prefersdark;
}

/* Consolidated theme definition - eliminates duplicate theme definitions */
@plugin "daisyui/theme" {
	name: 'hairloomdark';
	default: false;
	prefersdark: true;
	color-scheme: 'dark';
	--color-base-100: oklch(12% 0.042 264.695);
	--color-base-200: oklch(20% 0.042 265.755);
	--color-base-300: oklch(27% 0.041 260.031);
	--color-base-content: oklch(96% 0.007 247.896);
	--color-primary: oklch(68% 0.28 285);
	--color-primary-content: oklch(98% 0.02 285);
	--color-secondary: oklch(72% 0.25 315);
	--color-secondary-content: oklch(98% 0.02 315);
	--color-accent: oklch(78% 0.20 65);
	--color-accent-content: oklch(20% 0.02 65);
	--color-neutral: oklch(12% 0.042 264.695);
	--color-neutral-content: oklch(98% 0.003 247.858);
	--color-info: oklch(60% 0.126 221.723);
	--color-info-content: oklch(98% 0.019 200.873);
	--color-success: oklch(64% 0.2 131.684);
	--color-success-content: oklch(98% 0.031 120.757);
	--color-warning: oklch(64% 0.222 41.116);
	--color-warning-content: oklch(98% 0.016 73.684);
	--color-error: oklch(57% 0.245 27.325);
	--color-error-content: oklch(97% 0.013 17.38);
	--radius-selector: var(--radius-large);
	--radius-field: var(--radius-large);
	--radius-box: var(--radius-large);
	--size-selector: var(--radius-small);
	--size-field: var(--radius-small);
	--border: var(--size-border);
	--depth: 1;
	--noise: 1;
}

@plugin "daisyui/theme" {
	name: 'hairloomlight';
	default: true;
	prefersdark: false;
	color-scheme: light;
	--color-base-100: oklch(98% 0 0);
	--color-base-200: oklch(97% 0 0);
	--color-base-300: oklch(92% 0 0);
	--color-base-content: oklch(20% 0 0);
	--color-primary: oklch(65% 0.25 280);
	--color-primary-content: oklch(98% 0.02 280);
	--color-secondary: oklch(70% 0.22 320);
	--color-secondary-content: oklch(98% 0.02 320);
	--color-accent: oklch(75% 0.18 60);
	--color-accent-content: oklch(20% 0.02 60);
	--color-neutral: oklch(26% 0 0);
	--color-neutral-content: oklch(98% 0 0);
	--color-info: oklch(71% 0.143 215.221);
	--color-info-content: oklch(98% 0.019 200.873);
	--color-success: oklch(70% 0.14 182.503);
	--color-success-content: oklch(98% 0.014 180.72);
	--color-warning: oklch(70% 0.213 47.604);
	--color-warning-content: oklch(98% 0.016 73.684);
	--color-error: oklch(65% 0.241 354.308);
	--color-error-content: oklch(97% 0.014 343.198);
	--radius-selector: var(--radius-large);
	--radius-field: var(--radius-large);
	--radius-box: var(--radius-large);
	--size-selector: var(--radius-small);
	--size-field: var(--radius-small);
	--border: var(--size-border);
	--depth: 1;
	--noise: 1;
}

/*
CONSOLIDATED CSS: Removed duplicate theme definitions
Previously had 3 copies of the same theme colors:
1. @plugin "daisyui/theme" blocks (kept - these are the active definitions)
2. [data-theme="hairloomlight"] (removed - redundant)
3. [data-theme="hairloomdark"] (removed - redundant)

Magic numbers consolidated into CSS custom properties at :root level.
*/
