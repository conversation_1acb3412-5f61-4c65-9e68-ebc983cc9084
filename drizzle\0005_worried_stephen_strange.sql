CREATE TABLE "expiring_rate_limit_buckets" (
	"key" text PRIMARY KEY NOT NULL,
	"count" integer NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "client_notes" DROP CONSTRAINT "client_notes_template_id_templates_id_fk";
--> statement-breakpoint
ALTER TABLE "client_notes" ALTER COLUMN "template_id" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "client_notes" ALTER COLUMN "note_data" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "client_notes" ADD CONSTRAINT "client_notes_template_id_templates_id_fk" FOREIGN KEY ("template_id") REFERENCES "public"."templates"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "client_notes" DROP COLUMN "content";--> statement-breakpoint
ALTER TABLE "client_notes" DROP COLUMN "title";