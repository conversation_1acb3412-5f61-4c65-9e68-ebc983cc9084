/**
 * Shared constants and magic numbers
 * Centralized location for all hardcoded values used throughout the application
 */

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const ERROR_MESSAGES = {
  // Generic errors
  GENERIC_ERROR: 'An error occurred. Please try again or contact support.',
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  
  // Authentication errors
  INVALID_CREDENTIALS: 'Invalid email or password.',
  ACCOUNT_LOCKED: 'Account temporarily locked due to too many failed attempts.',
  SESSION_EXPIRED: 'Your session has expired. Please sign in again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  
  // Validation errors
  REQUIRED_FIELD: 'This field is required.',
  INVALID_EMAIL: 'Please enter a valid email address.',
  PASSWORD_TOO_SHORT: 'Password must be at least 8 characters long.',
  INVALID_PHONE: 'Please enter a valid phone number.',
  INVALID_BARCODE: 'Please enter a valid barcode.',
  
  // File upload errors
  FILE_TOO_LARGE: 'File size must be less than 5MB.',
  INVALID_FILE_TYPE: 'Please select a valid image file (JPEG, PNG, GIF, WebP).',
  UPLOAD_FAILED: 'Failed to upload image. Please try again.',
  
  // Camera errors
  CAMERA_ACCESS_DENIED: 'Camera access denied. Please allow camera permissions and try again.',
  CAMERA_NOT_FOUND: 'No camera found. Please ensure your device has a camera and try again.',
  CAMERA_IN_USE: 'Camera is already in use. Please close other applications using the camera and try again.',
  CAMERA_NOT_SUPPORTED: 'Camera access is not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.',
  HTTPS_REQUIRED: 'Camera access requires a secure connection (HTTPS). Please access this page over HTTPS.',
  
  // Inventory errors
  ITEM_NOT_FOUND: 'Inventory item not found.',
  BARCODE_EXISTS: 'An item with this barcode already exists.',
  INSUFFICIENT_QUANTITY: 'Insufficient quantity available.',
  INVALID_QUANTITY: 'Quantity must be a positive number.',
  
  // Shopping list errors
  ITEM_ALREADY_IN_LIST: 'Item is already in your shopping list.',
  LIST_ITEM_NOT_FOUND: 'Shopping list item not found.',
  
  // Client errors
  CLIENT_NOT_FOUND: 'Client not found.',
  DUPLICATE_CLIENT: 'A client with this email already exists.',
  
  // Template errors
  TEMPLATE_NOT_FOUND: 'Template not found.',
  TEMPLATE_NAME_EXISTS: 'A template with this name already exists.',
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const SUCCESS_MESSAGES = {
  // Generic success
  SAVED: 'Changes saved successfully.',
  DELETED: 'Item deleted successfully.',
  UPDATED: 'Item updated successfully.',
  CREATED: 'Item created successfully.',
  
  // Authentication
  LOGIN_SUCCESS: 'Welcome back!',
  LOGOUT_SUCCESS: 'You have been signed out.',
  REGISTRATION_SUCCESS: 'Account created successfully.',
  PASSWORD_RESET: 'Password reset email sent.',
  
  // Inventory
  INVENTORY_ITEM_CREATED: 'Inventory item created successfully.',
  INVENTORY_ITEM_UPDATED: 'Inventory item updated successfully.',
  INVENTORY_ITEM_DELETED: 'Inventory item deleted successfully.',
  QUANTITY_UPDATED: 'Quantity updated successfully.',
  
  // Shopping list
  ADDED_TO_SHOPPING_LIST: 'Item added to shopping list.',
  REMOVED_FROM_SHOPPING_LIST: 'Item removed from shopping list.',
  SHOPPING_LIST_CLEARED: 'Shopping list cleared.',
  ITEM_PURCHASED: 'Item marked as purchased.',
  
  // Quick restock
  QUICK_RESTOCK_COMPLETE: 'Quick restock completed successfully.',
  
  // Clients
  CLIENT_CREATED: 'Client created successfully.',
  CLIENT_UPDATED: 'Client updated successfully.',
  CLIENT_DELETED: 'Client deleted successfully.',
  
  // Templates
  TEMPLATE_CREATED: 'Template created successfully.',
  TEMPLATE_UPDATED: 'Template updated successfully.',
  TEMPLATE_DELETED: 'Template deleted successfully.',
  
  // File uploads
  IMAGE_UPLOADED: 'Image uploaded successfully.',
  IMAGE_REMOVED: 'Image removed successfully.',
} as const;

// ============================================================================
// INFO MESSAGES
// ============================================================================

export const INFO_MESSAGES = {
  // Loading states
  LOADING: 'Loading...',
  SAVING: 'Saving...',
  UPLOADING: 'Uploading...',
  PROCESSING: 'Processing...',
  
  // Camera and scanner
  CAMERA_INITIALIZING: 'Initializing camera...',
  SCANNER_READY: 'Scanner ready. Point camera at barcode.',
  BARCODE_DETECTED: 'Barcode detected.',
  
  // Inventory
  LOW_STOCK_WARNING: 'Low stock warning',
  OUT_OF_STOCK: 'Out of stock',
  REORDER_SUGGESTED: 'Consider reordering this item.',
  
  // Shopping list
  EMPTY_SHOPPING_LIST: 'Your shopping list is empty.',
  ALL_ITEMS_PURCHASED: 'All items have been purchased!',
  
  // Search
  NO_RESULTS: 'No results found.',
  SEARCH_PLACEHOLDER: 'Search...',
  
  // General
  NO_DATA: 'No data available.',
  COMING_SOON: 'This feature is coming soon.',
} as const;

// ============================================================================
// CONFIRMATION MESSAGES
// ============================================================================

export const CONFIRMATION_MESSAGES = {
  // Delete confirmations
  DELETE_INVENTORY_ITEM: {
    title: 'Delete Inventory Item',
    message: 'Are you sure you want to delete this inventory item? This action cannot be undone.',
    confirmText: 'Delete',
    cancelText: 'Cancel',
    confirmClass: 'btn-error',
  },
  
  DELETE_CLIENT: {
    title: 'Delete Client',
    message: 'Are you sure you want to delete this client? This action cannot be undone.',
    confirmText: 'Delete',
    cancelText: 'Cancel',
    confirmClass: 'btn-error',
  },
  
  DELETE_TEMPLATE: {
    title: 'Delete Template',
    message: 'Are you sure you want to delete this template? This action cannot be undone.',
    confirmText: 'Delete',
    cancelText: 'Cancel',
    confirmClass: 'btn-error',
  },
  
  // Clear confirmations
  CLEAR_SHOPPING_LIST: {
    title: 'Clear Shopping List',
    message: 'Are you sure you want to clear your entire shopping list?',
    confirmText: 'Clear',
    cancelText: 'Cancel',
    confirmClass: 'btn-warning',
  },
  
  // Logout confirmation
  LOGOUT: {
    title: 'Sign Out',
    message: 'Are you sure you want to sign out?',
    confirmText: 'Sign Out',
    cancelText: 'Cancel',
    confirmClass: 'btn-primary',
  },
  
  // Discard changes
  DISCARD_CHANGES: {
    title: 'Discard Changes',
    message: 'You have unsaved changes. Are you sure you want to discard them?',
    confirmText: 'Discard',
    cancelText: 'Keep Editing',
    confirmClass: 'btn-warning',
  },
} as const;

// ============================================================================
// PLACEHOLDER TEXTS
// ============================================================================

export const PLACEHOLDERS = {
  // Form fields
  NAME: 'Enter name',
  DESCRIPTION: 'Enter description',
  EMAIL: 'Enter email address',
  PHONE: 'Enter phone number',
  BARCODE: 'Enter or scan barcode',
  QUANTITY: 'Enter quantity',
  PRICE: 'Enter price',
  COST: 'Enter cost',
  SUPPLIER: 'Enter supplier name',
  
  // Search
  SEARCH_INVENTORY: 'Search inventory...',
  SEARCH_CLIENTS: 'Search clients...',
  SEARCH_TEMPLATES: 'Search templates...',
  SEARCH_GENERAL: 'Search...',
  
  // Image upload
  IMAGE_UPLOAD: 'Upload an image',
  IMAGE_UPLOAD_COMPACT: 'Add image',
  IMAGE_UPLOAD_GALLERY: 'Add to gallery',
  
  // Notes and comments
  NOTES: 'Add notes...',
  COMMENTS: 'Add a comment...',
} as const;

// ============================================================================
// BUTTON TEXTS
// ============================================================================

export const BUTTON_TEXTS = {
  // Actions
  SAVE: 'Save',
  CANCEL: 'Cancel',
  DELETE: 'Delete',
  EDIT: 'Edit',
  CREATE: 'Create',
  UPDATE: 'Update',
  ADD: 'Add',
  REMOVE: 'Remove',
  CLEAR: 'Clear',
  RESET: 'Reset',
  
  // Navigation
  BACK: 'Back',
  NEXT: 'Next',
  PREVIOUS: 'Previous',
  CONTINUE: 'Continue',
  FINISH: 'Finish',
  
  // File operations
  UPLOAD: 'Upload',
  CHOOSE_FILE: 'Choose File',
  TAKE_PHOTO: 'Take Photo',
  REMOVE_IMAGE: 'Remove',
  
  // Authentication
  SIGN_IN: 'Sign In',
  SIGN_OUT: 'Sign Out',
  SIGN_UP: 'Sign Up',
  FORGOT_PASSWORD: 'Forgot Password',
  
  // Shopping list
  ADD_TO_LIST: 'Add to List',
  MARK_PURCHASED: 'Mark as Purchased',
  MARK_UNPURCHASED: 'Mark as Unpurchased',
  
  // Scanner
  SCAN_BARCODE: 'Scan Barcode',
  MANUAL_INPUT: 'Manual Input',
  USE_CAMERA: 'Use Camera',
  
  // Quick actions
  QUICK_RESTOCK: 'Quick Restock',
  BULK_EDIT: 'Bulk Edit',
  EXPORT: 'Export',
  IMPORT: 'Import',
} as const;

// ============================================================================
// KEYBOARD SHORTCUTS
// ============================================================================

export const KEYBOARD_SHORTCUTS = {
  // Global shortcuts
  QUICK_ACTION: 'cmd+k',
  SEARCH: 'cmd+f',
  NEW_ITEM: 'cmd+n',
  SAVE: 'cmd+s',
  
  // Navigation
  DASHBOARD: 'cmd+1',
  INVENTORY: 'cmd+2',
  CLIENTS: 'cmd+3',
  SHOPPING_LIST: 'cmd+4',
  TEMPLATES: 'cmd+5',
  SETTINGS: 'cmd+6',
  
  // Modal controls
  CLOSE_MODAL: 'Escape',
  CONFIRM: 'Enter',
  
  // Scanner controls
  TOGGLE_SCANNER: 'cmd+shift+s',
  MANUAL_BARCODE: 'cmd+shift+m',
} as const;

// ============================================================================
// REGEX PATTERNS
// ============================================================================

export const REGEX_PATTERNS = {
  // Validation patterns
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[+]?[1-9][\d]{0,15}$/,
  BARCODE: /^[0-9A-Za-z\-_]+$/,
  
  // Image validation
  BASE64_IMAGE: /^data:image\/(jpeg|jpg|png|gif|webp);base64,/,
  
  // Currency
  CURRENCY: /^\d+(\.\d{1,2})?$/,
  
  // Safe error patterns (for production)
  SAFE_ERROR_PATTERNS: [
    /^Access denied/i,
    /^Not found/i,
    /^Page not found/i,
    /^Unauthorized/i,
    /^Forbidden/i,
    /^Bad request/i,
    /^Invalid request/i,
  ],
} as const;
