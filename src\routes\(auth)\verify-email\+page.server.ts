import { resendCodeSchema, verifyCodeSchema } from '$lib/schemas/auth';
import {
	createEmailVerificationAttempt,
	getEmailVerificationAttemptData,
	invalidateEmailVerificationAttempt,
	sendVerificationEmail,
	sendVerificationEmailBucket, // Rate limiter for resending
	verifyEmailVerificationOtp
} from '$lib/server/auth/email-verification';
import { ExpiringTokenBucket } from '$lib/server/auth/rate-limit';
import { setUserEmailAsVerified } from '$lib/server/auth/user'; // Added
import { db } from '$lib/server/db'; // Added
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

// Rate limiter for OTP verification attempts, keyed by hashed URL token ID
const otpVerificationBucket = new ExpiringTokenBucket<string>(5, 60 * 5); // 5 attempts per 5 minutes

const ResendRateLimit = new ExpiringTokenBucket<string>(1, 60 * 1); // 1 try per minute

export const load: PageServerLoad = async (event) => {
	const { user, session } = event.locals;
	if (!user || !session) {
		return redirect(302, '/login');
	}
	if (user.email_verified) {
		return redirect(302, '/app');
	}

	const urlToken = event.url.searchParams.get('token');

	if (!urlToken) {
		// No token, means user landed here to start verification or token expired badly.
		// Attempt to create and send a new one if not rate-limited.
		if (!sendVerificationEmailBucket.check(user.id, 1)) {
			return { error: 'Too many requests to send verification email. Please try again later.' };
		}
		await sendVerificationEmailBucket.consume(user.id, 1);
		const newAttempt = await createEmailVerificationAttempt(user.id, user.email);
		if (newAttempt) {
			sendVerificationEmail(
				user.email,
				newAttempt.plaintextUrlToken,
				newAttempt.plaintextOtp,
				event.url.origin
			);
			// Redirect to self with the new token
			return redirect(302, `/verify-email?token=${newAttempt.plaintextUrlToken}&initial_send=true`);
		} else {
			return { error: 'Failed to create a verification request. Please try again.' };
		}
	}

	// Token is present in URL, try to load its data
	const attempt = await getEmailVerificationAttemptData(urlToken);
	if (!attempt || !attempt.tokenRecord) {
		// Invalid or expired token, redirect to generate a new one (by loading without token)
		return redirect(302, '/verify-email?error=invalid_token');
	}

	return {
		token: urlToken, // Pass plaintext URL token to the form
		emailToVerify: attempt.tokenRecord.email
	};
};

export const actions: Actions = {
	verify: async (event) => {
		const { user, session } = event.locals;
		if (!user || !session) return fail(401, { verify: { message: 'Not authenticated' } });
		if (user.email_verified) return fail(403, { verify: { message: 'Email already verified' } });

		const formData = await event.request.formData();
		const urlToken = formData.get('token') as string | null; // Plaintext URL token from form
		const validation = verifyCodeSchema.safeParse(Object.fromEntries(formData));

		if (!validation.success) {
			return fail(400, {
				verify: {
					data: Object.fromEntries(formData),
					errors: validation.error.flatten().fieldErrors
				}
			});
		}
		const { code } = validation.data;

		if (!urlToken) {
			return fail(400, {
				verify: {
					data: Object.fromEntries(formData),
					errors: { root: ['Missing verification session token.'] }
				}
			});
		}

		const attempt = await getEmailVerificationAttemptData(urlToken);
		if (!attempt || !attempt.tokenRecord) {
			return fail(401, {
				verify: {
					data: Object.fromEntries(formData),
					errors: { root: ['Invalid or expired verification session.'] }
				}
			});
		}

		const hashedTokenId = attempt.tokenRecord.id;
		if (!otpVerificationBucket.check(hashedTokenId, 1)) {
			return fail(429, {
				verify: {
					data: Object.fromEntries(formData),
					errors: { root: ['Too many OTP attempts.'] }
				}
			});
		}
		otpVerificationBucket.consume(hashedTokenId, 1);

		const otpValid = await verifyEmailVerificationOtp(hashedTokenId, code);
		if (!otpValid) {
			return fail(400, {
				verify: {
					data: Object.fromEntries(formData),
					errors: { code: ['Incorrect code.'] }
				}
			});
		}

		await setUserEmailAsVerified(user.id); // Mark user's email as verified
		await invalidateEmailVerificationAttempt(hashedTokenId); // Clean up this attempt
		otpVerificationBucket.reset(hashedTokenId);

		// Check if user has WebAuthn credentials registered
		const userWebAuthnCredentials = await db.query.webauthn_credentials.findMany({
			where: (credentials, { eq }) => eq(credentials.user_id, user.id),
			limit: 1
		});

		if (userWebAuthnCredentials.length === 0) {
			redirect(302, '/app/settings/webauthn?setup_prompt=true');
		} else {
			redirect(302, '/app');
		}
	},
	resend: async (event) => {
		const { user } = event.locals;
		if (!user) return fail(401, { resend: { message: 'Not authenticated' } });
		if (user.email_verified) return fail(403, { resend: { message: 'Email already verified' } });

		const validation = resendCodeSchema.safeParse({}); // No fields to validate, but keeps pattern consistent
		if (!validation.success) {
			// This should not happen with an empty schema
			return fail(400, { resend: { errors: { root: ['Invalid request.'] } } });
		}

		if (!ResendRateLimit.check(user.id, 1)) {
			return fail(429, {
				resend: { errors: { root: ['Too many requests to resend. Please try again later.'] } }
			});
		}
		await ResendRateLimit.consume(user.id, 1);

		// Create a new attempt (old ones for this user.id and user.email are cleared by createEmailVerificationAttempt)
		const newAttempt = await createEmailVerificationAttempt(user.id, user.email);
		if (newAttempt) {
			sendVerificationEmail(
				user.email,
				newAttempt.plaintextUrlToken,
				newAttempt.plaintextOtp,
				event.url.origin
			);
			return { resend: { success: true, message: 'A new verification email has been sent.' } };
		} else {
			return fail(500, {
				resend: { errors: { root: ['Failed to create a new verification request.'] } }
			});
		}
	}
};
