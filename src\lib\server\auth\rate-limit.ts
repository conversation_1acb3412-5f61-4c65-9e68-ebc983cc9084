import { db } from '$lib/server/db';
import { expiring_rate_limit_buckets, rate_limit_buckets } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

export class RefillingTokenBucket {
	public capacity: number;
	public refillRatePerSecond: number;

	constructor(capacity: number, refillIntervalSeconds: number) {
		this.capacity = capacity;
		this.refillRatePerSecond = capacity / refillIntervalSeconds;
	}

	private async getBucket(key: string) {
		const bucket = await db.query.rate_limit_buckets.findFirst({
			where: eq(rate_limit_buckets.key, key)
		});
		return bucket;
	}

	private refill(bucket: { tokens: number; last_refilled_at: Date }): {
		tokens: number;
		last_refilled_at: Date;
	} {
		const now = new Date();
		const secondsSinceLastRefill = (now.getTime() - bucket.last_refilled_at.getTime()) / 1000;
		const tokensToAdd = Math.floor(secondsSinceLastRefill * this.refillRatePerSecond);

		if (tokensToAdd > 0) {
			return {
				tokens: Math.min(bucket.tokens + tokensToAdd, this.capacity),
				last_refilled_at: now
			};
		}
		return {
			tokens: bucket.tokens,
			last_refilled_at: bucket.last_refilled_at
		};
	}

	public async check(key: string, cost: number): Promise<boolean> {
		const bucket = await this.getBucket(key);

		if (!bucket) {
			return this.capacity >= cost;
		}

		const { tokens: refilledTokens } = this.refill(bucket);
		return refilledTokens >= cost;
	}

	public async consume(key: string, cost: number): Promise<boolean> {
		const bucket = await this.getBucket(key);

		if (!bucket) {
			// Bucket doesn't exist, create a new one.
			if (this.capacity < cost) {
				return false; // Not enough capacity for the initial consumption
			}
			await db
				.insert(rate_limit_buckets)
				.values({
					key: key,
					tokens: this.capacity - cost,
					last_refilled_at: new Date()
				})
				.onConflictDoNothing(); // It might have been created by a concurrent request
			return true;
		}

		const { tokens: refilledTokens, last_refilled_at: newRefillTime } = this.refill(bucket);

		if (refilledTokens < cost) {
			return false; // Not enough tokens to consume
		}

		// Sufficient tokens, perform consumption and update the bucket
		await db
			.update(rate_limit_buckets)
			.set({
				tokens: refilledTokens - cost,
				last_refilled_at: newRefillTime
			})
			.where(eq(rate_limit_buckets.key, key));

		return true;
	}
}

export class Throttler<_Key> {
	public timeoutSeconds: number[];

	private storage = new Map<_Key, ThrottlingCounter>();

	constructor(timeoutSeconds: number[]) {
		this.timeoutSeconds = timeoutSeconds;
	}

	public consume(key: _Key): boolean {
		let counter = this.storage.get(key) ?? null;
		const now = Date.now();
		if (counter === null) {
			counter = {
				timeout: 0,
				updatedAt: now
			};
			this.storage.set(key, counter);
			return true;
		}
		const allowed = now - counter.updatedAt >= this.timeoutSeconds[counter.timeout] * 1000;
		if (!allowed) {
			return false;
		}
		counter.updatedAt = now;
		counter.timeout = Math.min(counter.timeout + 1, this.timeoutSeconds.length - 1);
		this.storage.set(key, counter);
		return true;
	}

	public reset(key: _Key): void {
		this.storage.delete(key);
	}
}

export class ExpiringTokenBucket<Key extends string> {
	public max: number;
	public expiresInSeconds: number;

	constructor(max: number, expiresInSeconds: number) {
		this.max = max;
		this.expiresInSeconds = expiresInSeconds;
	}

	private async getBucket(key: Key) {
		return db.query.expiring_rate_limit_buckets.findFirst({
			where: eq(expiring_rate_limit_buckets.key, key)
		});
	}

	public async check(key: Key, cost: number): Promise<boolean> {
		const bucket = await this.getBucket(key);
		const now = new Date();
		if (bucket === null || bucket === undefined) {
			return true;
		}
		if (now.getTime() - bucket.created_at.getTime() >= this.expiresInSeconds * 1000) {
			return true;
		}
		return bucket.count >= cost;
	}

	public async consume(key: Key, cost: number): Promise<boolean> {
		const bucket = await this.getBucket(key);
		const now = new Date();

		if (bucket && now.getTime() - bucket.created_at.getTime() >= this.expiresInSeconds * 1000) {
			// Bucket exists but is expired, so we can delete it and proceed
			await this.reset(key);
			// After deleting, there's no bucket, so we can create a new one below.
		}

		const currentBucket = await this.getBucket(key); // Re-fetch in case it was deleted

		if (currentBucket === null || currentBucket === undefined) {
			if (this.max < cost) {
				return false;
			}
			await db
				.insert(expiring_rate_limit_buckets)
				.values({
					key: key,
					count: this.max - cost,
					created_at: now
				})
				.onConflictDoUpdate({
					target: expiring_rate_limit_buckets.key,
					set: {
						count: this.max - cost,
						created_at: now
					}
				});
			return true;
		}

		if (currentBucket.count < cost) {
			return false;
		}
		await db
			.update(expiring_rate_limit_buckets)
			.set({
				count: currentBucket.count - cost
			})
			.where(eq(expiring_rate_limit_buckets.key, key));
		return true;
	}

	public async reset(key: Key): Promise<void> {
		await db.delete(expiring_rate_limit_buckets).where(eq(expiring_rate_limit_buckets.key, key));
	}
}

interface RefillBucket {
	count: number;
	refilledAt: number;
}

interface ExpiringBucket {
	count: number;
	createdAt: number;
}

interface ThrottlingCounter {
	timeout: number;
	updatedAt: number;
}
