import { describe, expect, it } from 'vitest';
import { getBase64ImageSize, isValidBase64Image } from './imageProcessing';

describe('Image Processing Utils', () => {
	const validBase64Image = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';
	const invalidBase64Image = 'not-a-valid-image';

	describe('isValidBase64Image', () => {
		it('should return true for valid base64 image', () => {
			expect(isValidBase64Image(validBase64Image)).toBe(true);
		});

		it('should return false for invalid base64 image', () => {
			expect(isValidBase64Image(invalidBase64Image)).toBe(false);
		});

		it('should return false for empty string', () => {
			expect(isValidBase64Image('')).toBe(false);
		});

		it('should return false for non-image data URL', () => {
			expect(isValidBase64Image('data:text/plain;base64,SGVsbG8gV29ybGQ=')).toBe(false);
		});
	});

	describe('getBase64ImageSize', () => {
		it('should return correct size for valid base64 image', () => {
			const size = getBase64ImageSize(validBase64Image);
			expect(size).toBeGreaterThan(0);
		});

		it('should return 0 for invalid base64 image', () => {
			// The function returns the size of the string itself if it's not a valid data URL
			// This is expected behavior since it tries to decode whatever is after the prefix
			expect(getBase64ImageSize(invalidBase64Image)).toBeGreaterThan(0);
		});

		it('should return 0 for empty string', () => {
			expect(getBase64ImageSize('')).toBe(0);
		});
	});
});
