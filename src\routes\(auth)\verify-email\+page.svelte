<!--
This Svelte component handles email verification.
It expects a token in the URL (managed by +page.server.ts for initial load, or from form actions for resend/verify).
The component displays different UI based on the verification status (data.error, data.emailToVerify).
If there's an email to verify, it shows a form to enter the 8-digit code and a button to resend the code.
It uses Svelte Runes for reactivity and $props() to access form data and page data.
Error/success messages from server actions (form?.verify?.message, form?.resend?.message) are displayed.
-->
<script lang="ts">
	import { enhance } from '$app/forms';
	import Errors from '$lib/runes-form/Errors.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { resendCodeSchema, verifyCodeSchema } from '$lib/schemas/auth';
	import { toast } from '$lib/ui/toast';
	import type { ActionData, PageData } from './$types.js';

	let { data, form: actionForm }: { data: PageData; form?: ActionData } = $props();

	const verifyForm = createRuneForm(verifyCodeSchema, {
		code:
			actionForm?.verify && 'data' in actionForm.verify
				? (actionForm.verify.data?.code?.toString() ?? '')
				: ''
	});
	const resendForm = createRuneForm(resendCodeSchema, {});

	$effect(() => {
		const verifyAction = actionForm?.verify;
		if (verifyAction && 'errors' in verifyAction && verifyAction.errors) {
			verifyForm.setErrors(verifyAction.errors);
			const fieldErrors = Object.keys(verifyAction.errors).filter((k) => k !== 'root').length;
			if (fieldErrors > 0) {
				toast.error('Please correct the code.');
			}
		}

		const resendAction = actionForm?.resend;
		if (resendAction) {
			if ('success' in resendAction && resendAction.success) {
				toast.success(resendAction.message);
			} else if ('errors' in resendAction && resendAction.errors?.root) {
				toast.error(resendAction.errors.root[0]);
			}
		}
	});
</script>

<div
	class="hero min-h-screen bg-cover bg-center"
	style="background-image: url('/theme/auth/Background.webp');"
	data-testid="verify-email-page"
>
	<div class="hero-content flex-col">
		<div class="text-center">
			<h1 class="text-5xl font-bold text-base-content" data-testid="verify-email-page-title">
				Verify Your Email Address
			</h1>
		</div>

		{#if data.error}
			{#if data.error === 'Too many requests to send verification email. Please try again later.'}
				<p class="py-4 text-base-content/80" data-testid="verify-email-too-many-requests-message">
					You can try sending a new code again after some time.
				</p>
			{:else if data.error === 'Failed to create a verification request. Please try again.' || data.error === 'invalid_token'}
				<div
					class="card glass bg-base-100/50 mt-4 w-full max-w-sm shrink-0 shadow-xl backdrop-blur-sm"
					data-testid="verify-email-try-resend-card"
				>
					<RuneForm form={resendForm}>
						{#snippet children(form)}
							<form
								class="card-body"
								method="post"
								action="?/resend"
								use:enhance={() => {
									form.setSubmitting(true);
									return async ({ update }) => {
										await update();
										form.setSubmitting(false);
									};
								}}
								onsubmit={form.handleSubmit()}
								data-testid="verify-email-try-resend-form"
							>
								<div class="form-control">
									<button
										class="btn btn-primary"
										data-testid="verify-email-try-resend-button"
										disabled={form.isSubmitting}
									>
										{#if form.isSubmitting}
											<span class="loading loading-spinner"></span>
										{/if}
										Try sending a new code
									</button>
								</div>
							</form>
						{/snippet}
					</RuneForm>
				</div>
			{/if}
		{:else if data.emailToVerify}
			<p class="py-4 text-base-content/80" data-testid="verify-email-instruction-message">
				We sent an 8-digit code to <strong data-testid="verify-email-address-display"
					>{data.emailToVerify}</strong
				>. Please check your inbox.
			</p>
			<div
				class="card glass bg-base-100/50 w-full max-w-sm shrink-0 shadow-xl backdrop-blur-sm"
				data-testid="verify-email-form-card"
			>
				<RuneForm form={verifyForm}>
					{#snippet children(form)}
						<form
							class="card-body"
							method="post"
							action="?/verify"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ update }) => {
									await update();
									form.setSubmitting(false);
								};
							}}
							onsubmit={form.handleSubmit()}
							data-testid="verify-email-form"
						>
							<input
								type="hidden"
								name="token"
								value={data.token}
								data-testid="verify-email-token-input"
							/>
							<div class="form-control">
								<label class="label" for="form-verify.code">
									<span class="label-text">Verification Code</span>
								</label>
								<label
									class="input input-bordered flex items-center gap-2"
									data-testid="verify-email-code-group"
								>
									<!-- hashtag icon -->
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="16"
										height="16"
										fill="currentColor"
										viewBox="0 0 24 24"
										class="opacity-70"
										><path
											d="M10 15h4v-2h-4v2zm-2 4h8v-2h-8v2zm0-6h8v-2h-8v2zm0-6h8V5h-8v2z"
										/></svg
									>
									<input
										id="form-verify.code"
										name="code"
										class="grow"
										required
										placeholder="8-digit code"
										data-testid="verify-email-code-input"
										bind:value={form.values.code}
										onblur={() => form.setTouched('code', true)}
									/>
								</label>
								<Errors name="code" />
							</div>

							<div class="mt-2">
								<Errors name="root" />
							</div>

							<div class="form-control mt-6">
								<button
									class="btn btn-primary"
									data-testid="verify-email-submit-button"
									disabled={form.isSubmitting}
								>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
									{/if}
									Verify
								</button>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			</div>

			<div class="divider" data-testid="verify-email-options-divider">OR</div>

			<div
				class="card glass bg-base-100/50 w-full max-w-sm shrink-0 shadow-xl backdrop-blur-sm"
				data-testid="verify-email-resend-card"
			>
				<RuneForm form={resendForm}>
					{#snippet children(form)}
						<form
							class="card-body"
							method="post"
							action="?/resend"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ update }) => {
									await update();
									form.setSubmitting(false);
								};
							}}
							onsubmit={form.handleSubmit()}
							data-testid="verify-email-resend-form"
						>
							<div class="form-control">
								<button
									class="btn btn-secondary"
									data-testid="verify-email-resend-button"
									disabled={form.isSubmitting}
								>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
									{/if}
									Resend code</button
								>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			</div>
		{:else}
			<div
				class="card glass bg-base-100/50 mt-4 w-full max-w-sm shrink-0 shadow-xl backdrop-blur-sm"
				data-testid="verify-email-loading-status-card"
			>
				<div class="card-body items-center text-center">
					<p class="py-4 text-base-content/80" data-testid="verify-email-loading-message">
						Loading verification status...
					</p>
					<span class="loading loading-dots loading-lg" data-testid="verify-email-loading-spinner"
					></span>
				</div>
			</div>
		{/if}

		<div class="mt-6 text-center">
			<a
				href="/app/settings"
				class="link link-hover text-base-content/80"
				data-testid="verify-email-back-to-settings-link">Change your email settings</a
			>
		</div>
	</div>
</div>
