import { goto, invalidateAll } from '$app/navigation';
import { toast } from '$lib/ui/toast';
import { startAuthentication, startRegistration, WebAuthnError } from '@simplewebauthn/browser';

/**
 * Initiates the passkey registration process.
 * @param name - The name for the new passkey (e.g., device name).
 */
export async function registerPasskey(name: string): Promise<void> {
	try {
		// 1. Get registration options from the server
		const response = await fetch('/api/auth/webauthn/register');
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || error.error || 'Failed to get registration options.');
		}
		const options = await response.json();

		// 2. Use browser WebAuthn API to create credentials
		const registrationResponse = await startRegistration({ optionsJSON: options });

		// 3. Verify the registration with the server
		const verifyResponse = await fetch('/api/auth/webauthn/register', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ name, response: registrationResponse })
		});

		if (!verifyResponse.ok) {
			const error = await verifyResponse.json();
			throw new Error(error.message || error.error || 'Verification failed.');
		}

		const result = await verifyResponse.json();
		if (result.success) {
			toast.success('Passkey registered successfully!');
			await invalidateAll();
		} else {
			throw new Error(result.message || 'Registration failed');
		}
	} catch (error) {
		const toastMessage =
			error instanceof WebAuthnError && error.code === 'ERROR_CEREMONY_ABORTED'
				? 'Registration was cancelled.'
				: error instanceof Error
					? error.message
					: 'An unknown error occurred.';
		toast.error(toastMessage);
		console.error('WebAuthn registration error:', error);
		throw error; // Re-throw for component-level handling if needed
	}
}

/**
 * Initiates the passkey login process.
 */
export async function loginWithPasskey(): Promise<void> {
	try {
		// 1. Get authentication options from the server
		const response = await fetch('/api/auth/webauthn/login', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({})
		});
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || error.error || 'Authentication failed');
		}
		const { options } = await response.json();

		// 2. Use browser WebAuthn API to get credentials
		const authenticationResponse = await startAuthentication({ optionsJSON: options });

		// 3. Verify the authentication with the server
		const verifyResponse = await fetch('/api/auth/webauthn/login', {
			method: 'PUT',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify(authenticationResponse)
		});

		if (!verifyResponse.ok) {
			const error = await verifyResponse.json();
			throw new Error(error.message || error.error || 'Verification failed');
		}

		// Success - redirect to app
		await goto('/app');
		await invalidateAll();
	} catch (error) {
		if (error instanceof WebAuthnError && error.code === 'ERROR_CEREMONY_ABORTED') {
			// Do nothing, user cancelled the action.
			return;
		}
		const message = error instanceof Error ? error.message : 'Login failed. Please try again.';
		toast.error(message);
		console.error('WebAuthn login error:', error);
		throw error;
	}
}

/**
 * Authenticates a user with a passkey.
 * @returns The result of the passkey authentication.
 */
export async function authenticatePasskey(): Promise<PublicKeyCredential> {
	try {
		// 1. Get authentication options from the server
		const res = await fetch('/api/auth/webauthn/generate-authentication-options', {
			method: 'POST'
		});

		if (!res.ok) {
			const errorData = await res.json();
			throw new Error(errorData.message || 'Failed to get authentication options.');
		}

		const options: PublicKeyCredentialRequestOptions = await res.json();

		// 2. Use WebAuthn API to authenticate
		const authentication = await navigator.credentials.get({
			publicKey: options
		});

		if (!authentication) {
			throw new Error('Authentication was cancelled or failed.');
		}

		return authentication as PublicKeyCredential;
	} catch (error) {
		console.error('Passkey authentication failed:', error);
		toast.error('Could not authenticate with passkey. Please try again.');
		throw error;
	}
}
