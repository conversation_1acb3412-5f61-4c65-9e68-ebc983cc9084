<!--
Svelte component for Account Recovery.
Allows users to recover their account via password reset or by using a recovery code.
-->
<script lang="ts">
	import { applyAction, enhance } from '$app/forms';
	import Errors from '$lib/runes-form/Errors.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { forgotPasswordSchema, useRecoveryCodeSchema } from '$lib/schemas/auth';
	import { toast } from '$lib/ui/toast';
	import type { ActionData } from './$types.js';

	let { form: actionForm }: { form?: ActionData } = $props();

	const forgotPasswordForm = createRuneForm(forgotPasswordSchema, {
		email:
			(actionForm?.forgotPassword &&
				'data' in actionForm.forgotPassword &&
				actionForm.forgotPassword.data?.email?.toString()) ||
			''
	});

	const recoveryCodeForm = createRuneForm(useRecoveryCodeSchema, {
		email:
			(actionForm?.useRecoveryCode &&
				'data' in actionForm.useRecoveryCode &&
				actionForm.useRecoveryCode.data?.email?.toString()) ||
			'',
		recovery_code:
			(actionForm?.useRecoveryCode &&
				'data' in actionForm.useRecoveryCode &&
				actionForm.useRecoveryCode.data?.recovery_code?.toString()) ||
			''
	});

	$effect(() => {
		// Handle forgot password form
		const fpAction = actionForm?.forgotPassword;
		if (fpAction) {
			if ('errors' in fpAction && fpAction.errors) {
				forgotPasswordForm.setErrors(fpAction.errors);
				toast.error('Please correct the errors in the password reset form.');
			} else if ('message' in fpAction) {
				if ('success' in fpAction && fpAction.success) {
					toast.success(fpAction.message);
					forgotPasswordForm.reset();
				} else {
					toast.error(fpAction.message);
				}
			}
		}

		// Handle recovery code form
		const rcAction = actionForm?.useRecoveryCode;
		if (rcAction) {
			if ('errors' in rcAction && rcAction.errors) {
				recoveryCodeForm.setErrors(rcAction.errors);
				toast.error('Please correct the errors in the recovery code form.');
			} else if ('message' in rcAction) {
				toast.error(rcAction.message);
			}
		}
	});
</script>

<div
	class="hero min-h-screen bg-cover bg-center"
	style="background-image: url('/theme/auth/Background.webp');"
	data-testid="account-recovery-page"
>
	<div class="hero-content w-full max-w-md flex-col px-4">
		<div class="mb-8 text-center">
			<h1 class="text-4xl font-bold text-base-content" data-testid="account-recovery-title">
				Account Recovery
			</h1>
			<p class="py-4 text-base-content/80" data-testid="account-recovery-instruction">
				If you've lost access to your account, you can reset your password or use a recovery code.
			</p>
		</div>

		<!-- Forgot Password Section -->
		<div
			class="card glass bg-base-100/50 mb-8 w-full shrink-0 shadow-xl backdrop-blur-sm"
			data-testid="forgot-password-section"
		>
			<RuneForm form={forgotPasswordForm}>
				{#snippet children(form)}
					<form
						class="card-body"
						method="post"
						use:enhance={() => {
							form.setSubmitting(true);
							return async ({ result }) => {
								await applyAction(result);
								form.setSubmitting(false);
							};
						}}
						action="?/forgotPassword"
						onsubmit={form.handleSubmit()}
						data-testid="forgot-password-form"
					>
						<h2 class="card-title mb-1 text-xl" data-testid="forgot-password-section-title">
							Forgot Your Password?
						</h2>
						<p
							class="text-base-content/70 mb-4 text-sm"
							data-testid="forgot-password-section-instruction"
						>
							Enter your email address below. We'll send you a link to reset your password.
						</p>
						<div class="form-control">
							<label class="label" for="form-forgot-password-email">
								<span class="label-text">Email Address</span>
							</label>
							<div
								class="input input-bordered flex items-center gap-2"
								data-testid="forgot-password-email-group"
							>
								<!-- envelope icon -->
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									fill="currentColor"
									viewBox="0 0 24 24"
									class="opacity-70"
									><path
										d="M20 4H4c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h16c1.103 0 2-.897 2-2V6c0-1.103-.897-2-2-2zm0 2v.511l-8 5.053-8-5.053V6h16zM4 18V8.489l7.447 4.703c.322.203.784.203 1.106 0L20 8.489V18H4z"
									/></svg
								>
								<input
									type="email"
									id="form-forgot-password-email"
									name="email"
									class="grow"
									required
									bind:value={form.values.email}
									onblur={() => form.setTouched('email', true)}
									data-testid="forgot-password-email-input"
								/>
							</div>
							<Errors name="email" />
						</div>
						<div class="form-control mt-6">
							<button
								class="btn btn-primary"
								disabled={form.isSubmitting}
								data-testid="forgot-password-submit-button"
							>
								{#if form.isSubmitting}
									<span class="loading loading-spinner"></span>
									Sending...
								{:else}
									Send Reset Link
								{/if}
							</button>
						</div>
					</form>
				{/snippet}
			</RuneForm>
		</div>

		<div class="divider" data-testid="recovery-options-divider">OR</div>

		<!-- Use Recovery Code Section -->
		<div
			class="card glass bg-base-100/50 mt-8 w-full shrink-0 shadow-xl backdrop-blur-sm"
			data-testid="use-recovery-code-section"
		>
			<RuneForm form={recoveryCodeForm}>
				{#snippet children(form)}
					<form
						class="card-body"
						method="post"
						use:enhance={() => {
							form.setSubmitting(true);
							return async ({ result }) => {
								await applyAction(result);
								form.setSubmitting(false);
							};
						}}
						action="?/useRecoveryCode"
						onsubmit={form.handleSubmit()}
						data-testid="use-recovery-code-form"
					>
						<h2 class="card-title mb-1 text-xl" data-testid="use-recovery-code-section-title">
							Use a Recovery Code
						</h2>
						<p
							class="text-base-content/70 mb-4 text-sm"
							data-testid="use-recovery-code-section-instruction"
						>
							If you have a recovery code, enter your email and the code below to sign in.
						</p>
						<div class="form-control">
							<label class="label" for="form-recovery-code-email">
								<span class="label-text">Email Address</span>
							</label>
							<div
								class="input input-bordered flex items-center gap-2"
								data-testid="recovery-code-email-group"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									fill="currentColor"
									viewBox="0 0 24 24"
									class="opacity-70"
									><path
										d="M20 4H4c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h16c1.103 0 2-.897 2-2V6c0-1.103-.897-2-2-2zm0 2v.511l-8 5.053-8-5.053V6h16zM4 18V8.489l7.447 4.703c.322.203.784.203 1.106 0L20 8.489V18H4z"
									/></svg
								>
								<input
									type="email"
									id="form-recovery-code-email"
									name="email"
									class="grow"
									required
									bind:value={form.values.email}
									onblur={() => form.setTouched('email', true)}
									data-testid="recovery-code-email-input"
								/>
							</div>
							<Errors name="email" />
						</div>

						<div class="form-control mt-4">
							<label class="label" for="form-recovery-code-code">
								<span class="label-text">Recovery Code</span>
							</label>
							<div
								class="input input-bordered flex items-center gap-2"
								data-testid="recovery-code-group"
							>
								<!-- hashtag icon -->
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									fill="currentColor"
									viewBox="0 0 24 24"
									class="opacity-70"
									><path d="M10 15h4v-2h-4v2zm-2 4h8v-2h-8v2zm0-6h8v-2h-8v2zm0-6h8V5h-8v2z" /></svg
								>
								<input
									type="text"
									id="form-recovery-code-code"
									name="recovery_code"
									class="grow"
									required
									placeholder="XXXX-XXXX-XXXX"
									autocomplete="off"
									bind:value={form.values.recovery_code}
									onblur={() => form.setTouched('recovery_code', true)}
									data-testid="recovery-code-input"
								/>
							</div>
							<Errors name="recovery_code" />
						</div>

						<div class="form-control mt-6">
							<button
								class="btn btn-primary"
								disabled={form.isSubmitting}
								data-testid="recovery-code-submit-button"
							>
								{#if form.isSubmitting}
									<span class="loading loading-spinner"></span>
									Signing In...
								{:else}
									Sign In With Code
								{/if}
							</button>
						</div>
					</form>
				{/snippet}
			</RuneForm>
		</div>

		<div class="mt-8 text-center">
			<a
				href="/login"
				class="link link-primary text-base-content/80"
				data-testid="account-recovery-back-to-login-link">Back to Login</a
			>
		</div>
	</div>
</div>

<svelte:head>
	<!-- ... existing code ... -->
</svelte:head>
