import { sha256 } from '@oslojs/crypto/sha2';
import { encodeHexLowerCase } from '@oslojs/encoding';
import { generateRandomOTP, generateRandomUrlToken, hashOtp } from './utils';

/**
 * Represents a complete token pair for authentication flows
 */
export interface TokenPair {
	plaintextUrlToken: string;
	hashedUrlTokenId: string;
	plaintextOtp: string;
	hashedOtpForDb: string;
	expiresAt: Date;
}

/**
 * Configuration for token creation
 */
export interface TokenConfig {
	durationMinutes: number;
	urlTokenByteLength?: number;
}

/**
 * Creates a complete token pair for authentication flows (email verification, password reset, etc.)
 * This eliminates code duplication across different auth modules.
 * 
 * @param config - Token configuration including duration and optional byte length
 * @returns Promise<TokenPair> - Complete token pair with plaintext and hashed versions
 */
export async function createTokenPair(config: TokenConfig): Promise<TokenPair> {
	const { durationMinutes, urlTokenByteLength = 24 } = config;

	// Generate URL token (for links)
	const plaintextUrlToken = generateRandomUrlToken(urlTokenByteLength);
	const hashedUrlTokenId = encodeHexLowerCase(sha256(new TextEncoder().encode(plaintextUrlToken)));

	// Generate OTP (for user input)
	const plaintextOtp = generateRandomOTP();
	const hashedOtpForDb = await hashOtp(plaintextOtp);

	// Calculate expiration
	const expiresAt = new Date(Date.now() + 1000 * 60 * durationMinutes);

	return {
		plaintextUrlToken,
		hashedUrlTokenId,
		plaintextOtp,
		hashedOtpForDb,
		expiresAt
	};
}

/**
 * Validates if a token has expired
 * @param expiresAt - Token expiration date
 * @returns boolean - true if expired
 */
export function isTokenExpired(expiresAt: Date): boolean {
	return Date.now() >= expiresAt.getTime();
}

/**
 * Hashes a plaintext URL token for database lookup
 * @param plaintextToken - The plaintext token to hash
 * @returns string - Hashed token for database queries
 */
export function hashUrlToken(plaintextToken: string): string {
	return encodeHexLowerCase(sha256(new TextEncoder().encode(plaintextToken)));
}

/**
 * Common token durations used throughout the application
 */
export const TOKEN_DURATIONS = {
	EMAIL_VERIFICATION: 10, // minutes
	PASSWORD_RESET: 10, // minutes
	MAGIC_LINK: 15, // minutes
	TWO_FACTOR: 5 // minutes
} as const;
