import { forgotPasswordSchema } from '$lib/schemas/auth';
import {
	createPasswordResetAttempt,
	sendPasswordResetEmail
} from '$lib/server/auth/password-reset';
import { RefillingTokenBucket } from '$lib/server/auth/rate-limit';
import { getUserFromEmail } from '$lib/server/auth/user';
import { fail } from '@sveltejs/kit';
import type { Actions } from './$types.js';

// TODO: Replace this in-memory rate limiter with a more robust, persistent solution
// like Redis. The current implementation will reset on every server restart,
// making it easy to bypass.
const ipBucket = new RefillingTokenBucket(3, 60);
const userBucket = new RefillingTokenBucket(3, 60);

export const actions: Actions = {
	default: async ({ request, url }) => {
		const formData = Object.fromEntries(await request.formData());
		const validation = forgotPasswordSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				data: formData,
				errors: validation.error.flatten().fieldErrors
			});
		}

		const { email } = validation.data;

		try {
			const user = await getUserFromEmail(email);
			if (user) {
				const attempt = await createPasswordResetAttempt(user.id, user.email);
				if (attempt) {
					// In a real app, you would use a proper email sending service
					// and construct a full URL for the reset link.
					sendPasswordResetEmail(
						email,
						attempt.plaintextUrlToken,
						attempt.plaintextOtp,
						url.origin
					);
				}
			}
		} catch (e) {
			console.error('Error during password reset process:', e);
			// Fail silently to the user
		}

		return {
			success: true,
			message:
				"If an account with that email exists, we've sent instructions to reset your password."
		};
	}
};
