{"name": "hairloom", "private": true, "version": "0.3.1", "type": "module", "scripts": {"dev": "vite dev --host", "build": "vite build", "postbuild": "echo 'Build completed. Run npm run db:release for database setup.'", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "npm run test:integration && npm run test:unit", "test:integration": "playwright test", "test:unit": "vitest", "test:e2e": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test", "test:e2e:headed": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test --headed", "test:e2e:debug": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test --debug", "test:e2e:ui": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test --ui", "db:start": "docker compose up", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "tsx src/lib/server/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/lib/server/db/seed/seed.ts", "db:seed:prod": "tsx src/lib/server/db/seed/seed.prod.ts", "db:release": "npm run db:migrate && npm run db:seed:prod", "db:reset": "tsx src/lib/server/db/reset.ts", "db:reseed": "npm run db:reset && npm run db:migrate && npm run db:seed"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/js": "^9.29.0", "@iconify-json/icon-park": "^1.2.2", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify-json/mdi": "^1.2.3", "@playwright/test": "^1.53.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.21.5", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/sharp": "^0.31.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-basic-ssl": "^2.0.0", "cross-env": "^7.0.3", "daisyui": "^5.0.43", "dotenv": "^16.5.0", "effect": "^3.16.9", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.3", "fast-check": "^4.1.1", "globals": "^16.2.0", "jsdom": "^26.1.0", "prettier": "^3.6.0", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.13", "svelte": "^5.34.7", "svelte-check": "^4.2.2", "svelte-confetti": "^2.3.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "unplugin-icons": "^22.1.0", "vite": "^6.3.5", "vitest": "^3.2.4", "zod-to-json-schema": "^3.24.5"}, "dependencies": {"@icon-park/svg": "^1.4.2", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@oslojs/otp": "^1.1.0", "@paralleldrive/cuid2": "^2.2.2", "@simplewebauthn/browser": "^13.1.0", "@simplewebauthn/server": "^13.1.1", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@vercel/postgres": "^0.10.0", "@vercel/speed-insights": "^1.2.0", "@xenova/transformers": "^2.17.2", "@zxing/library": "^0.21.3", "arctic": "^3.7.0", "autoprefixer": "^10.4.21", "bip39": "^3.1.0", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "immer": "^10.1.1", "jose": "^6.0.12", "just-safe-get": "^4.2.0", "just-safe-set": "^4.2.1", "nodemailer": "^7.0.3", "postgres": "^3.4.7", "resend": "^4.6.0", "sharp": "^0.34.3", "svelte-sonner": "^1.0.5", "sveltekit-superforms": "^2.27.0", "tailwindcss": "^4.1.11", "uuid": "^11.1.0", "zod": "^3.25.67", "zod-form-data": "^3.0.0"}}