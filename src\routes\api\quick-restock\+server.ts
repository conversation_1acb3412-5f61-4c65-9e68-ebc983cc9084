import { db } from '$lib/server/db';
import { inventory_items, shopping_list_items } from '$lib/server/db/schema';
import {
    createErrorResponse,
    createJsonResponse,
    handleValidationError,
    withAuthAndErrorHandling
} from '$lib/server/utils/api-response';
import { json } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import { z } from 'zod/v4';
import type { RequestHandler } from './$types';

const quickRestockSchema = z.object({
	barcode: z.string().trim().min(1, 'Barcode is required'),
	quantity: z.coerce.number().int().min(1).optional() // Optional override quantity
});

export const POST: RequestHandler = withAuthAndErrorHandling(async ({ request, locals }) => {
	// User is guaranteed to exist due to withAuthAndErrorHandling
	const user = locals.user!;

	const body = await request.json();
	const validation = quickRestockSchema.safeParse(body);

	if (!validation.success) {
		const { response, status } = handleValidationError(validation.error);
		return json(response, { status });
	}

	const { barcode, quantity: overrideQuantity } = validation.data;

	// Additional validation for empty barcode after trimming
	if (!barcode || barcode.length === 0) {
		return createErrorResponse('Barcode cannot be empty', 'BAD_REQUEST', undefined, 400);
		}

		// Find the inventory item by barcode
		const inventoryItem = await db
			.select()
			.from(inventory_items)
			.where(
				and(
					eq(inventory_items.barcode, barcode),
					eq(inventory_items.user_id, user.id)
				)
			)
			.limit(1);

		if (inventoryItem.length === 0) {
			return createErrorResponse(
				'Item not found with this barcode. Please check the barcode and try again, or add this item to your inventory first.',
				'NOT_FOUND',
				undefined,
				404
			);
		}

		const item = inventoryItem[0];

		// Determine quantity to add to shopping list
		let quantityToAdd: number;
		
		if (overrideQuantity) {
			// Use provided quantity
			quantityToAdd = overrideQuantity;
		} else if (item.reorder_threshold !== null && item.reorder_threshold > 0) {
			// Use reorder threshold
			quantityToAdd = item.reorder_threshold;
		} else {
			// No reorder threshold set - return item info for user to specify quantity
			return createJsonResponse({
				needsQuantity: true,
				item: {
					id: item.id,
					name: item.name,
					description: item.description,
					barcode: item.barcode,
					current_quantity: item.quantity,
					reorder_threshold: item.reorder_threshold
				}
			});
		}

		// Start transaction to ensure atomicity
		await db.transaction(async (tx) => {
			// Zero out the inventory quantity
			await tx
				.update(inventory_items)
				.set({
					quantity: 0,
					updated_at: new Date()
				})
				.where(eq(inventory_items.id, item.id));

			// Check if item already exists in shopping list
			const existingShoppingListItem = await tx
				.select()
				.from(shopping_list_items)
				.where(
					and(
						eq(shopping_list_items.user_id, user.id),
						eq(shopping_list_items.inventory_item_id, item.id)
					)
				)
				.limit(1);

			if (existingShoppingListItem.length > 0) {
				// Update existing shopping list item quantity
				await tx
					.update(shopping_list_items)
					.set({
						quantity: existingShoppingListItem[0].quantity + quantityToAdd,
						purchased: false // Reset purchased status when adding more
					})
					.where(eq(shopping_list_items.id, existingShoppingListItem[0].id));
			} else {
				// Create new shopping list item
				await tx
					.insert(shopping_list_items)
					.values({
						user_id: user.id,
						inventory_item_id: item.id,
						quantity: quantityToAdd
					});
			}
		});

		return createJsonResponse({
			success: true,
			item: {
				id: item.id,
				name: item.name,
				description: item.description,
				barcode: item.barcode,
				previous_quantity: item.quantity,
				quantity_added_to_shopping_list: quantityToAdd,
				used_reorder_threshold: !overrideQuantity && item.reorder_threshold !== null
			}
		}, 'Quick restock completed successfully');
});
