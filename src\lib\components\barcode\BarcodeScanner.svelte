<!--
  ALPHA FEATURE: Barcode Scanner Component
  A reusable barcode scanner component using @zxing/library

  Used by ALPHA FEATURES: Inventory Management and Shopping List for barcode scanning workflows.
  Provides mobile and desktop camera access with torch support, multiple camera selection,
  and comprehensive error handling.

  Props:
    - onScan: callback function called when a barcode is successfully scanned
    - onError: callback function called when an error occurs
    - isActive: boolean to control when scanning is active
    - formats: array of barcode formats to scan (optional)

  This component is part of the inventory management system that is currently in alpha status.
-->
<script lang="ts">
	import { toast } from '$lib/ui/toast';
	import { BrowserMultiFormatReader, NotFoundException } from '@zxing/library';
	import { onDestroy, onMount } from 'svelte';
	import IconCamera from '~icons/icon-park-outline/camera';
	import IconClose from '~icons/icon-park-outline/close';
	import IconFlashlight from '~icons/icon-park-outline/flashlight';

	interface Props {
		onScan: (result: string) => void;
		onError?: (error: string) => void;
		onClose?: () => void;
		isActive?: boolean;
		formats?: string[];
	}

	let {
		onScan,
		onError = () => {},
		onClose = () => {},
		isActive = true,
		formats = []
	}: Props = $props();

	let videoElement: HTMLVideoElement;
	let codeReader: BrowserMultiFormatReader;
	let scanning = $state(false);
	let error = $state('');

	/**
	 * Validate barcode content for security
	 * Prevents processing of potentially malicious QR codes or invalid barcodes
	 */
	function validateBarcodeContent(content: string): boolean {
		// Check length (reasonable barcode length)
		if (!content || content.length === 0 || content.length > 255) {
			return false;
		}

		// Allow alphanumeric characters, hyphens, underscores, and dots
		// This covers most standard barcode formats (UPC, EAN, Code128, etc.)
		const validBarcodePattern = /^[A-Za-z0-9\-_.]+$/;
		if (!validBarcodePattern.test(content)) {
			return false;
		}

		// Reject obvious attempts at code injection
		const dangerousPatterns = [
			/javascript:/i,
			/<script/i,
			/data:/i,
			/vbscript:/i,
			/on\w+=/i
		];

		for (const pattern of dangerousPatterns) {
			if (pattern.test(content)) {
				return false;
			}
		}

		return true;
	}
	let hasCamera = $state(false);
	let stream: MediaStream | null = null;
	let torchSupported = $state(false);
	let torchEnabled = $state(false);
	let cameras = $state<MediaDeviceInfo[]>([]);
	let currentCameraIndex = $state(0);
	let isInitializing = $state(true);

	onMount(async () => {
		try {
			// Check if mediaDevices is supported
			if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
				error = 'Camera access not supported in this browser';
				toast.error('Camera access is not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.');
				onError(error);
				isInitializing = false;
				return;
			}

			// Check if we're on HTTPS (required for camera access on most browsers)
			if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
				error = 'Camera access requires HTTPS';
				toast.error('Camera access requires a secure connection (HTTPS). Please access this page over HTTPS.');
				onError(error);
				isInitializing = false;
				return;
			}

			// Try to request camera permission by attempting to get user media
			// This is the most reliable way to check if camera is available and request permission
			let testStream: MediaStream | null = null;
			try {
				// Request basic camera access to trigger permission prompt
				testStream = await navigator.mediaDevices.getUserMedia({ video: true });

				// If we get here, permission was granted
				// Stop the test stream immediately
				testStream.getTracks().forEach(track => track.stop());
				testStream = null;

				// Now enumerate devices to get the actual camera list
				const devices = await navigator.mediaDevices.enumerateDevices();
				cameras = devices.filter(device => device.kind === 'videoinput');
				hasCamera = cameras.length > 0;

				// If no cameras found but permission was granted, there might be a camera but no labels
				// This can happen on some browsers/devices
				if (!hasCamera) {
					// Try to detect if there are video input devices without labels
					const videoInputs = devices.filter(device => device.kind === 'videoinput');
					if (videoInputs.length > 0) {
						hasCamera = true;
						cameras = videoInputs;
					}
				}

			} catch (permErr) {
				// Clean up test stream if it exists
				if (testStream) {
					testStream.getTracks().forEach(track => track.stop());
				}

				const errorMessage = (permErr as Error).message;
				console.error('Camera permission error:', permErr);

				if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
					error = 'Camera permission denied';
					toast.error('Camera permission denied. Please allow camera access when prompted and try again.');
				} else if (errorMessage.includes('NotFoundError') || errorMessage.includes('DevicesNotFoundError')) {
					error = 'No camera found on this device';
					toast.error('No camera found. Please ensure your device has a camera and try again.');
				} else {
					error = 'Failed to access camera: ' + errorMessage;
					toast.error('Failed to access camera. Please check your camera permissions and try again.');
				}

				onError(error);
				isInitializing = false;
				return;
			}

			if (!hasCamera) {
				error = 'No camera found on this device';
				toast.error('No camera found. Please ensure your device has a camera and try again.');
				onError(error);
				isInitializing = false;
				return;
			}

			// Initialize the code reader
			codeReader = new BrowserMultiFormatReader();

			if (isActive) {
				await startScanning();
			}

			isInitializing = false;
		} catch (err) {
			error = 'Failed to initialize camera: ' + (err as Error).message;
			toast.error('Failed to initialize camera. Please check your camera permissions and try again.');
			onError(error);
			isInitializing = false;
		}
	});

	onDestroy(() => {
		stopScanning();
	});

	async function startScanning() {
		if (!hasCamera || scanning) return;

		try {
			scanning = true;
			error = '';

			// Get video constraints with preference for back camera on mobile
			const currentCamera = cameras[currentCameraIndex];
			let constraints: MediaStreamConstraints;

			if (currentCamera && currentCamera.deviceId) {
				// Use specific camera if available
				constraints = {
					video: {
						deviceId: { exact: currentCamera.deviceId },
						facingMode: { ideal: 'environment' },
						width: { ideal: 1280 },
						height: { ideal: 720 }
					}
				};
			} else {
				// Fallback to basic constraints
				constraints = {
					video: {
						facingMode: { ideal: 'environment' },
						width: { ideal: 1280 },
						height: { ideal: 720 }
					}
				};
			}

			// Start video stream
			stream = await navigator.mediaDevices.getUserMedia(constraints);
			videoElement.srcObject = stream;

			// Wait for video to be ready and play if needed
			try {
				if (videoElement.paused) {
					await videoElement.play();
				}
			} catch (playError) {
				// Ignore "already playing" errors
				const errorMessage = playError instanceof Error ? playError.message : String(playError);
				if (!errorMessage.includes('already playing')) {
					console.warn('Video play error:', playError);
				}
			}

			// Check for torch support
			const track = stream.getVideoTracks()[0];
			const capabilities = track.getCapabilities();
			torchSupported = 'torch' in capabilities;

			// Start barcode detection
			await codeReader.decodeFromVideoDevice(null, videoElement, (result, err) => {
				if (result) {
					const scannedText = result.getText();
					// Validate barcode content for security
					if (validateBarcodeContent(scannedText)) {
						onScan(scannedText);
						stopScanning();
					} else {
						console.warn('Invalid barcode format detected:', scannedText);
						error = 'Invalid barcode format. Please try again.';
					}
				} else if (err && !(err instanceof NotFoundException)) {
					console.warn('Barcode scanning error:', err);
				}
			});

		} catch (err) {
			scanning = false;
			const errorMessage = (err as Error).message;
			error = 'Failed to start camera: ' + errorMessage;

			// Provide specific error messages based on the error type
			if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
				toast.error('Camera permission denied. Please refresh the page and allow camera access when prompted.');
			} else if (errorMessage.includes('NotFoundError') || errorMessage.includes('DevicesNotFoundError')) {
				toast.error('No camera found. Please ensure your device has a working camera.');
			} else if (errorMessage.includes('NotReadableError') || errorMessage.includes('TrackStartError')) {
				toast.error('Camera is already in use by another application. Please close other camera apps and try again.');
			} else if (errorMessage.includes('OverconstrainedError') || errorMessage.includes('ConstraintNotSatisfiedError')) {
				toast.error('Camera settings not supported. Trying with basic settings...');
				// Try again with basic constraints
				setTimeout(() => tryBasicConstraints(), 1000);
				return;
			} else if (errorMessage.includes('NotSupportedError')) {
				toast.error('Camera access not supported in this browser. Please use Chrome, Firefox, or Safari.');
			} else {
				toast.error('Failed to start camera. Please check your camera permissions and try again.');
			}

			onError(error);
		}
	}

	function stopScanning() {
		scanning = false;

		if (codeReader) {
			codeReader.reset();
		}

		if (stream) {
			stream.getTracks().forEach(track => track.stop());
			stream = null;
		}

		if (videoElement) {
			// Pause video before clearing srcObject
			try {
				videoElement.pause();
			} catch (pauseError) {
				// Ignore pause errors
			}
			videoElement.srcObject = null;
		}

		torchEnabled = false;
	}

	async function tryBasicConstraints() {
		if (!hasCamera || scanning) return;

		try {
			scanning = true;
			error = '';

			// Use very basic constraints as fallback
			const basicConstraints: MediaStreamConstraints = {
				video: true
			};

			// Start video stream with basic constraints
			stream = await navigator.mediaDevices.getUserMedia(basicConstraints);
			videoElement.srcObject = stream;

			// Wait for video to be ready and play if needed
			try {
				if (videoElement.paused) {
					await videoElement.play();
				}
			} catch (playError) {
				// Ignore "already playing" errors
				const errorMessage = playError instanceof Error ? playError.message : String(playError);
				if (!errorMessage.includes('already playing')) {
					console.warn('Video play error:', playError);
				}
			}

			// Start barcode detection
			await codeReader.decodeFromVideoDevice(null, videoElement, (result, err) => {
				if (result) {
					const scannedText = result.getText();
					// Validate barcode content for security
					if (validateBarcodeContent(scannedText)) {
						onScan(scannedText);
						stopScanning();
					} else {
						console.warn('Invalid barcode format detected:', scannedText);
						error = 'Invalid barcode format. Please try again.';
					}
				} else if (err && !(err instanceof NotFoundException)) {
					console.warn('Barcode scanning error:', err);
				}
			});

		} catch (err) {
			scanning = false;
			error = 'Failed to start camera with basic settings: ' + (err as Error).message;
			toast.error('Unable to access camera. Please check your device settings and try again.');
			onError(error);
		}
	}

	async function toggleTorch() {
		if (!stream || !torchSupported) return;

		try {
			const track = stream.getVideoTracks()[0];
			await track.applyConstraints({
				advanced: [{ torch: !torchEnabled } as any]
			});
			torchEnabled = !torchEnabled;
		} catch (err) {
			console.warn('Failed to toggle torch:', err);
		}
	}

	async function switchCamera() {
		if (cameras.length <= 1) return;

		stopScanning();
		currentCameraIndex = (currentCameraIndex + 1) % cameras.length;

		if (isActive) {
			await startScanning();
		}
	}

	function handleClose() {
		stopScanning();
		onClose();
	}

	function isMobileDevice() {
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	async function requestCameraPermission() {
		try {
			isInitializing = true;
			error = '';

			// Request camera permission
			const testStream = await navigator.mediaDevices.getUserMedia({ video: true });
			testStream.getTracks().forEach(track => track.stop());

			// Re-enumerate devices now that permission is granted
			const devices = await navigator.mediaDevices.enumerateDevices();
			cameras = devices.filter(device => device.kind === 'videoinput');
			hasCamera = cameras.length > 0;

			if (hasCamera) {
				toast.success('Camera access granted! You can now scan barcodes.');
				if (isActive) {
					await startScanning();
				}
			} else {
				error = 'No camera found on this device';
				toast.error('No camera found. Please ensure your device has a camera.');
			}

		} catch (err) {
			const errorMessage = (err as Error).message;
			error = 'Failed to access camera: ' + errorMessage;

			if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
				toast.error('Camera permission denied. Please check your browser settings to enable camera access for this site.');
			} else {
				toast.error('Failed to access camera. Please try again or check your device settings.');
			}
		} finally {
			isInitializing = false;
		}
	}

	// Watch isActive prop changes
	$effect(() => {
		if (isActive && hasCamera && !scanning) {
			startScanning();
		} else if (!isActive && scanning) {
			stopScanning();
		}
	});
</script>

<div class="fixed inset-0 z-50 bg-black">
	<!-- Mobile-Optimized Header -->
	<div class="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/90 to-transparent">
		<!-- Mobile Header -->
		<div class="flex items-center justify-between text-white p-4 md:hidden">
			<div class="flex items-center gap-3">
				<button
					type="button"
					class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
					onclick={handleClose}
					aria-label="Close scanner"
				>
					<IconClose class="h-6 w-6" />
				</button>
				<div>
					<h2 class="text-lg font-semibold">Scan Barcode</h2>
					<p class="text-xs text-white/70">Tap to close</p>
				</div>
			</div>
			<div class="flex items-center gap-2">
				{#if cameras.length > 1}
					<button
						type="button"
						class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
						onclick={switchCamera}
						aria-label="Switch camera"
					>
						<IconCamera class="h-5 w-5" />
					</button>
				{/if}
				{#if torchSupported}
					<button
						type="button"
						class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors {torchEnabled ? 'bg-white/30' : ''}"
						onclick={toggleTorch}
						aria-label="Toggle flashlight"
					>
						<IconFlashlight class="h-5 w-5" />
					</button>
				{/if}
			</div>
		</div>

		<!-- Desktop Header -->
		<div class="hidden md:flex items-center justify-between text-white p-6">
			<h2 class="text-xl font-semibold">Scan Barcode</h2>
			<div class="flex items-center gap-3">
				{#if cameras.length > 1}
					<button
						type="button"
						class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
						onclick={switchCamera}
						aria-label="Switch camera"
					>
						<IconCamera class="h-6 w-6" />
					</button>
				{/if}
				{#if torchSupported}
					<button
						type="button"
						class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors {torchEnabled ? 'bg-white/30' : ''}"
						onclick={toggleTorch}
						aria-label="Toggle flashlight"
					>
						<IconFlashlight class="h-6 w-6" />
					</button>
				{/if}
				<button
					type="button"
					class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
					onclick={handleClose}
					aria-label="Close scanner"
				>
					<IconClose class="h-6 w-6" />
				</button>
			</div>
		</div>
	</div>

	<!-- Video Container -->
	<div class="relative h-full w-full">
		{#if hasCamera}
			<!-- Video Element -->
			<video
				bind:this={videoElement}
				class="h-full w-full object-cover"
				muted
				playsinline
			></video>

			<!-- Mobile-Optimized Scanning Overlay -->
			<div class="absolute inset-0 flex items-center justify-center">
				<div class="relative">
					<!-- Mobile Scanning Frame - Larger and more prominent -->
					<div class="h-56 w-56 md:h-64 md:w-64 border-2 border-white/50 bg-transparent">
						<!-- Corner indicators with better mobile visibility -->
						<div class="absolute -top-1 -left-1 h-10 w-10 md:h-8 md:w-8 border-l-4 border-t-4 border-primary"></div>
						<div class="absolute -top-1 -right-1 h-10 w-10 md:h-8 md:w-8 border-r-4 border-t-4 border-primary"></div>
						<div class="absolute -bottom-1 -left-1 h-10 w-10 md:h-8 md:w-8 border-l-4 border-b-4 border-primary"></div>
						<div class="absolute -bottom-1 -right-1 h-10 w-10 md:h-8 md:w-8 border-r-4 border-b-4 border-primary"></div>
					</div>

					<!-- Enhanced Scanning Line Animation -->
					{#if scanning}
						<div class="absolute inset-0 overflow-hidden">
							<div class="scanning-line absolute left-0 right-0 h-1 md:h-0.5 bg-primary shadow-lg shadow-primary/50"></div>
						</div>
					{/if}

					<!-- Mobile Helper Text -->
					<div class="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center md:hidden">
						<p class="text-white text-sm font-medium">Position barcode in frame</p>
						<p class="text-white/70 text-xs mt-1">Auto-scan when detected</p>
					</div>
				</div>
			</div>
		{:else}
			<!-- No Camera Available -->
			<div class="flex h-full items-center justify-center text-white">
				<div class="text-center">
					<IconCamera class="mx-auto mb-4 h-16 w-16 text-white/50" />
					<h3 class="mb-2 text-xl font-semibold">Camera Not Available</h3>
					<p class="text-white/70">Please ensure your device has a camera and grant permission to use it.</p>
				</div>
			</div>
		{/if}
	</div>

	<!-- Mobile-Optimized Instructions -->
	<div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent text-center text-white">
		{#if error}
			<!-- Mobile Error Display -->
			<div class="p-4 md:p-6">
				<div class="mb-4 rounded-lg bg-error/20 p-4 text-error-content">
					<p class="font-semibold mb-3 text-base">{error}</p>
					{#if error.includes('Permission denied') || error.includes('Camera permission')}
						<div class="text-sm space-y-3">
							{#if isMobileDevice()}
								<div class="text-left">
									<p class="mb-2 font-medium">On mobile devices:</p>
									<ul class="list-disc list-inside space-y-1 text-xs">
										<li>Tap "Allow" when prompted for camera access</li>
										<li>Check your browser settings if you previously denied access</li>
										<li>Try refreshing the page and allowing camera access</li>
										<li>Ensure you're using a secure connection (HTTPS)</li>
									</ul>
								</div>
							{:else}
								<div class="text-left">
									<p class="mb-2 font-medium">On desktop:</p>
									<ul class="list-disc list-inside space-y-1 text-xs">
										<li>Click "Allow" when prompted for camera access</li>
										<li>Check the camera icon in your browser's address bar</li>
										<li>Go to browser settings and enable camera for this site</li>
										<li>Ensure no other applications are using the camera</li>
									</ul>
								</div>
							{/if}
							<!-- Mobile-Friendly Action Buttons -->
							<div class="flex flex-col gap-2 mt-4 md:flex-row md:justify-center">
								<button
									type="button"
									class="btn btn-sm btn-primary w-full md:w-auto"
									onclick={requestCameraPermission}
									disabled={isInitializing}
								>
									{isInitializing ? 'Requesting...' : 'Request Camera Access'}
								</button>
								<button
									type="button"
									class="btn btn-sm btn-outline w-full md:w-auto"
									onclick={() => location.reload()}
								>
									Refresh Page
								</button>
							</div>
						</div>
					{/if}
				</div>
			</div>
		{:else if isInitializing}
			<div class="p-6">
				<p class="text-lg md:text-xl mb-2">Initializing camera...</p>
				<p class="text-sm text-white/70">
					{isMobileDevice() ? 'Please tap "Allow" when prompted for camera access' : 'Please click "Allow" when prompted for camera access'}
				</p>
			</div>
		{:else if scanning}
			<!-- Hidden on mobile since we show inline helper text -->
			<div class="hidden md:block p-6">
				<p class="text-lg">Position the barcode within the frame</p>
				<p class="text-sm text-white/70">The barcode will be scanned automatically</p>
			</div>
		{:else}
			<div class="p-6">
				<p class="text-lg md:text-xl mb-2">Camera ready</p>
				<p class="text-sm text-white/70">
					{isMobileDevice() ? 'Tap anywhere to start scanning' : 'Click to start scanning'}
				</p>
			</div>
		{/if}
	</div>
</div>

<style>
	.scanning-line {
		animation: scan 2s linear infinite;
	}

	@keyframes scan {
		0% {
			top: 0;
		}
		100% {
			top: 100%;
		}
	}
</style>
