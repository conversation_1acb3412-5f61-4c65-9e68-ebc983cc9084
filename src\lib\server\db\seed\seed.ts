import { hash } from '@node-rs/argon2';
import { randomUUID } from 'crypto';
import { and, eq, inArray, isNull } from 'drizzle-orm';
import { db } from '..';
import {
	templateDefinitionSchema,
	type FormField,
	type TemplateDefinition
} from '../../../schemas/template';
import { generateAndStoreRecoveryCodes } from '../../auth/account-recovery';
import { hashOtp } from '../../auth/utils';
import {
	client_notes,
	clients,
	email_verification_tokens,
	password_reset_tokens,
	templates,
	user_account_recovery_codes,
	user_oauth_accounts,
	user_sessions,
	users,
	webauthn_challenges,
	webauthn_credentials
} from '../schema';
import { seedSystemTemplates } from './system-templates';

/**
 * Comprehensive test data seeding for WebAuthn-based authentication flows
 * Creates multiple test users with different authentication states
 */

// Test user configurations
const TEST_USERS = [
	{
		email: '<EMAIL>',
		password: 'password123',
		firstName: 'Test',
		lastName: 'User',
		emailVerified: true,
		hasWebAuthn: true,
		hasRecoveryCodes: true,
		description:
			'Primary test user - fully verified, password, WebAuthn, recovery codes, multiple clients and templates.'
	},
	{
		email: '<EMAIL>',
		password: 'password123',
		firstName: 'Unverified',
		lastName: 'User',
		emailVerified: false,
		description: 'Unverified test user - for email verification flow testing'
	},
	{
		email: '<EMAIL>',
		password: 'password123',
		firstName: 'WebAuthn',
		lastName: 'User',
		emailVerified: true,
		hasWebAuthn: true,
		description: 'User with WebAuthn credentials - for passkey testing'
	},
	{
		email: '<EMAIL>',
		password: null, // WebAuthn-only user
		firstName: 'Passwordless',
		lastName: 'User',
		emailVerified: true,
		hasWebAuthn: true,
		description: 'Passwordless user - WebAuthn only authentication'
	},
	{
		email: '<EMAIL>',
		password: 'password123',
		firstName: 'Multi',
		lastName: 'Passkey',
		emailVerified: true,
		hasWebAuthn: true,
		multiplePasskeys: true,
		description: 'User with multiple passkeys - for credential management testing'
	},
	{
		email: '<EMAIL>',
		password: 'password123',
		firstName: 'Recovery',
		lastName: 'User',
		emailVerified: true,
		hasRecoveryCodes: true,
		description: 'User with account recovery codes - for recovery testing'
	}
];

// Mock client data for testing
const MOCK_CLIENTS = [
	{
		firstName: 'Jane',
		lastName: 'Doe',
		email: '<EMAIL>',
		phone: '555-0101',
		notes: 'First-time client, interested in color and cut.'
	},
	{
		firstName: 'John',
		lastName: 'Smith',
		email: '<EMAIL>',
		phone: '555-0102',
		notes: 'Regular client, books every 6 weeks for a trim.'
	},
	{
		firstName: 'Emily',
		lastName: 'Jones',
		email: '<EMAIL>',
		phone: '555-0103',
		notes: 'Prefers eco-friendly products. Allergic to lavender.'
	},
	{
		firstName: 'Michael',
		lastName: 'Williams',
		email: '<EMAIL>',
		phone: '555-0104',
		notes: 'Getting married next month, needs consultation for wedding hairstyle.'
	},
	{
		firstName: 'Sarah',
		lastName: 'Brown',
		email: '<EMAIL>',
		phone: '555-0105',
		notes: 'Has very curly hair, needs advice on managing it.'
	},
	{
		firstName: 'David',
		lastName: 'Miller',
		email: '<EMAIL>',
		phone: '555-0106',
		notes: 'Wants a significant change, maybe a bold color.'
	}
];

// Mock WebAuthn credential data for testing
const MOCK_WEBAUTHN_CREDENTIALS = [
	{
		credentialId: 'mock-credential-platform-1',
		publicKey:
			'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA',
		counter: 0,
		transports: 'internal,hybrid',
		deviceType: 'platform',
		backedUp: true
	},
	{
		credentialId: 'mock-credential-security-key',
		publicKey:
			'BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB',
		counter: 0,
		transports: 'usb,nfc',
		deviceType: 'cross-platform',
		backedUp: false
	},
	{
		credentialId: 'mock-credential-mobile',
		publicKey:
			'CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC',
		counter: 0,
		transports: 'hybrid,internal',
		deviceType: 'platform',
		backedUp: true
	}
];

async function clearExistingData(): Promise<void> {
	console.log('🧹 Clearing existing test data...');

	try {
		// Clear in dependency order (foreign keys)
		await db.delete(client_notes);
		await db.delete(clients);
		await db.delete(templates);
		await db.delete(webauthn_credentials);
		await db.delete(user_sessions);
		await db.delete(email_verification_tokens);
		await db.delete(password_reset_tokens);
		await db.delete(user_account_recovery_codes);
		await db.delete(webauthn_challenges);
		await db.delete(user_oauth_accounts);
		await db.delete(users);

		console.log('  ✅ Existing data cleared successfully');
	} catch (error) {
		console.error('  ❌ Error clearing existing data:', error);
		throw error;
	}
}

async function seedUsers(): Promise<Map<string, string>> {
	console.log('👥 Seeding test users...');

	const userIdMap = new Map<string, string>();

	for (const userData of TEST_USERS) {
		try {
			const hashedPassword = userData.password ? await hash(userData.password) : null;

			const [newUser] = await db
				.insert(users)
				.values({
					email: userData.email,
					hashed_password: hashedPassword,
					first_name: userData.firstName,
					last_name: userData.lastName,
					email_verified: userData.emailVerified,
					preferences: {
						hiddenSystemTemplates: []
					}
				})
				.returning({ id: users.id, email: users.email });

			if (newUser) {
				userIdMap.set(userData.email, newUser.id);
				console.log(`  ✅ Created user: ${userData.email} (${userData.description})`);
			} else {
				console.error(`  ❌ Failed to create user: ${userData.email}`);
			}
		} catch (error) {
			console.error(`  ❌ Error creating user ${userData.email}:`, error);
			throw error;
		}
	}

	return userIdMap;
}

async function seedUserTemplates(userIdMap: Map<string, string>): Promise<void> {
	console.log('📝 Seeding user-specific note templates...');

	const testUserId = userIdMap.get('<EMAIL>');
	if (!testUserId) {
		console.warn('  ⚠️ <EMAIL> user not found, skipping user template seeding.');
		return;
	}

	const userTemplates = [
		{
			name: 'My Custom Cut & Style',
			description: 'A personalized template for my regulars.',
			status: 'active',
			fields: [
				{ name: 'previousStyle', label: 'Previous Style', type: 'text', required: false },
				{
					name: 'requestedChange',
					label: 'Requested Change',
					type: 'textarea',
					required: true
				},
				{
					name: 'consultationNotes',
					label: 'Consultation Notes',
					type: 'textarea',
					required: false
				}
			]
		},
		{
			name: 'Quick Consultation (Draft)',
			description: 'A short form for walk-ins. Still a work in progress.',
			status: 'draft',
			fields: [{ name: 'clientRequest', label: 'Client Request', type: 'textarea', required: true }]
		},
		{
			name: 'Old Color Template (Archived)',
			description: 'This was replaced by the new system template.',
			status: 'archived',
			fields: [{ name: 'oldFormula', label: 'Old Formula', type: 'text', required: true }]
		}
	];

	for (const template of userTemplates) {
		try {
			const templateDefinition: TemplateDefinition = {
				fields: template.fields.map((field) => ({
					id: randomUUID(),
					...field
				})) as FormField[]
			};

			// Validate against strict schema before inserting
			const validation = templateDefinitionSchema.safeParse(templateDefinition);

			if (!validation.success) {
				console.error(
					`  ❌ Validation failed for user template "${template.name}":`,
					validation.error.issues
				);
				continue;
			}

			await db.insert(templates).values({
				user_id: testUserId,
				name: template.name,
				description: template.description,
				status: template.status as 'draft' | 'active' | 'archived',
				template_definition: templateDefinition
			});
			console.log(`  ✅ Added user <NAME_EMAIL>: "${template.name}"`);
		} catch (error) {
			console.error(`  ❌ Error adding user template "${template.name}":`, error);
		}
	}
}

async function seedClients(userIdMap: Map<string, string>): Promise<Map<string, string[]>> {
	console.log('💇 Seeding clients...');
	const userClientsMap = new Map<string, string[]>();

	for (const [email, userId] of userIdMap.entries()) {
		console.log(`  ➕ Adding clients for user: ${email}`);
		const createdClientIds: string[] = [];

		// Give the primary test user a comprehensive set of clients
		const isPrimaryTestUser = email === '<EMAIL>';
		const clientCount = isPrimaryTestUser ? MOCK_CLIENTS.length : email.includes('multi') ? 4 : 2;

		for (let i = 0; i < clientCount; i++) {
			const clientData = MOCK_CLIENTS[i % MOCK_CLIENTS.length];
			try {
				const [newClient] = await db
					.insert(clients)
					.values({
						user_id: userId,
						first_name: clientData.firstName,
						last_name: clientData.lastName,
						email: `${clientData.firstName.toLowerCase()}.${clientData.lastName.toLowerCase()}${i}@testclient.com`,
						phone_number: clientData.phone,
						notes: clientData.notes
					})
					.returning({ id: clients.id });

				if (newClient) {
					createdClientIds.push(newClient.id);
					console.log(`    ✅ Added client: ${clientData.firstName} ${clientData.lastName}`);
				}
			} catch (error) {
				console.error(
					`    ❌ Error adding client "${clientData.firstName}" for user ${email}:`,
					error
				);
			}
		}
		userClientsMap.set(userId, createdClientIds);
	}
	return userClientsMap;
}

async function seedClientNotes(userClientsMap: Map<string, string[]>): Promise<void> {
	console.log('🗒️  Seeding client notes with hairstylist-specific data...');

	const systemTemplates = await db.query.templates.findMany({
		where: and(isNull(templates.user_id), eq(templates.status, 'active'))
	});

	if (systemTemplates.length === 0) {
		console.warn('  ⚠️ No active system templates found, skipping note creation.');
		return;
	}

	const findTemplate = (marker: string) => {
		const template = systemTemplates.find((t) => t.special_marker === marker);
		if (!template) {
			throw new Error(`System template with marker "${marker}" not found!`);
		}
		return template;
	};

	try {
		const newClientConsultationTemplate = findTemplate('new_client_consultation');
		const colorFormulaTemplate = findTemplate('color_formula');
		const haircutDetailsTemplate = findTemplate('haircut_details');
		const plainTextTemplate = findTemplate('plain_text_note');

		for (const [userId, clientIds] of userClientsMap.entries()) {
			const clientsForUser = await db.query.clients.findMany({
				where: inArray(clients.id, clientIds)
			});

			for (const client of clientsForUser) {
				// --- Jane Doe: New Client ---
				if (client.first_name === 'Jane' && client.last_name === 'Doe') {
					await db.insert(client_notes).values({
						client_id: client.id,
						user_id: userId,
						template_id: newClientConsultationTemplate.id,
						note_data: {
							hairHistory: 'Previously box-dyed dark brown. Some old highlights at the ends.',
							hairGoals: 'Wants to go lighter, aiming for a natural-looking balayage.',
							allergiesSensitivities: 'Slight sensitivity to strong fragrances.',
							lifestyle: 'Washes hair 2-3 times a week, uses heat tools occasionally.',
							hairTexture: 'fine',
							hairDensity: 'medium'
						}
					});
					console.log(`    ✅ Added 'New Client Consultation' note for Jane Doe.`);
				}

				// --- John Smith: Regular Color & Cut ---
				if (client.first_name === 'John' && client.last_name === 'Smith') {
					await db.insert(client_notes).values([
						{
							client_id: client.id,
							user_id: userId,
							template_id: colorFormulaTemplate.id,
							note_data: {
								colorLineUsed: 'Redken Shades EQ',
								baseColorFormula: '7N + 8GI on roots',
								highlightsLowlightsFormula: 'Full head of babylights with Flash Lift',
								tonerGlossFormula: '9V + 9P + Crystal Clear',
								developerVolume: '20',
								processingTime: 45,
								applicationNotes: 'Toned at the bowl for 15 minutes.'
							}
						},
						{
							client_id: client.id,
							user_id: userId,
							template_id: haircutDetailsTemplate.id,
							note_data: {
								lengthRemoved: '0.5 inches',
								techniqueUsed: 'Scissor over comb on sides, textured top',
								toolsUsed: 'Shears, texturizing shears',
								stylingNotes: 'Styled with a matte pomade.'
							}
						}
					]);
					console.log(`    ✅ Added 'Color Formula' and 'Haircut' notes for John Smith.`);
				}

				// --- Generic Notes for other clients ---
				if (client.first_name !== 'Jane' && client.first_name !== 'John') {
					await db.insert(client_notes).values({
						client_id: client.id,
						user_id: userId,
						template_id: plainTextTemplate.id,
						note_data: {
							content: `Client expressed interest in a new styling product. Follow up next time. They also rebooked for 6 weeks from now.`
						}
					});
					console.log(`    ✅ Added 'Plain Text Note' for ${client.first_name}.`);
				}
			}
		}
	} catch (error) {
		console.error('  ❌ Error seeding realistic client notes:', error);
		throw error;
	}
}

async function seedWebAuthnCredentials(userIdMap: Map<string, string>): Promise<void> {
	console.log('🔐 Seeding WebAuthn credentials...');

	// Add WebAuthn credentials for users that should have them
	const webauthnUsers = TEST_USERS.filter((user) => user.hasWebAuthn);

	for (const userData of webauthnUsers) {
		const userId = userIdMap.get(userData.email);
		if (!userId) {
			console.error(`  ❌ User ID not found for ${userData.email}`);
			continue;
		}

		try {
			// Determine how many credentials to add
			let credentialsToAdd;
			if (userData.multiplePasskeys) {
				credentialsToAdd = MOCK_WEBAUTHN_CREDENTIALS; // All 3 credentials
			} else if (userData.email === '<EMAIL>') {
				credentialsToAdd = MOCK_WEBAUTHN_CREDENTIALS.slice(0, 2); // 2 credentials
			} else {
				// All other users with `hasWebAuthn` get one credential
				credentialsToAdd = [MOCK_WEBAUTHN_CREDENTIALS[0]]; // 1 credential
			}

			for (const [index, credData] of credentialsToAdd.entries()) {
				await db.insert(webauthn_credentials).values({
					user_id: userId,
					credential_id: `${credData.credentialId}-${userData.email.split('@')[0]}-${index}`,
					public_key: credData.publicKey,
					counter: credData.counter,
					transports: credData.transports,
					device_type: credData.deviceType,
					backed_up: credData.backedUp,
					last_used_at: new Date()
				});

				console.log(
					`  ✅ Added WebAuthn credential for ${userData.email} (${credData.deviceType})`
				);
			}
		} catch (error) {
			console.error(`  ❌ Error adding WebAuthn credentials for ${userData.email}:`, error);
			throw error;
		}
	}
}

async function seedEmailVerificationTokens(userIdMap: Map<string, string>): Promise<void> {
	console.log('📧 Seeding email verification tokens...');

	// Create verification tokens for unverified users
	const unverifiedUsers = TEST_USERS.filter((user) => !user.emailVerified);

	for (const userData of unverifiedUsers) {
		const userId = userIdMap.get(userData.email);
		if (!userId) {
			console.error(`  ❌ User ID not found for ${userData.email}`);
			continue;
		}

		try {
			// Create a mock verification token
			const mockToken = `mock-verification-token-${Date.now()}`;
			const mockOtpCode = '123456';
			const hashedOtp = await hashOtp(mockOtpCode);

			await db.insert(email_verification_tokens).values({
				id: mockToken,
				user_id: userId,
				email: userData.email,
				hashed_otp_code: hashedOtp,
				expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
			});

			console.log(`  ✅ Created verification token for ${userData.email}`);
			console.log(`     📝 Token: ${mockToken}`);
			console.log(`     🔢 OTP Code: ${mockOtpCode}`);
		} catch (error) {
			console.error(`  ❌ Error creating verification token for ${userData.email}:`, error);
			throw error;
		}
	}
}

async function seedAccountRecoveryCodes(userIdMap: Map<string, string>): Promise<void> {
	console.log('🔑 Seeding account recovery codes...');

	const recoveryUsers = TEST_USERS.filter((user) => user.hasRecoveryCodes);

	if (recoveryUsers.length === 0) {
		console.log('  ⚠️ No users configured for recovery codes, skipping.');
		return;
	}

	for (const recoveryUser of recoveryUsers) {
		const userId = userIdMap.get(recoveryUser.email);
		if (!userId) {
			console.error(`  ❌ User ID not found for ${recoveryUser.email}`);
			continue;
		}

		try {
			const codesObject = await generateAndStoreRecoveryCodes(userId);
			if (!codesObject || !Array.isArray(codesObject.hexCodes)) {
				console.error(
					`  ❌ Failed to generate valid recovery codes for ${recoveryUser.email}. codesObject.hexCodes is not an array.`
				);
				continue;
			}
			const plaintextCodes = codesObject.hexCodes;

			console.log(
				`  ✅ Generated ${plaintextCodes.length} recovery codes for ${recoveryUser.email}:`
			);
			plaintextCodes.forEach((code, index) => {
				console.log(`     ${index + 1}. ${code}`);
			});
		} catch (error) {
			console.error(`  ❌ Error generating recovery codes for ${recoveryUser.email}:`, error);
			throw error;
		}
	}
}

async function createTestSessions(userIdMap: Map<string, string>): Promise<void> {
	console.log('🔑 Creating test sessions...');

	// Create active sessions for some users to test session management
	const sessionUsers = [
		'<EMAIL>',
		'<EMAIL>',
		'<EMAIL>'
	];

	for (const email of sessionUsers) {
		const userId = userIdMap.get(email);
		if (!userId) {
			console.error(`  ❌ User ID not found for ${email}`);
			continue;
		}

		try {
			const sessionId = `test-session-${Date.now()}-${Math.random().toString(36).substring(7)}`;

			await db.insert(user_sessions).values({
				id: sessionId,
				user_id: userId,
				expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
			});

			console.log(`  ✅ Created session for ${email}`);
			console.log(`     🆔 Session ID: ${sessionId}`);
		} catch (error) {
			console.error(`  ❌ Error creating session for ${email}:`, error);
			throw error;
		}
	}
}

async function printSeedingSummary(userIdMap: Map<string, string>): Promise<void> {
	console.log('\n✅ Seeding complete. Summary:');
	console.log('==========================================');

	const testUsersData = TEST_USERS.map((user) => ({
		...user,
		id: userIdMap.get(user.email)
	}));

	for (const userData of testUsersData) {
		const userId = userData.id;
		if (!userId) continue;

		const webauthnCreds = await db
			.select()
			.from(webauthn_credentials)
			.where(eq(webauthn_credentials.user_id, userId));
		const userTemplates = await db.select().from(templates).where(eq(templates.user_id, userId));
		const userClients = await db.select().from(clients).where(eq(clients.user_id, userId));

		console.log(`📧 ${userData.email}`);
		console.log(`   🔒 Password: ${userData.password || 'None (WebAuthn only)'}`);
		console.log(`   ✅ Verified: ${userData.emailVerified ? 'Yes' : 'No'}`);
		console.log(`   🔐 WebAuthn: ${webauthnCreds.length} credential(s)`);
		console.log(`   📝 Note Templates: ${userTemplates.length}`);
		console.log(`   💇 Clients: ${userClients.length}`);

		if (userData.hasRecoveryCodes) {
			const recoveryCodes = await db
				.select()
				.from(user_account_recovery_codes)
				.where(eq(user_account_recovery_codes.user_id, userId));
			console.log(
				`   🛡️ Recovery Codes: ${
					recoveryCodes.filter((rc) => !rc.used_at).length
				} active (Codes were printed to console during seeding)`
			);
		}

		console.log(`   📝 ${userData.description}`);
		console.log('');
	}

	console.log('🔧 Testing Instructions:');
	console.log('==========================================');
	console.log('• Use <EMAIL> / password123 for basic login testing');
	console.log('• Use <EMAIL> for email verification testing');
	console.log('• Use <EMAIL> for WebAuthn/passkey testing');
	console.log('• Use <EMAIL> for passwordless WebAuthn testing');
	console.log('• Use <EMAIL> for multiple passkey management testing');
	console.log(
		'• Use <EMAIL> for account recovery code testing (codes printed during seed)'
	);
	console.log('• Check console output above for verification tokens and session IDs');
	console.log('✨ Seeding complete! Your development environment is ready. ✨');
	console.log('------------------------------------------------------------');
}

export async function seedDatabase() {
	try {
		const userIdMap = await seedUsers();
		await seedUserTemplates(userIdMap);
		const userClientsMap = await seedClients(userIdMap);
		await seedClientNotes(userClientsMap);
		await seedWebAuthnCredentials(userIdMap);
		await seedEmailVerificationTokens(userIdMap);
		await seedAccountRecoveryCodes(userIdMap);
		// Note: No password reset tokens are seeded by default
		await createTestSessions(userIdMap);
		await printSeedingSummary(userIdMap);
	} catch (error) {
		console.error('❌ An error occurred during the seeding process:', error);
		throw error;
	}
}

async function main() {
	try {
		// The new `db:reseed` script now handles clearing data first.
		await seedSystemTemplates(db);
		await seedDatabase();
		process.exit(0);
	} catch (error) {
		console.error('❌ Seeding failed:', error);
		process.exit(1);
	}
}

// Run main function if executed directly
if (import.meta.url.startsWith('file:')) {
	const moduleUrl = new URL(import.meta.url);
	const executionUrl = new URL(process.argv[1], 'file:');
	if (moduleUrl.pathname === executionUrl.pathname) {
		main();
	}
}
