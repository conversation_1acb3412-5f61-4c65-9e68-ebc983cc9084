import { db } from '$lib/server/db';
import {
    auth_magic_links,
    email_verification_tokens,
    password_reset_tokens,
    users
} from '$lib/server/db/schema';
import { sendEmail } from '$lib/server/utils/email';
import { createHash, randomBytes } from 'crypto';
import { count, eq } from 'drizzle-orm';
import { hashUrlToken, TOKEN_DURATIONS } from './token-manager';
import { generateRandomUrlToken } from './utils';

export function verifyEmailInput(email: string): boolean {
	if (!email || email.length < 3 || !email.includes('@')) {
		return false;
	}
	return true;
}

export async function checkEmailAvailability(email: string): Promise<boolean> {
	const result = await db.select({ value: count() }).from(users).where(eq(users.email, email));

	// result should be [{ value: number }]
	return result.length > 0 && result[0].value === 0;
}

const MAGIC_LINK_EXPIRATION_MINUTES = 15;

function generateSecureToken(length = 40) {
	return randomBytes(length).toString('hex');
}

export async function generateMagicLinkToken(userId: string): Promise<string> {
	// Delete any existing magic links for this user
	await db.delete(auth_magic_links).where(eq(auth_magic_links.user_id, userId));

	// Generate a secure random token
	const plaintextToken = generateRandomUrlToken(32); // 64 character hex string
	const hashedToken = hashUrlToken(plaintextToken);
	const expiresAt = new Date(Date.now() + TOKEN_DURATIONS.MAGIC_LINK * 60 * 1000);

	// Store the hashed version in the database
	await db.insert(auth_magic_links).values({
		id: hashedToken,
		user_id: userId,
		expires_at: expiresAt
	});

	// Return the plaintext token for use in the email link
	return plaintextToken;
}

export async function validateMagicLinkToken(token: string): Promise<string | null> {
	// Hash the provided token to compare with stored hash
	const hashedToken = hashUrlToken(token);
	
	const magicLink = await db.query.auth_magic_links.findFirst({
		where: eq(auth_magic_links.id, hashedToken)
	});

	// Always delete the token after use (consumed or expired)
	if (magicLink) {
		await db.delete(auth_magic_links).where(eq(auth_magic_links.id, hashedToken));
	}

	// Check if token exists and is not expired
	if (!magicLink || magicLink.expires_at.getTime() < Date.now()) {
		return null;
	}

	return magicLink.user_id;
}

const OTP_LENGTH = 8;
const OTP_EXPIRATION_MINUTES = 15;

export async function generateEmailVerificationToken(
	userId: string,
	email: string
): Promise<string> {
	await db
		.delete(email_verification_tokens)
		.where(eq(email_verification_tokens.user_id, userId));
	const code = generateSecureToken(OTP_LENGTH / 2); // Each byte becomes 2 hex characters
	const expiresAt = new Date(Date.now() + OTP_EXPIRATION_MINUTES * 60 * 1000);

	const hashed_otp_code = createHash('sha256').update(code).digest('hex');

	await db.insert(email_verification_tokens).values({
		id: code, // In this context, the code itself is the ID
		user_id: userId,
		email,
		hashed_otp_code: hashed_otp_code,
		expires_at: expiresAt
	});

	return code;
}

export async function sendEmailVerificationLink(email: string, code: string) {
	// Use environment variable for base URL, fallback to localhost for development
	const baseUrl = process.env.WEBAUTHN_ORIGIN?.split(',')[0] || 'http://localhost:5173';
	const url = `${baseUrl}/verify-email?code=${code}`;

	await sendEmail({
		to: email,
		subject: 'Verify your email address',
		text: `Click the link to verify your email: ${url}`,
		html: `<p>Click the link to verify your email: <a href="${url}">${url}</a></p>`
	});
}

export async function verifyEmailVerificationToken(token: string): Promise<string | null> {
	const hashedToken = createHash('sha256').update(token).digest('hex');

	const storedToken = await db.query.email_verification_tokens.findFirst({
		where: eq(email_verification_tokens.hashed_otp_code, hashedToken)
	});

	if (storedToken) {
		await db
			.delete(email_verification_tokens)
			.where(eq(email_verification_tokens.id, storedToken.id));
	}

	if (!storedToken || storedToken.expires_at.getTime() < Date.now()) {
		return null;
	}

	return storedToken.user_id;
}

export async function sendPasswordResetEmail(email: string) {
	const user = await db.query.users.findFirst({
		where: eq(users.email, email)
	});

	if (!user) {
		// Do not reveal if user exists
		return;
	}

	const code = await generatePasswordResetToken(user.id, email);

	// Use environment variable for base URL, fallback to localhost for development
	const baseUrl = process.env.WEBAUTHN_ORIGIN?.split(',')[0] || 'http://localhost:5173';
	const url = `${baseUrl}/reset-password?code=${code}`;

	await sendEmail({
		to: email,
		subject: 'Reset your password',
		text: `Click the link to reset your password: ${url}`,
		html: `<p>Click the link to reset your password: <a href="${url}">${url}</a></p>`
	});
}

export async function generatePasswordResetToken(userId: string, email: string): Promise<string> {
	await db.delete(password_reset_tokens).where(eq(password_reset_tokens.user_id, userId));

	const code = generateSecureToken(4); // 8 characters
	const expiresAt = new Date(Date.now() + OTP_EXPIRATION_MINUTES * 60 * 1000);
	const hashedCode = createHash('sha256').update(code).digest('hex');

	await db.insert(password_reset_tokens).values({
		id: code,
		user_id: userId,
		email,
		hashed_otp_code: hashedCode,
		expires_at: expiresAt
	});
	return code;
}

export async function verifyPasswordResetToken(token: string): Promise<string | null> {
	const hashedToken = createHash('sha256').update(token).digest('hex');

	const storedToken = await db.query.password_reset_tokens.findFirst({
		where: eq(password_reset_tokens.hashed_otp_code, hashedToken)
	});

	if (storedToken) {
		await db.delete(password_reset_tokens).where(eq(password_reset_tokens.id, storedToken.id));
	}

	if (!storedToken || storedToken.expires_at.getTime() < Date.now()) {
		return null;
	}

	return storedToken.user_id;
}
