<!--
This Svelte component handles the "Forgot Password" functionality.
Users can enter their email address to receive a password reset link.
It uses Svelte Runes for reactivity and $props() to access form data from server actions.
The `enhance` function from $app/forms is used for progressive enhancement of the form.
Error messages or success messages (form?.message) from the server are displayed.
-->
<script lang="ts">
	import { applyAction, enhance } from '$app/forms';
	import { ERROR_MESSAGES } from '$lib/config';
	import Errors from '$lib/runes-form/Errors.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { forgotPasswordSchema } from '$lib/schemas/auth';
	import { toast } from '$lib/ui/toast';
	import IconEmail from '~icons/icon-park-outline/email-down';
	import type { ActionData } from './$types';

	let { form: actionForm }: { form?: ActionData } = $props();

	const form = createRuneForm(forgotPasswordSchema, {
		email: actionForm?.data?.email?.toString() ?? ''
	});

	$effect(() => {
		if (actionForm?.success) {
			toast.success(actionForm.message);
		} else if (actionForm?.message) {
			toast.error(actionForm.message);
		}
		if (actionForm?.errors) {
			form.setErrors(actionForm.errors);
			const errorCount = Object.values(actionForm.errors).filter((e) => e).length;
			if (errorCount > 0) {
				toast.error(ERROR_MESSAGES.GENERIC_ERROR);
			}
		}
	});
</script>

<div class="hero bg-base-200 min-h-screen">
	<div class="hero-content">
		<div class="card bg-base-100/80 w-full max-w-md shadow-xl backdrop-blur-sm">
			<div class="card-body">
				<h1 class="card-title text-2xl">Forgot Your Password?</h1>
				<p class="text-base-content/70">
					No problem. Enter your email address and we'll send you a link to reset it.
				</p>
				<div class="divider"></div>

				<RuneForm {form}>
					{#snippet children(form)}
						<form
							method="POST"
							class="space-y-4"
							use:enhance={() => {
								form.setSubmitting(true);
								return async ({ result }) => {
									await applyAction(result);
									form.setSubmitting(false);
									if (result.type === 'success') {
										form.reset();
									}
								};
							}}
							onsubmit={form.handleSubmit()}
						>
							<div class="form-control">
								<label for="email" class="label"><span class="label-text">Email</span></label>
								<div class="relative">
									<span
										class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
									>
										<IconEmail class="text-base-content/40 h-5 w-5" />
									</span>
									<input
										id="email"
										name="email"
										type="email"
										placeholder="<EMAIL>"
										class="input input-bordered w-full pl-10"
										bind:value={form.values.email}
										onblur={() => form.setTouched('email', true)}
									/>
								</div>
								<Errors name="email" />
							</div>

							<div class="form-control mt-6">
								<button type="submit" class="btn btn-primary" disabled={form.isSubmitting}>
									{#if form.isSubmitting}
										<span class="loading loading-spinner"></span>
										Sending...
									{:else}
										Send Reset Link
									{/if}
								</button>
							</div>
						</form>
					{/snippet}
				</RuneForm>

				<div class="mt-4 text-center text-sm">
					<p>
						Remembered your password?
						<a href="/login" class="link-primary link">Back to Sign In</a>
					</p>
				</div>
			</div>
		</div>
	</div>
</div>
