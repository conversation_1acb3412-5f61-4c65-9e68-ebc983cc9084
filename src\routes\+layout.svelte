<script lang="ts">
	import ConfirmationModal from '$lib/components/ConfirmationModal.svelte';
	import ToastNotifications from '$lib/components/ToastNotifications.svelte';
	import { injectSpeedInsights } from '@vercel/speed-insights/sveltekit';
	import type { Snippet } from 'svelte';

	import '../app.css';

	type LayoutProps = {
		children: Snippet; // children is always expected
	};

	let { children }: LayoutProps = $props();

	injectSpeedInsights();
</script>

<!--
This is the root layout file for the SvelteKit application, using Svelte Runes mode.
It wraps all pages and layouts, making it the ideal place to import global styles
like app.css, which includes Tailwind CSS and DaisyUI.

In Runes mode, props are accessed using $props(), as seen with let { children } = $props();.
The content of the current page or nested layout is rendered using {@render children()}
instead of the traditional <slot /> component.
Any elements or components placed around {@render children()} will be present on all pages
that use this root layout.
-->

<svelte:head>
	<script
		data-recording-token="geA1aEpd1yz8Ds8tsgFVVdYK3lQgiSASJcwGnn4h"
		data-is-production-environment="false"
		src="https://snippet.meticulous.ai/v1/meticulous.js"
	></script>
</svelte:head>

<!-- Page content goes here -->
<div data-testid="root-layout-container">
	{@render children()}
</div>

<div data-testid="toast-notifications-wrapper">
	<ToastNotifications />
</div>
<ConfirmationModal />
