import type { TemplateDefinition } from '$lib/schemas/template';
import { relations } from 'drizzle-orm';
import {
    bigint,
    boolean,
    integer,
    jsonb,
    pgEnum,
    pgTable,
    text,
    timestamp,
    uniqueIndex,
    uuid,
    varchar
} from 'drizzle-orm/pg-core';
import { z } from 'zod/v4';

// ENUMS
export const product_action_type = pgEnum('product_action_type', ['Used', 'Sold']);
export const template_status = pgEnum('template_status', ['draft', 'active', 'archived']);

export const UserPreferencesSchema = z.object({
	hiddenSystemTemplates: z.array(z.string()).default([])
});

export type UserPreferences = z.infer<typeof UserPreferencesSchema>;

// USERS
export const users = pgTable('users', {
	id: uuid('id').primaryKey().defaultRandom(),
	email: varchar('email', { length: 255 }).notNull().unique(),
	hashed_password: text('hashed_password'),
	first_name: varchar('first_name', { length: 100 }),
	last_name: varchar('last_name', { length: 100 }),
	profile_photo_url: text('profile_photo_url'),
	preferences: jsonb('preferences').$type<UserPreferences>().default({
		hiddenSystemTemplates: []
	}),
	email_verified: boolean('email_verified').notNull().default(false),
	current_challenge: text('current_challenge'),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	updated_at: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// TEMPLATES (replaces system_templates and user_templates)
export const templates = pgTable('templates', {
	id: uuid('id').primaryKey().defaultRandom(),
	user_id: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }), // NULL for system templates
	name: varchar('name', { length: 255 }).notNull(),
	description: text('description'),
	template_definition: jsonb('template_definition').notNull().$type<TemplateDefinition>(),
	status: template_status('status').notNull().default('active'),
	special_marker: text('special_marker').unique(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	updated_at: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// USER SESSIONS
export const user_sessions = pgTable('user_sessions', {
	id: text('id').primaryKey(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	expires_at: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// USER OAUTH ACCOUNTS
export const user_oauth_accounts = pgTable('user_oauth_accounts', {
	id: uuid('id').primaryKey().defaultRandom(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	provider: varchar('provider', { length: 50 }).notNull(),
	provider_user_id: text('provider_user_id').notNull(),
	email: varchar('email', { length: 255 }),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	updated_at: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// WEBAUTHN CREDENTIALS
export const webauthn_credentials = pgTable('webauthn_credentials', {
	id: uuid('id').primaryKey().defaultRandom(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	name: varchar('name', { length: 255 }),
	credential_id: text('credential_id').notNull().unique(),
	public_key: text('public_key').notNull(),
	counter: bigint('counter', { mode: 'number' }).notNull(),
	transports: text('transports'),
	device_type: varchar('device_type', { length: 50 }),
	backed_up: boolean('backed_up'),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	last_used_at: timestamp('last_used_at', { withTimezone: true, mode: 'date' })
});

// EMAIL VERIFICATION TOKENS
export const email_verification_tokens = pgTable('email_verification_tokens', {
	id: text('id').primaryKey(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	email: varchar('email', { length: 255 }).notNull(),
	hashed_otp_code: text('hashed_otp_code').notNull(),
	expires_at: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// PASSWORD RESET TOKENS
export const password_reset_tokens = pgTable('password_reset_tokens', {
	id: text('id').primaryKey(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	email: varchar('email', { length: 255 }).notNull(),
	hashed_otp_code: text('hashed_otp_code').notNull(),
	expires_at: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// AUTH MAGIC LINKS
export const auth_magic_links = pgTable('auth_magic_links', {
	id: text('id').primaryKey(), // The token itself
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	expires_at: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// USER_ACCOUNT_RECOVERY_CODES
export const user_account_recovery_codes = pgTable('user_account_recovery_codes', {
	id: uuid('id').primaryKey().defaultRandom(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	hashed_code: text('hashed_code').notNull(),
	used_at: timestamp('used_at', { withTimezone: true, mode: 'date' }),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// WEBAUTHN CHALLENGES
export const webauthn_challenges = pgTable('webauthn_challenges', {
	id: text('id').primaryKey(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	expires_at: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// INVENTORY ITEMS - ALPHA FEATURE
// This table supports the inventory management system that is currently in alpha status.
// Features include: item tracking, barcode scanning, quantity management, cost/price tracking,
// reorder thresholds, supplier information, and image storage.
// The system distinguishes between purchase cost (what you pay) and selling price (what you charge).
export const inventory_items = pgTable(
	'inventory_items',
	{
		id: uuid('id').primaryKey().defaultRandom(),
		user_id: uuid('user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		name: varchar('name', { length: 255 }).notNull(),
		description: text('description'),
		sku: varchar('sku', { length: 100 }), // Stock Keeping Unit - redundant with barcode, may be removed
		barcode: varchar('barcode', { length: 255 }), // Primary identifier for scanning workflows
		quantity: integer('quantity').notNull().default(0), // Current stock level
		price_in_cents: integer('price_in_cents'), // Selling price - what you charge customers (optional)
		cost_in_cents: integer('cost_in_cents').notNull(), // Purchase cost - what you pay to acquire the item (required, can be 0)
		reorder_threshold: integer('reorder_threshold'), // Minimum quantity before reordering is suggested
		supplier: text('supplier'), // Supplier information for reordering
		image_base64: text('image_base64'), // Base64 encoded image data, resized on backend
		created_at: timestamp('created_at', { withTimezone: true, mode: 'date' })
			.notNull()
			.defaultNow(),
		updated_at: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
	},
	(table) => {
		return {
			unique_sku_per_user: uniqueIndex('unique_sku_per_user').on(table.user_id, table.sku)
		};
	}
);

// SHOPPING LIST ITEMS - ALPHA FEATURE
// This table supports the shopping list system that is currently in alpha status.
// Features include: adding inventory items to shopping list, quantity management,
// purchase tracking, cost estimation, bulk completion workflows, and integration
// with inventory for automatic restocking after shopping completion.
export const shopping_list_items = pgTable('shopping_list_items', {
	id: uuid('id').primaryKey().defaultRandom(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	inventory_item_id: uuid('inventory_item_id')
		.notNull()
		.references(() => inventory_items.id, { onDelete: 'cascade' }), // Links to inventory for item details and cost tracking
	quantity: integer('quantity').notNull().default(1), // Quantity to purchase
	purchased: boolean('purchased').notNull().default(false), // Purchase completion status
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// CLIENTS
export const clients = pgTable('clients', {
	id: uuid('id').primaryKey().defaultRandom(),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	first_name: varchar('first_name', { length: 100 }),
	last_name: varchar('last_name', { length: 100 }),
	email: varchar('email', { length: 255 }),
	phone_number: varchar('phone_number', { length: 20 }),
	notes: text('notes'),
	image_base64: text('image_base64'),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	updated_at: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// CLIENT NOTES
export const client_notes = pgTable('client_notes', {
	id: uuid('id').primaryKey().defaultRandom(),
	client_id: uuid('client_id')
		.notNull()
		.references(() => clients.id, { onDelete: 'cascade' }),
	user_id: uuid('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	template_id: uuid('template_id')
		.notNull()
		.references(() => templates.id, { onDelete: 'cascade' }),
	note_data: jsonb('note_data').notNull().$type<Record<string, unknown>>(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow(),
	updated_at: timestamp('updated_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// RATE LIMIT BUCKETS
export const rate_limit_buckets = pgTable('rate_limit_buckets', {
	key: text('key').primaryKey(), // The identifier for the bucket (e.g., IP address, user ID)
	tokens: integer('tokens').notNull(),
	last_refilled_at: timestamp('last_refilled_at', { withTimezone: true, mode: 'date' })
		.notNull()
		.defaultNow()
});

// EXPIRING RATE LIMIT BUCKETS (for things like email verification)
export const expiring_rate_limit_buckets = pgTable('expiring_rate_limit_buckets', {
	key: text('key').primaryKey(),
	count: integer('count').notNull(),
	created_at: timestamp('created_at', { withTimezone: true, mode: 'date' }).notNull().defaultNow()
});

// RELATIONS
export const usersRelations = relations(users, ({ many }) => ({
	clients: many(clients),
	user_sessions: many(user_sessions),
	user_oauth_accounts: many(user_oauth_accounts),
	webauthn_credentials: many(webauthn_credentials),
	email_verification_tokens: many(email_verification_tokens),
	password_reset_tokens: many(password_reset_tokens),
	user_account_recovery_codes: many(user_account_recovery_codes),
	webauthn_challenges: many(webauthn_challenges),
	templates: many(templates),
	inventory_items: many(inventory_items),
	shopping_list_items: many(shopping_list_items),
	auth_magic_links: many(auth_magic_links)
}));

export const clientsRelations = relations(clients, ({ one, many }) => ({
	user: one(users, {
		fields: [clients.user_id],
		references: [users.id]
	}),
	client_notes: many(client_notes)
}));

export const clientNotesRelations = relations(client_notes, ({ one }) => ({
	client: one(clients, {
		fields: [client_notes.client_id],
		references: [clients.id]
	}),
	template: one(templates, {
		fields: [client_notes.template_id],
		references: [templates.id]
	})
}));

export const templatesRelations = relations(templates, ({ one, many }) => ({
	user: one(users, {
		fields: [templates.user_id],
		references: [users.id]
	}),
	client_notes: many(client_notes)
}));

export const userSessionsRelations = relations(user_sessions, ({ one }) => ({
	user: one(users, {
		fields: [user_sessions.user_id],
		references: [users.id]
	})
}));

export const userOAuthAccountsRelations = relations(user_oauth_accounts, ({ one }) => ({
	user: one(users, {
		fields: [user_oauth_accounts.user_id],
		references: [users.id]
	})
}));

export const webauthnCredentialsRelations = relations(webauthn_credentials, ({ one }) => ({
	user: one(users, {
		fields: [webauthn_credentials.user_id],
		references: [users.id]
	})
}));

export const emailVerificationTokensRelations = relations(email_verification_tokens, ({ one }) => ({
	user: one(users, {
		fields: [email_verification_tokens.user_id],
		references: [users.id]
	})
}));

export const passwordResetTokensRelations = relations(password_reset_tokens, ({ one }) => ({
	user: one(users, {
		fields: [password_reset_tokens.user_id],
		references: [users.id]
	})
}));

export const userAccountRecoveryCodesRelations = relations(
	user_account_recovery_codes,
	({ one }) => ({
		user: one(users, {
			fields: [user_account_recovery_codes.user_id],
			references: [users.id]
		})
	})
);

export const webauthnChallengesRelations = relations(webauthn_challenges, ({ one }) => ({
	user: one(users, {
		fields: [webauthn_challenges.user_id],
		references: [users.id]
	})
}));

export const inventoryItemsRelations = relations(inventory_items, ({ one }) => ({
	user: one(users, {
		fields: [inventory_items.user_id],
		references: [users.id]
	})
}));

export const shoppingListItemsRelations = relations(shopping_list_items, ({ one }) => ({
	user: one(users, { fields: [shopping_list_items.user_id], references: [users.id] }),
	item: one(inventory_items, {
		fields: [shopping_list_items.inventory_item_id],
		references: [inventory_items.id]
	})
}));

// TYPES
export type User = typeof users.$inferSelect;
export type UserSession = typeof user_sessions.$inferSelect;
export type UserOAuthAccount = typeof user_oauth_accounts.$inferSelect;
export type WebAuthnCredential = typeof webauthn_credentials.$inferSelect;
export type EmailVerificationToken = typeof email_verification_tokens.$inferSelect;
export type PasswordResetToken = typeof password_reset_tokens.$inferSelect;
export type UserAccountRecoveryCode = typeof user_account_recovery_codes.$inferSelect;
export type WebAuthnChallenge = typeof webauthn_challenges.$inferSelect;
export type Client = typeof clients.$inferSelect;
export type ClientNote = typeof client_notes.$inferSelect;
export type Template = typeof templates.$inferSelect;
export type RateLimitBucket = typeof rate_limit_buckets.$inferSelect;
export type InventoryItem = typeof inventory_items.$inferSelect;
export type ShoppingListItem = typeof shopping_list_items.$inferSelect;
export type AuthMagicLink = typeof auth_magic_links.$inferSelect;
