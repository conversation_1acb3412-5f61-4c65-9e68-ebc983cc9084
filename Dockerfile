# SvelteKit App Dockerfile
# Uses pnpm, adjust if using npm or yarn

# ---- Base ----
# Use an official Node.js Alpine image as a base.
# Alpine is a lightweight Linux distribution.
FROM node:20-alpine AS base
WORKDIR /app

# Enable pnpm via corepack
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# ---- Dependencies ----
# Install all dependencies (including devDependencies for building)
# This layer is cached if package.json/pnpm-lock.yaml don't change.
FROM base AS deps
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile --prod=false

# ---- Builder ----
# Build the SvelteKit application
FROM base AS builder
COPY --from=deps /app/node_modules /app/node_modules
COPY . .
# Your build script in package.json (e.g., "build": "svelte-kit build")
RUN pnpm build

# ---- Runner ----
# Create a lean production image
FROM node:20-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production

# Enable pnpm via corepack for installing production dependencies
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# Copy package.json and lockfile to install only production dependencies
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/pnpm-lock.yaml ./pnpm-lock.yaml
RUN pnpm install --prod --frozen-lockfile

# Copy the built application artifacts from the builder stage
# For @sveltejs/adapter-node, this is typically the 'build' directory
COPY --from=builder /app/build ./build

# Set the port the SvelteKit app will run on.
# Your SvelteKit adapter might also respect a PORT environment variable.
# Default for adapter-node output is often 3000.
ENV PORT=3000
EXPOSE 3000

ENV VITE_WEBAUTHN_ORIGIN=http://localhost:5173
# Command to run the SvelteKit app (output of @sveltejs/adapter-node)
# This runs the server entry point, usually build/index.js
CMD ["node", "build/index.js"] 