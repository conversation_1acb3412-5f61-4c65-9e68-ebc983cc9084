import { updateInventoryItemSchema } from '$lib/schemas/inventory';
import { db } from '$lib/server/db';
import { inventory_items } from '$lib/server/db/schema';
import { processImage } from '$lib/server/utils/imageProcessing';
import { error, fail, isRedirect, redirect } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params }) => {
	if (!locals.user) throw redirect(302, '/login');

	const itemId = params.id;
	const item = await db.query.inventory_items.findFirst({
		where: and(eq(inventory_items.id, itemId), eq(inventory_items.user_id, locals.user.id))
	});

	if (!item) {
		error(404, 'Item not found');
	}

	return { item };
};

export const actions: Actions = {
	update: async ({ request, locals, params }) => {
		if (!locals.user) return fail(401, { update: { errors: { root: ['Unauthorized'] } } });

		const itemId = params.id;
		const formData = Object.fromEntries(await request.formData());
		const validation = updateInventoryItemSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				update: { data: formData, errors: validation.error.flatten().fieldErrors }
			});
		}

		try {
			const values = validation.data;

			// Process image if provided, or handle image removal
			let processedImageBase64: string | null = values.image_base64 || null;
			if (values.image_base64 && values.image_base64.trim() !== '') {
				try {
					processedImageBase64 = await processImage(values.image_base64, {
						maxWidth: 1600,
						maxHeight: 1200,
						quality: 80,
						format: 'jpeg'
					});
				} catch (imageError) {
					console.error('Image processing error:', imageError);
					return fail(400, {
						update: { data: formData, errors: { image_base64: ['Failed to process image. Please try a different image.'] } }
					});
				}
			} else if (values.image_base64 === '') {
				// User explicitly removed the image
				processedImageBase64 = null;
			}

			await db
				.update(inventory_items)
				.set({
					...values,
					image_base64: processedImageBase64,
					updated_at: new Date()
				})
				.where(and(eq(inventory_items.id, itemId), eq(inventory_items.user_id, locals.user.id)));
		} catch (err) {
			if (isRedirect(err)) throw err;
			console.error(err);
			return fail(500, {
				update: { data: formData, errors: { root: ['Failed to update item.'] } }
			});
		}

		return { update: { success: true } };
	},

	delete: async ({ locals, params }) => {
		if (!locals.user) return fail(401, { delete: { errors: { root: ['Unauthorized'] } } });

		const itemId = params.id;

		try {
			await db
				.delete(inventory_items)
				.where(and(eq(inventory_items.id, itemId), eq(inventory_items.user_id, locals.user.id)));
		} catch (err) {
			if (isRedirect(err)) throw err;
			console.error(err);
			return fail(500, { delete: { errors: { root: ['Failed to delete item.'] } } });
		}

		throw redirect(302, '/app/inventory');
	}
};
