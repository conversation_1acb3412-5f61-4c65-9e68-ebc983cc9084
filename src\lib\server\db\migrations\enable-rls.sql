-- Enable Row Level Security (RLS) Migration
-- This migration enables RLS on all user-specific tables and creates appropriate policies

-- Enable RLS on all user-specific tables
ALTER TABLE inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE shopping_list_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE webauthn_credentials ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_verification_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_reset_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_account_recovery_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_magic_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE webauthn_challenges ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user-specific data
-- These policies ensure users can only access their own data

CREATE POLICY "user_inventory_policy" ON inventory_items
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_clients_policy" ON clients
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_shopping_list_policy" ON shopping_list_items
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_templates_policy" ON templates
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_client_notes_policy" ON client_notes
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

-- Auth-related tables policies
CREATE POLICY "user_sessions_policy" ON user_sessions
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_webauthn_credentials_policy" ON webauthn_credentials
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_email_verification_policy" ON email_verification_tokens
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_password_reset_policy" ON password_reset_tokens
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_recovery_codes_policy" ON user_account_recovery_codes
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_magic_links_policy" ON auth_magic_links
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

CREATE POLICY "user_webauthn_challenges_policy" ON webauthn_challenges
  FOR ALL USING (user_id = current_setting('app.current_user_id', true)::uuid);

-- Note: The 'users' table itself should NOT have RLS enabled as it's needed for authentication
-- The 'rate_limit_buckets' and 'expiring_rate_limit_buckets' tables also don't need RLS
-- as they are keyed by IP addresses or other non-user-specific identifiers
