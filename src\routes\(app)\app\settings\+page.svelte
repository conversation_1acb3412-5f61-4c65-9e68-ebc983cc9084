<!--
	@component
	This is the main settings page, organized into tabs. It uses modular components
	for each settings section to improve maintainability and organization.
-->
<script lang="ts">
	import { page } from '$app/state';
	import { onMount } from 'svelte';
	import IconCode from '~icons/icon-park-outline/code';
	import IconLink from '~icons/icon-park-outline/link-three';
	import IconLock from '~icons/icon-park-outline/lock';
	import IconUser from '~icons/icon-park-outline/user';

	import type { ActionData, PageData } from './$types';
	
	// Import settings modules
	import AccountsSettings from './components/AccountsSettings.svelte';
	import DeveloperSettings from './components/DeveloperSettings.svelte';
	import ProfileSettings from './components/ProfileSettings.svelte';
	import SecuritySettings from './components/SecuritySettings.svelte';

	let { data, form: actionData }: { data: PageData; form?: ActionData } = $props();

	const isDeveloperEnvironment = $derived(data.isDeveloperEnvironment);
	let activeTab = $state('profile');

	onMount(() => {
		const urlTab = page.url.searchParams.get('tab');
		if (urlTab && ['profile', 'security', 'accounts', 'developer'].includes(urlTab)) {
			activeTab = urlTab;
		}
	});
</script>

<!-- Settings Page -->
<div class="space-y-8">
	<!-- Page Header -->
	<div class="mb-8">
		<div class="flex items-center gap-3 mb-3">
			<div class="w-2 h-2 bg-primary/40 rounded-full"></div>
			<h1 class="text-2xl font-medium text-base-content/90">
				Settings
			</h1>
		</div>
		<p class="text-base-content/60 ml-5">
			Manage your account preferences and security
		</p>
	</div>

	<!-- DaisyUI Tabs -->
	<div class="tabs tabs-lift tabs-lg">
		<!-- Profile Tab -->
		<label class="tab">
			<input
				type="radio"
				name="settings_tabs"
				checked={activeTab === 'profile'}
				onchange={() => (activeTab = 'profile')}
			/>
			<IconUser class="h-4 w-4 me-2" />
			Profile
		</label>
		<div class="tab-content bg-base-100 border-base-300 p-8">
			<div class="flex items-center gap-3 mb-6">
				<IconUser class="h-5 w-5 text-primary" />
				<h2 class="text-xl font-semibold">Profile Settings</h2>
			</div>
			<ProfileSettings user={data.user} {actionData} />
		</div>

		<!-- Security Tab -->
		<label class="tab">
			<input
				type="radio"
				name="settings_tabs"
				checked={activeTab === 'security'}
				onchange={() => (activeTab = 'security')}
			/>
			<IconLock class="h-4 w-4 me-2" />
			Security
		</label>
		<div class="tab-content bg-base-100 border-base-300 p-8">
			<div class="flex items-center gap-3 mb-6">
				<IconLock class="h-5 w-5 text-primary" />
				<h2 class="text-xl font-semibold">Security Settings</h2>
			</div>
			<SecuritySettings {data} {actionData} />
		</div>

		<!-- Accounts Tab -->
		<label class="tab">
			<input
				type="radio"
				name="settings_tabs"
				checked={activeTab === 'accounts'}
				onchange={() => (activeTab = 'accounts')}
			/>
			<IconLink class="h-4 w-4 me-2" />
			Accounts
		</label>
		<div class="tab-content bg-base-100 border-base-300 p-8">
			<div class="flex items-center gap-3 mb-6">
				<IconLink class="h-5 w-5 text-primary" />
				<h2 class="text-xl font-semibold">Connected Accounts</h2>
			</div>
			<AccountsSettings {data} {actionData} />
		</div>

		<!-- Developer Tab (conditional) -->
		{#if isDeveloperEnvironment}
			<label class="tab">
				<input
					type="radio"
					name="settings_tabs"
					checked={activeTab === 'developer'}
					onchange={() => (activeTab = 'developer')}
				/>
				<IconCode class="h-4 w-4 me-2" />
				Developer
			</label>
			<div class="tab-content bg-base-100 border-base-300 p-8">
				<div class="flex items-center gap-3 mb-6">
					<IconCode class="h-5 w-5 text-primary" />
					<h2 class="text-xl font-semibold">Developer Settings</h2>
				</div>
				<DeveloperSettings />
			</div>
		{/if}
	</div>
</div>
