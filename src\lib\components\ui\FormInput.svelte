<!--
	@component
	Refined form input component that follows the coy/playful/refined aesthetic
-->
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		label: string;
		id: string;
		type?: string;
		placeholder?: string;
		value?: string | number;
		required?: boolean;
		disabled?: boolean;
		class?: string;
		error?: string;
		help?: string;
		children?: Snippet;
		options?: Snippet;
	}

	let {
		label,
		id,
		type = 'text',
		placeholder,
		value = '',
		required = false,
		disabled = false,
		class: className = '',
		error,
		help,
		children,
		options,
		...restProps
	}: Props = $props();
</script>

<div class="form-control">
	<label for={id} class="label">
		<span class="label-text font-medium text-base-content/90">
			{label}
			{#if required}
				<span class="text-error/70 ml-1">*</span>
			{/if}
		</span>
	</label>
	
	{#if children}
		{@render children()}
	{:else if type === 'textarea'}
		<textarea
			{id}
			class="textarea textarea-bordered transition-all duration-200 focus:border-primary/50 focus:ring-2 focus:ring-primary/10 {className}"
			{placeholder}
			{required}
			{disabled}
			{...restProps}
			bind:value
		></textarea>
	{:else if type === 'select'}
		<select
			{id}
			class="select select-bordered transition-all duration-200 focus:border-primary/50 focus:ring-2 focus:ring-primary/10 {className}"
			{required}
			{disabled}
			{...restProps}
			bind:value
		>
			<option disabled selected={!value}>Choose...</option>
			{#if options}
				{@render options()}
			{/if}
		</select>
	{:else}
		<input
			{id}
			{type}
			class="input input-bordered transition-all duration-200 focus:border-primary/50 focus:ring-2 focus:ring-primary/10 {className}"
			{placeholder}
			{required}
			{disabled}
			{...restProps}
			bind:value
		/>
	{/if}
	
	{#if help}
		<div class="label">
			<span class="label-text-alt text-base-content/60">{help}</span>
		</div>
	{/if}

	{#if error}
		<div class="label">
			<span class="label-text-alt text-error">{error}</span>
		</div>
	{/if}
</div>
