/**
 * Abstraction for the toast notification system.
 * This provides a simple functional API for showing notifications,
 * hiding the underlying store implementation from the components.
 */
import { TIMING } from '$lib/config';
import { toastStore } from '$lib/stores/toastStore';

type ToastOptions = {
	duration?: number;
};

function success(message: string, opts?: ToastOptions) {
	toastStore.success(message, opts?.duration);
}

function error(message: string, opts?: ToastOptions) {
	toastStore.error(message, opts?.duration);
}

function info(message: string, opts?: ToastOptions) {
	toastStore.info(message, opts?.duration);
}

function warning(message: string, opts?: ToastOptions) {
	toastStore.warning(message, opts?.duration);
}

function loading(message: string): number {
	return toastStore.loading(message);
}

function custom<T>(
	promise: Promise<T>,
	messages: {
		loading: string;
		success: string | ((data: T) => string);
		error: string | ((err: unknown) => string);
	}
) {
	const id = toastStore.loading(messages.loading);

	promise
		.then((data) => {
			const successMessage =
				typeof messages.success === 'function' ? messages.success(data) : messages.success;
			toastStore.update(id, { message: successMessage, type: 'success', duration: TIMING.TOAST_SUCCESS_DURATION });
		})
		.catch((err) => {
			const errorMessage =
				typeof messages.error === 'function' ? messages.error(err) : messages.error;
			toastStore.update(id, { message: errorMessage, type: 'error', duration: TIMING.TOAST_ERROR_DURATION });
		});
}

/**
 * A simple wrapper around the toastStore to provide easy access
 * to toast notification functions.
 */
export const toast = {
	success,
	error,
	info,
	warning,
	loading,
	custom
};
