import { relations } from "drizzle-orm/relations";
import { users, clients, clientNotes, templates, emailVerificationTokens, userAccountRecoveryCodes, webauthnChallenges, webauthnCredentials, userOauthAccounts, passwordResetTokens, userSessions } from "./schema";

export const clientsRelations = relations(clients, ({one, many}) => ({
	user: one(users, {
		fields: [clients.userId],
		references: [users.id]
	}),
	clientNotes: many(clientNotes),
}));

export const usersRelations = relations(users, ({many}) => ({
	clients: many(clients),
	clientNotes: many(clientNotes),
	emailVerificationTokens: many(emailVerificationTokens),
	userAccountRecoveryCodes: many(userAccountRecoveryCodes),
	webauthnChallenges: many(webauthnChallenges),
	webauthnCredentials: many(webauthnCredentials),
	userOauthAccounts: many(userOauthAccounts),
	templates: many(templates),
	passwordResetTokens: many(passwordResetTokens),
	userSessions: many(userSessions),
}));

export const clientNotesRelations = relations(clientNotes, ({one}) => ({
	client: one(clients, {
		fields: [clientNotes.clientId],
		references: [clients.id]
	}),
	user: one(users, {
		fields: [clientNotes.userId],
		references: [users.id]
	}),
	template: one(templates, {
		fields: [clientNotes.templateId],
		references: [templates.id]
	}),
}));

export const templatesRelations = relations(templates, ({one, many}) => ({
	clientNotes: many(clientNotes),
	user: one(users, {
		fields: [templates.userId],
		references: [users.id]
	}),
}));

export const emailVerificationTokensRelations = relations(emailVerificationTokens, ({one}) => ({
	user: one(users, {
		fields: [emailVerificationTokens.userId],
		references: [users.id]
	}),
}));

export const userAccountRecoveryCodesRelations = relations(userAccountRecoveryCodes, ({one}) => ({
	user: one(users, {
		fields: [userAccountRecoveryCodes.userId],
		references: [users.id]
	}),
}));

export const webauthnChallengesRelations = relations(webauthnChallenges, ({one}) => ({
	user: one(users, {
		fields: [webauthnChallenges.userId],
		references: [users.id]
	}),
}));

export const webauthnCredentialsRelations = relations(webauthnCredentials, ({one}) => ({
	user: one(users, {
		fields: [webauthnCredentials.userId],
		references: [users.id]
	}),
}));

export const userOauthAccountsRelations = relations(userOauthAccounts, ({one}) => ({
	user: one(users, {
		fields: [userOauthAccounts.userId],
		references: [users.id]
	}),
}));

export const passwordResetTokensRelations = relations(passwordResetTokens, ({one}) => ({
	user: one(users, {
		fields: [passwordResetTokens.userId],
		references: [users.id]
	}),
}));

export const userSessionsRelations = relations(userSessions, ({one}) => ({
	user: one(users, {
		fields: [userSessions.userId],
		references: [users.id]
	}),
}));