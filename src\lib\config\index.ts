/**
 * Centralized configuration exports
 * Single point of import for all configuration, constants, and types
 */

import type { ToastMessage } from '../types';
import { API, CURRENCY, DIMENSIONS, ENV, FEATURES, ROUTES, STORAGE_KEYS, TIMING, VALIDATION } from './app';
import { ERROR_MESSAGES, REGEX_PATTERNS } from './constants';

// Configuration exports
export * from './app';
export * from './constants';

// Type exports
export * from '../types';

// Re-export commonly used configurations with convenient names
export {
  API, CURRENCY, DIMENSIONS, ENV, FEATURES, ROUTES, STORAGE_KEYS, TIMING, VALIDATION
} from './app';

export {
  BUTTON_TEXTS, CONFIRMATION_MESSAGES, ERROR_MESSAGES, INFO_MESSAGES, KEYBOARD_SHORTCUTS, PLACEHOLDERS, REG<PERSON>_PATTERNS, SUCCESS_MESSAGES
} from './constants';

// Convenience re-exports for types
export type {
  ApiResponse, AppError, BarcodeScannerProps, ButtonProps,
  CardProps, Client, ClientFormData, ConfirmationConfig, ImageUploadConfig, ImageUploadProps, InventoryItem, InventoryItemFormData, LoginFormData, ModalProps, PaginatedResponse, QuickRestockItem,
  QuickRestockResponse, RegisterFormData, SearchResponse, ShoppingListItem,
  Template, ToastMessage,
  ToastOptions, UpdateModeSessionItem, User,
  UserPreferences, ValidationError
} from '../types';

// Utility functions for common operations
export const formatCurrency = (amountInCents: number, locale = CURRENCY.DEFAULT_LOCALE): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: CURRENCY.DEFAULT_CURRENCY,
  }).format(amountInCents / CURRENCY.CENTS_PER_DOLLAR);
};

export const parseCurrencyToCents = (value: string | number): number => {
  if (typeof value === 'number') return Math.round(value * CURRENCY.CENTS_PER_DOLLAR);
  const num = parseFloat(String(value).replace(/[^0-9.-]/g, ''));
  return isNaN(num) ? 0 : Math.round(num * CURRENCY.CENTS_PER_DOLLAR);
};

export const formatQuantity = (quantity: number): string => {
  return new Intl.NumberFormat().format(quantity);
};

export const getStorageKey = (key: keyof typeof STORAGE_KEYS): string => {
  return STORAGE_KEYS[key];
};

export const getRoute = (route: keyof typeof ROUTES, ...params: string[]): string => {
  const routeValue = ROUTES[route];
  if (typeof routeValue === 'function') {
    return (routeValue as (...args: string[]) => string)(...params);
  }
  return routeValue;
};

export const getApiEndpoint = (endpoint: keyof typeof API.ENDPOINTS): string => {
  return `${API.BASE_URL}${API.ENDPOINTS[endpoint]}`;
};

// Environment helpers
export const isDev = ENV.isDevelopment;
export const isProd = ENV.isProduction;
export const isClient = ENV.isClient;
export const isServer = ENV.isServer;

// Validation helpers
export const isValidEmail = (email: string): boolean => {
  return REGEX_PATTERNS.EMAIL.test(email);
};

export const isValidPhone = (phone: string): boolean => {
  return REGEX_PATTERNS.PHONE.test(phone);
};

export const isValidBarcode = (barcode: string): boolean => {
  return REGEX_PATTERNS.BARCODE.test(barcode);
};

export const isValidCurrency = (value: string): boolean => {
  return REGEX_PATTERNS.CURRENCY.test(value);
};

// Image validation helpers
export const isValidImageType = (type: string): boolean => {
  return (VALIDATION.ACCEPTED_IMAGE_TYPES as readonly string[]).includes(type);
};

export const isValidImageSize = (sizeInBytes: number, maxSizeKB = DIMENSIONS.MAX_IMAGE_SIZE_KB): boolean => {
  return sizeInBytes <= maxSizeKB * 1024;
};

export const isBase64Image = (value: string): boolean => {
  return REGEX_PATTERNS.BASE64_IMAGE.test(value);
};

// Quantity validation helpers
export const isValidQuantity = (quantity: number): boolean => {
  return quantity >= VALIDATION.MIN_QUANTITY && quantity <= VALIDATION.MAX_QUANTITY;
};

export const isValidReorderThreshold = (threshold: number): boolean => {
  return threshold >= VALIDATION.MIN_REORDER_THRESHOLD && threshold <= VALIDATION.MAX_REORDER_THRESHOLD;
};

// Status helpers
export const getQuantityStatus = (quantity: number, reorderThreshold?: number | null): 'success' | 'warning' | 'error' => {
  if (quantity === 0) return 'error';
  if (reorderThreshold !== null && reorderThreshold !== undefined && quantity <= reorderThreshold) return 'warning';
  return 'success';
};

export const getQuantityStatusClass = (quantity: number, reorderThreshold?: number | null): string => {
  const status = getQuantityStatus(quantity, reorderThreshold);
  switch (status) {
    case 'error': return 'status-dot-error';
    case 'warning': return 'status-dot-warning';
    case 'success': return 'status-dot-success';
    default: return 'status-dot-info';
  }
};

// Toast helpers
export const getToastDuration = (type: ToastMessage['type']): number => {
  switch (type) {
    case 'success': return TIMING.TOAST_SUCCESS_DURATION;
    case 'error': return TIMING.TOAST_ERROR_DURATION;
    case 'warning': return TIMING.TOAST_WARNING_DURATION;
    case 'info': return TIMING.TOAST_INFO_DURATION;
    case 'loading': return 0; // Loading toasts don't auto-dismiss
    default: return TIMING.TOAST_INFO_DURATION;
  }
};

// Animation helpers
export const getAnimationDuration = (type: 'fast' | 'normal' | 'slow' | 'modal' = 'normal'): number => {
  switch (type) {
    case 'fast': return TIMING.ANIMATION_FAST;
    case 'normal': return TIMING.ANIMATION_NORMAL;
    case 'slow': return TIMING.ANIMATION_SLOW;
    case 'modal': return TIMING.ANIMATION_MODAL;
    default: return TIMING.ANIMATION_NORMAL;
  }
};

// Error message helpers
export const getErrorMessage = (code: string): string => {
  // Map error codes to user-friendly messages
  const errorMap: Record<string, string> = {
    'UNAUTHORIZED': ERROR_MESSAGES.UNAUTHORIZED,
    'NOT_FOUND': ERROR_MESSAGES.GENERIC_ERROR,
    'VALIDATION_ERROR': ERROR_MESSAGES.GENERIC_ERROR,
    'NETWORK_ERROR': ERROR_MESSAGES.NETWORK_ERROR,
    'TIMEOUT_ERROR': ERROR_MESSAGES.TIMEOUT_ERROR,
  };
  
  return errorMap[code] || ERROR_MESSAGES.GENERIC_ERROR;
};

// Safe error message for production
export const getSafeErrorMessage = (errorMessage: string | undefined): string => {
  if (isDev) {
    return errorMessage || ERROR_MESSAGES.GENERIC_ERROR;
  }
  
  if (!errorMessage) return ERROR_MESSAGES.GENERIC_ERROR;
  
  // Check if error matches safe patterns
  for (const pattern of REGEX_PATTERNS.SAFE_ERROR_PATTERNS) {
    if (pattern.test(errorMessage)) {
      return errorMessage;
    }
  }
  
  return ERROR_MESSAGES.GENERIC_ERROR;
};

// Feature flag helpers
export const isFeatureEnabled = (feature: keyof typeof FEATURES): boolean => {
  return FEATURES[feature];
};

// Camera helpers
export const getCameraConstraints = (facingMode: 'user' | 'environment' = 'environment') => {
  return {
    video: {
      facingMode,
      width: { ideal: DIMENSIONS.CAMERA_WIDTH_IDEAL },
      height: { ideal: DIMENSIONS.CAMERA_HEIGHT_IDEAL }
    }
  };
};

// Debounce helper
export const debounce = <T extends (...args: never[]) => unknown>(
  func: T,
  delay: number = TIMING.INPUT_DEBOUNCE
): ((...args: Parameters<T>) => void) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Throttle helper
export const throttle = <T extends (...args: never[]) => unknown>(
  func: T,
  delay: number = TIMING.INPUT_DEBOUNCE
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};
